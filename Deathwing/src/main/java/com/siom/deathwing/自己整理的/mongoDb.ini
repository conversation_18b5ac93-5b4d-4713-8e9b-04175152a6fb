#1 nosql和关系型数据库的区别 什么情况下使用关系型数据库 什么情况下使用mongo
数据模型：
    rdbms：使用表格来存储数据，数据之间有明确的关系 。所有的表结构都是统一的，每个表的列和数据类型在设计的时候都需要定义好
    nosql：提供灵活的模型，支持存储各种数据模式，不要求事先定好数据结构，可以在不改变现有数据的情况下添加新的字段
扩展性：
    rdbms：常采用垂直扩展。通过增加更强大的配置已提升性能
    ##垂直扩展适合 强一致性、高事务性、复杂查询 的场景，如金融、银行、库存管理等。其扩展方式简单直接，但成本较高且受限于硬件性能
    nosql：水平扩展，添加机器
    ##水平扩展适合 高并发、大数据量、动态结构 的场景，如社交媒体、日志处理、电商等。其扩展方式更灵活，但需要额外考虑分布式架构的复杂性。
事务支持
    rdbms： 天然支持ACID
    nosql： 默认不支持或仅支持部分事务 因此，NoSQL 更适合那些对一致性要求不是特别严格的应用，特别是在数据吞吐量和高可用性方面的需求上
查询方式
    rbdms： 支持sql语言 可以进行复杂的多表关联
    nosql： 没有统一的查询语言 单标查询强大，多表查询不如rdbms
数据一致性
    rdbms：提供强一致性，适合需要事务保障
    nosql：大部分情况下保证数据的最终一致性，需要容忍一定程度上的数据不一致性的问题（为了提高高性能和可用性）
总结： 何时选择关系型数据库，何时选择 NoSQL
选择关系型数据库：
    数据结构固定、关系复杂。
    需要支持 ACID 事务和强一致性。
    需要执行复杂的 SQL 查询和多表联查。
    业务复杂度高，数据关联度强（如财务系统、传统ERP系统等）。
选择 NoSQL：
    数据结构多变且灵活，或者没有固定结构。
    高吞吐量、低延迟要求。
    支持大规模数据的水平扩展。
    对一致性要求不高（最终一致性可接受）。
    快速开发和频繁变化的需求。
#2 MongoDB中的数据库 文档 集合的关系
    数据库：类型mysql的数据库
    集合： 类似mysql的表
    文档： 每一行数据
#3 MongoDB中的数据结构
    bson ->json
#4 MongoDB中的索引介绍一下
    默认情况下会添加一个主键索引 _id ，
    可以自定义索引以提高性能
    单字段索引（）
        db.collection.createIndex({ name: 1 }) // 为 `name` 字段创建升序索引
        db.collection.createIndex({ age: -1 }) // 为 `age` 字段创建降序索引
    复合索引
        db.collection.createIndex({ name: 1, age: -1 }) // `name` 升序，`age` 降序
    多键索引
        db.collection.createIndex({ tags: 1 }) // `tags` 是数组字段
    全文索引
        db.collection.createIndex({ content: "text" })
    唯一索引 可以定义唯一索引保证数据的唯一性 （默认情况下，MongoDB 自动为 _id 字段创建一个唯一索引）
        db.collection.createIndex({ username: 1 }, { unique: true })
#5 MongoDB的数据备份和恢复
1. 使用 mongodump 和 mongorestore 工具
    mongodump 是 MongoDB 提供的一个命令行工具，用于从 MongoDB 数据库中导出数据并生成备份。它将数据库的内容导出到一个二进制格式的备份文件中
    mongorestore 是 MongoDB 提供的一个命令行工具，用于恢复使用 mongodump 工具备份的数据。
2. 使用文件系统备份
3. 使用 MongoDB Atlas（MongoDB 的托管云服务） 的备份功能
#6 MongoDB的分布式情况下读写分离和分片 分片后如何避免查询性能下降？
    MongoDB 通过 readPreference 参数来配置读操作的目标节点。常用的读偏好设置包括：
    primary（默认）：强制所有读操作都从主节点读取，保证强一致性。
    secondary：从副本集中的某个从节点读取，不保证强一致性，但可以减轻主节点的负担。
    secondaryPreferred：优先从从节点读取，如果所有从节点都不可用，则从主节点读取。
    primaryPreferred：优先从主节点读取，如果主节点不可用，则从从节点读取。
#7 如何定位和解决慢查询问题
1：启动慢查询日志  db.setProfilingLevel(1, { slowms: 100 })
        1 表示启用慢查询日志。
            slowms: 100 设置查询执行超过 100 毫秒的查询会被记录到日志中。你可以根据实际需要调整这个阈值。
        2：查看慢查询日志
            db.system.profile.find({ millis: { $gt: 100 } }).sort({ ts: -1 })
        3：查看慢查询配置 db.getProfilingStatus()（这会返回当前的 profiling level 和 慢查询阈值）
2：使用 explain() 分析查询 db.collection.find({ field: 'value' }).explain('executionStats')
3. 查看和优化索引
4. 优化查询
    避免全表扫描
    尽量限制返回的文档数量
5：在大数据量场景下，考虑使用分片来提升性能
#8 使用 MongoDB 进行全文搜索的实现原理是什么？
    MongoDB 的全文搜索是通过 文本索引（Text Index）和 倒排索引 来实现的，支持基于文档内容的快速搜索。通过合理使用 $text 操作符和配置，
    可以高效地查找包含特定词汇的文档。尽管 MongoDB 提供了全文搜索功能，但在处理大量数据时，仍然需要优化索引和查询，以确保高效性
#9 如何防止分布式环境中的数据丢失和不一致问题？
    数据备份、恢复 故障节点处理
#10 MongoDB 是如何保证多文档事务的 ACID 特性的？
    启动事务
        const session = client.startSession();
        session.startTransaction();
    执行操作
        try {
        collection.updateOne({ _id: 1 }, { $set: { status: 'processed' } }, { session });
        collection.insertOne({ _id: 2, name: 'new doc' }, { session });
        // 如果有问题，可以抛出错误，触发回滚
        } catch (e) {
        console.log("Error occurred:", e);
        }
    提交或回滚事务
        // 提交事务
        await session.commitTransaction();
        // 或者回滚事务
        await session.abortTransaction();
    完成事务
        session.endSession();

#11 在高并发场景下，MongoDB 如何优化写入性能？
    1: 批量写入：通过 bulkWrite 操作减少网络开销，提高并发写入性能。
    2：减少不必要的索引：避免多余的索引，减少写操作的性能开销。
    3：调整写入确认级别：根据业务需求合理调整 writeConcern，以提高写入性能
    4：磁盘和内存优化：优化磁盘 I/O 和内存配置，减少数据访问的延迟
    5：分片：通过分片技术实现数据的水平扩展，提高处理高并发写入的能力
MySQL 中有哪几种锁，列举一下？
    1: 按照颗粒度算（表锁 行锁 页锁）
    2：按照加锁机制（乐观锁，悲观锁）
    3: 按照兼容性（共享锁，排他锁）
mysql的四大特性 （四大特性靠redo log 和undo log来保证）
    A   原子性
    C   一致性
    I   隔离性
    D   持久性
分库分表会带来什么问题  ## TODO ** 参考小牧丁的分库操作
    事务的问题(普通事务用不了 得分布式事务)
        对强一致性要求高：选择 2PC、TCC 或分布式事务中间件。
        对性能要求高、允许最终一致性：选择消息队列方案。
    跨库 JOIN 问题
        简单关联：优先考虑应用层实现 JOIN 或全局表
        复杂查询：使用中间件或定期同步到汇总表。
    跨节点的 count,order by,group by 以及聚合函数问题
    数据迁移，容量规划，扩容等问题
    ID 问题
        使用雪花算法