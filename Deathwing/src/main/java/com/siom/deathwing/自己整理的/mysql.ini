1:什么是内连接、外连接、交叉连接、笛卡尔积呢？
    内连接（inner join） ：返回两个表中连接字段匹配的行 如果a表有b表没有 则数据不会返回
    外连接（outer join） : 不仅返回两个表中匹配的行，还返回左表、右表或两者中未匹配的行
    交叉连接（cross join）：返回第一个表中的每一行与第二个表中的每一行的组合，这种类型的连接通常用于生成笛卡尔积。
    笛卡尔积：数学中的一个概念，例如集合 A={a,b}，集合 B={0,1,2}，那么 A✖️B={<a,0>,<a,1>,<a,2>,<b,0>,<b,1>,<b,2>,}。
2:说一下数据库的三大范式？
  ①、第一范式：确保表的每一列都是不可分割的基本数据单元   (保证数据的原子性)
  ②、第二范式：要求表中的每一列都和主键直接相关，而不能只与主键的某一部分相关 (消除部分依赖，非主键字段完全依赖于主键)
  ③、第三范式：非主键列应该只依赖于主键列，不依赖于其他非主键列   (消除传递依赖，非主键字段只依赖于主键。)
3:varchar 与 char 的区别？
    1： char的长度是固定的，如果插入的数据长度不够char的长度 则以空格填充 最多能存放的字符个数为 255，因为长度固定，所以存取速度要
        比varchar块 速度能快50%左右（用空间换时间） 适用与长度固定的字符串
    2： varchar长度不是固定的。存储速度要比char慢很多（时间换空间）。最多能存放的字符个数为65532
DATETIME 和 TIMESTAMP 的异同？
    相同点：
        两个数据类型存储时间的表现格式一致。均为 YYYY-MM-DD HH:MM:SS
        两个数据类型都包含「日期」和「时间」部分。
        两个数据类型都可以存储微秒的小数秒（秒后 6 位小数秒）
    区别：
        日期范围：DATETIME 的日期范围是 1000-01-01 00:00:00.000000 到 9999-12-31 23:59:59.999999；TIMESTAMP的时间范围是1970-01-01 00:00:01.000000 UTC到2038-01-09 03:14:07.999999 UTC
        存储空间：DATETIME 的存储空间为 8 字节；TIMESTAMP 的存储空间为 4 字节
        时区相关：DATETIME 存储时间与时区无关；TIMESTAMP 存储时间与时区有关，显示的值也依赖于时区
        默认值：DATETIME 的默认值为 null；TIMESTAMP 的字段默认不为空(not null)，默认值为当前时间(CURRENT_TIMESTAMP)
in 和 exists 的区别？
    使用 IN：当子查询返回小结果集，且需要明确比较值时。
    使用 EXISTS：当子查询返回大结果集，且仅需判断存在性时。（仅判断子查询是否返回行，而不关心具体的值）
    在实际应用中，可以通过观察查询计划（EXPLAIN）选择更高效的方式。
记录货币用什么字段类型比较好？
    DECIMAL（ 设置精度） bigint（设置最小单位 比如 分毫厘）
drop、delete 与 truncate 的区别？
    区别	delete	        truncate	                drop
    类型	属于 DML	    属于 DDL	                属于 DDL
    回滚	可回滚	        不可回滚	                    不可回滚
    删除内容	表结构还在，     删除表的全部或者一部分数据行	表结构还在，删除表中的所有数据	从数据库中删除表，所有数据行，索引和权限也会被删除
    删除速度	删除速度慢，     需要逐行删除	删除速度快	    删除速度最快
    #在不再需要一张表的时候，用 drop；在想删除部分数据行时候，用 delete；在保留表而删除所有数据的时候用 truncate
UNION 与 UNION ALL 的区别？
    如果使用 UNION，会在表链接后筛选掉重复的记录行
    如果使用 UNION ALL，不会合并重复的记录行
count(1)、count(*) 与 count(列名) 的区别？
    count(1) 不会忽略null
    count(*) 不会忽略null
    count(列名) 会忽略列为null的 当列为主键的时候效率比count（1）快
SQL 查询语句的执行顺序了解吗？
    from on join where group by having select DISTINCT order by limit
MySQL 有哪些常见存储引擎？
    innerDb （支持事务，支持行锁，支持外键 ，默认存储引擎，必须有主键）
    MyISAM  （不支持事务 支持表锁，不支持外键，可以没有主键）
MySQL 日志文件有哪些？分别介绍下作用？
    1：错误日志（error log）记录mysql服务器启动运行或者停止出现的问题
    2：慢日志查询（slow query log） 默认情况下慢查询日志是关闭状态 需要打开 主要存贮执行时间超过long_query_time（可配）值的所有sql语句，用来识别和优化慢sql
    3：一般查询日志（general query log） 记录了所有mysql服务器的链接信息和sql语句 不管这些语句是否有修改
    4：二进制文件（binary log） 记录所有更改数据库的事件（insert update delete等）sql语句
    5：重做日志（redo log）记录了对于 InnoDB 表的每个写操作，不是 SQL 级别的，而是物理级别的，主要用于崩溃恢复。MySQL 中 InnoDB 存储引擎的一部分
        #Redo Log 记录的是数据变更操作，而不是执行的 SQL 查询本身。也就是说，它不记录具体的 SQL 语句，如 SELECT、UPDATE 等查询内容。
        #Redo Log 只会记录 已提交 事务的变更信息，对于未提交的事务，即使其修改了数据，也不会记录在 Redo Log 中。未提交的事务数据 会在崩溃恢复时丢失
    6：回滚日志（Undo Log，或者叫事务日志）：记录数据被修改前的值，用于事务的回滚
    Redo Log 与 Undo Log 的区别
        Redo Log记录事务提交之后的修改操作，主要用于崩溃恢复，确保已提交的事务能够恢复
        undo log 记录事务执行前的状态 ，主要用于事务回滚，确保事务撤销的时候数据能够撤销修改
    Redo Log 和 Binary Log 的区别
        Redo Log：
        操作数据后提交事务：Redo Log 只会记录已提交事务的数据修改操作。也就是说，只有当事务提交时，相关的操作才会被写入 Redo Log。
        记录数据页变更：Redo Log 存储的是 数据页的修改内容，包括事务的插入、更新和删除操作。在事务提交之前，即使操作已在内存中执行，Redo Log 中也不会记录这些操作。
        崩溃恢复：当数据库崩溃时，Redo Log 用于 重做（redo） 已提交事务的操作，确保已提交的数据不会丢失，即使数据库中的数据文件没有完全写入磁盘。
        Binary Log：
        所有数据修改操作（包括删除）都会记录：Binary Log 记录的是 所有的数据库修改操作，包括 INSERT、UPDATE、DELETE 等 SQL 语句，甚至是 表的删除、数据库的创建/删除 等操作。
        记录事件而非数据变化：Binary Log 记录的是执行的 SQL 操作或事件（例如 INSERT INTO、UPDATE、DELETE 等）。它不仅记录数据变更，还记录这些操作的顺序，甚至可以通过重放（replay）来恢复整个操作。
        用于复制和备份：Binary Log 是 MySQL 主从复制的基础，主数据库将所有操作通过 Binary Log 发送给从数据库，从而同步数据。它也支持 点-in-time 恢复，可以通过回放 Binary Log 恢复到某个特定时间点。
        总结：
        Redo Log 记录的是已提交事务的 数据修改操作，包括插入、更新、删除等。它在事务提交时写入，主要用于崩溃恢复和事务的持久性。
        Binary Log 记录的是所有数据库操作，包括插入、更新、删除，甚至是 表的删除 或 数据库的删除。它不仅用于数据恢复（点-in-time 恢复），还用于主从复制。
        所以，确实，Binary Log 会记录所有数据库操作（包括删除），而 Redo Log 只记录已提交事务的数据变更。
MVCC
    多版本并发控制 保证读写不冲突 但是写写还需要行锁保证数据
慢 SQL 怎么定位呢？（慢 SQL 也就是执行时间较长的 SQL 语句，MySQL 中 long_query_time 默认值是 10 秒，也就是执行时间超过 10 秒的 SQL 语句会被记录到慢查询日志中）
    可通过 show variables like 'long_query_time'; 查看当前的 long_query_time 值
SQL 的执行过程了解吗？
    1: 客户端发送sql语句给mysql服务器
    2：mysql服务器会查询缓存是否打开 如果打开查询缓存 命中直接返回（mysql8已经移除）
    3：分析器会校验sql语句的完整性和语法分析
    4：mysql通过优化器生成执行计划
    5：执行器调用存储引擎的接口，执行sql语句
    sql执行过程中，影响sql语句执行效率因素  io成本（数据量越大 差的越慢 所以避免select *）尽量分页查询
    cpu成本: 避免复杂的查询条件，尽量缩短计算成本（比如说添加索引） 提高排序效率
如何优化慢 SQL？
    首先要排查哪些是慢sql
        1: 查询慢sql日志
        2：show processlist 查询目前执行中时间较长的sql语句
    查找到慢sql之后查看sql的执行计划 explain
        字段名称	            含义
        id	                查询的执行顺序。值越大优先级越高，用于表示复杂查询中的步骤（如子查询、联合查询等）。
        select_type	        查询的类型，例如简单查询、子查询、联合查询等。
        table	            查询中涉及的表的名称。
        partitions	        表示分区表中使用的分区信息（如果有分区）。
        type	            连接类型，表示 MySQL 查找数据的方式。效率从高到低为：system > const > eq_ref > ref > range > index > ALL。
        possible_keys	    查询中可能会用到的索引。
        key	                查询实际使用的索引。
        key_len	            使用的索引长度（字节数），反映索引效率。
        ref	                显示索引的哪一列被用在查询条件中，以及与索引匹配的常量或字段。
        rows	            MySQL 估算查询中需要扫描的行数。
        filtered	        估算通过条件过滤的行的百分比。
        Extra	            额外信息，显示查询执行过程中使用的附加操作，如 Using index、Using temporary 等。
    最后根据执行计划的分析结果判断是否需要增删索引，调整查询语句来优化sql
        1： 避免不要要的列 尽量不使用select * 减少查询io次数 索引覆盖最快（如果查询的字段属于索引字段）
        2： 能分页尽量分页
        3： 优化索引，需要添加索引的地方尽量添加索引 增加查询效率 ，避免列上使用函数运算，正确使用联合索引等等
        4： join优化 优化子查询，小表驱动大表，适量的冗余字段，避免join太多的表
        5： 条件下推
        6： 正确使用联合索引 （查询的时候按照联合索引的顺序查询 应遵循最左前缀原则）
    创建索引的时候需要注意什么
        1：选择合适的字段作为索引
        2：创建联合索引的时候尽量注意索引的顺序
        3：索引不要创建过多影响维护效率
    索引在哪些情况下失效
        1：索引字段使用函数
        2：索引字段带like %xx
        3：但 WHERE 不满足最左前缀原则
    索引不适合哪些场景呢
        1：数据很少的情况没有必要添加索引
        2：在字段更新比较频繁的情况下不要创建索引，否则影响效率
    什么样的字段适合添加索引
        1：经常在where后面出现的字段
        2：经常使用join的字段
        3：经常出现在order by group by 后面的字段
回表了解吗？
    回表是指在数据库查询过程中，通过非聚簇索引（secondary index）查找到记录的主键值后，再根据这个主键值到聚簇索引（clustered index）中查找完整记录的过程。
    inner db 的索引是聚簇索引（叶子节点存储数据）
