jvm是什么
    java虚拟机
跨平台？
    是的 jvm会吧calss文件编译成平台的机器码并执行
jvm是java特有的吗
    不是
jvm的内存模型
    堆
        new出来的对象大部分都是在堆中 年轻代（伊甸园，from，to 8:1：1）- 老年代 gc的主要目标
    栈 线程私有 一个线程默认会创建一个栈帧 占用1M的空间 配置 -xss1m
        栈帧
            局部变量
            方法返回地址
            操作数栈
            动态链接
    方法区
        类的原数据 ，常量池 静态变量 java8之前叫永久代数据堆空间的范围 java8之后叫原空间 属于直接内存
    本地方法战
        保存本地方法的空间 native
    程序计数器
        保存程序执行的指针 多线程切换保证当前线程继续执行
哪些空间是线程私有的
    栈
    本地方法栈
    程序计数器
堆gc
    partial gc（部分收集器）
    full gc（正对收集器）

类加载需要几步
    加载 - 加载到内存
    验证 - 字节码文件的正确性和完整性
    准备 - 分配内存+初始值的设置
    解析 - 解析符号引用替换为直接引用的过程
    初始化 类的初始化 会调用static放的 不是对象的初始化
    使用
类加载器
    引导类加载器 - c++写的 加载核心jar jre
    扩展类加载器 -- java写的 加载ext包
    引用类加载器 -- 加载当前类的calssspath下的类
双亲委派机制
    类只加载一起，效率高
    安全 防止核心类库被篡改
垃圾回收算法
    标记-清除算法  效率不高 内存碎片多
    标记-复制算法 如果出现存活数量笔记哦啊多的时候 需要复制较多对象，成本上升，效率降低---老年代无法使用
    标记-整理算法
如果检测对象已变成垃圾？
    引用计数法（不能检测出循环引用）
    可达性分析算法
垃圾回收器分类
    串形回收器
        新生代串形回收器
        老年代串形回收器
    并行回收器
        新生代ParNew回收器
        新生代ParallerGC回收器
        老年代ParallerGC回收器
    cms
    g1
java8默认的垃圾回收器
    Parallel Scavenge GC --复制算法
    Paraller Old GC -- 标记整理算法
java9 有人G1
强引用
软引用
弱引用
虚引用

tomcat调优
    1: 禁用ajp
    2: 设置线程池
    3: 设置最大等待队列
    4: 设置nio2的运行模式
调整jvm参数
    1；设置并行垃圾回收器
    2: 调整年轻代大小
    3: 设置g1垃圾收集器



