9大内置对象
    handlerMapping(处理器 默认会有三种)
    HandlerAdapter - 调用handler的适配器，如果把handler当成工具的化 adapter相当于人
    HandlerExceptionResolver -- 异常处理
    ViewResolver -- 视图的解析
    localResolver -- 从请求中解析出locale 表示一个区域 比如zh-ch 对于不同的区域用户 现实不同的结果就是i18n
    multipartResolver -- 处理上传请求 将普通的请求封装成mutipartServletRequest
    themeResolver -- 主题解析 不同的ui css等
    flashMapManager 用于管理flashMap  flashMap 用来管理redirect重定向中传递参数
    requestToVeiwNameTranslator --
    mutipart - handlermapping - handleradapter - handerexception - view - requestoviewname - local - theme - flashmap
请求流程是什么
   [framerorkServlet]  doGet - > processRequest ->
   [distpatchServlet] doService -> doDispatch
    1:校验是否为上传请求，如果是上传请求 如果是则通过multipartResolver将其封装成MultipartHttpServletRequest对象
    2:设置请求的标识 （是否为上传）
    3:获取请求的HandlerExecutionChain（包含handler处理器和interceptors拦截器数组） 获取不到报错
    4:获取当前handler对应的adapter对象 用来执行对应的handler
    5: 处理lastModified（上次修改时间）判断是否需要从缓存中获取数据
    6:处理handlerExecutionChanin中所有的拦截器preHandle方法 如果返回false 则停止往下走
    7: adaptor执行真正的业务，并且返回modelAndVice对象
    8: 判断是否需要异步处理 如果需要异步处理 直接返回
    9: 不需要异步处理的话判断view对象是否为空 如果为空 则用reqeustToviewName处理器生成一个默认的view （视图解析）
    10:执行拦截器的后置处理
    11: 处理返回结果 包括异常处理，页面渲染 触发Interceptor的afterCompletion
    12:如果是上传请求 需要删除请求的资源


Springmvc是spring框架的一部分
Springmvc是如何跟spring以及tomcat做整合的
    启动tomcat容器
    加载用用程序的web.xml的配置文件
    当前配置文件进行解析
    listener


 web.xml -> spring.xml -> springmvc.xml

