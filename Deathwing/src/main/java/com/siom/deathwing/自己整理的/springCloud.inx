SOA和微服务的区别
    SOA是一种面向服务的架构模式，核心思想是将业务构功能拆成多个可复用的服务，通过ESB（企业服务总线）进行继承和通信
        强调复用
        集中式架构（ESB）：中心枢纽，所有服务通过ESB交互
        服务颗粒大：通常以完成的业务模块为单位，如订单服务
    微服务架构是一种更细粒度的服务拆分方式，每个服务都是独立的，并且可以独立部署和扩展
        去中心化：不依赖esb
        轻量级通信
        独立部署
        服务颗粒小
        多技术展
    什么时候用soa，什么时候用微服务
        适合soa的场景
            大型企业有多个系统（ERP，CRM）需要集成
            强调服务复用，不同应用可以调用相同的服务
            需要支持多种通信协议
        适合微服务的场景
            需要高并发，弹性伸缩
            不同业务模块可以独立升级扩展
            需要技术多样性
什么是devOps
    这其实不是个技术，是一种开发流程，它强调软件开发和it运维的紧密组合，已实现更快速，更频繁，更可靠的软件交付

微服务的概念
dobbo - spring cloud netflix - spring cloud alibaba 区别


Nacos 统一管理各种应用配置的基础服务组建
    CP+AP 默认AP
    Ap:高可用 即使部分没电挂了。仍可提供部分可用服务 针对大部分互联网行业
    CP：保证数据强一致性 金融银行可能会需要
 核心功能
    服务注册
    服务心跳
    服务同步
    服务发现
    服务健康检查
   数据模型
    namespace -- 默认空串 常用环境隔离/租户管理 多租户情况下
    公共命名空间 -- 默认public
    分组默认default_group
   配置格式
    text json yml xml html properties
openFeign
    使用：@feignClient（value=“order-server”，path=“/order”） 然后基于springmvc的注解来生命远程调用新鲜
    配置超时 #连接超时时间 connectTimeout：3000 #请求处理超时时间 readTimeout：3000
    服务与服务之间的request参数传递需要用拦截器 requestInterceptor
gateWay api网关，有效且统一的api路由管理，作为系统的统一入口 重要功能包括认证，鉴权，路由转发，安全策略，防刷，流量控制，监控日志
    注意 他不能为传统的servlet容器中工作 所以需要剔除掉web包
    核心概念
        路由
        断言 （匹配规则）断言为真 走路由规则
        过滤器
        过滤器工厂：可以给请求中添加参数头信息等

        网关过滤器/全局过滤器
    限流
        1:requestRateLimiter 基于redis+lua脚本实现限流
        2:gateway整合sentinel限流

sentinel
    流量控制
    熔断降级
    系统负载保护
    实时监控/控制台
    使用方法：
    1:代码硬编码（不推荐）
    2:aop注解配置（不推荐） 每一个接口都需要配置复杂参数和熔断降级接口
    3:整合springmvc动态配置 熔断降级用异常拦截器进行拦截处理
    openFeign整合sentinel
        feign的声明接口上添加fallback属性或则会fallbackFactory
seata
    分布式事务的应用场景：跨库事务，分库分表 微服务架构
    二段式提交
        事务管理器 TM
        资源管理器 RM
    Seata是一款致力于提供高性能和简单易用的分布式事务服务 事务模式有AT TCC SAGA XA
skywalking
