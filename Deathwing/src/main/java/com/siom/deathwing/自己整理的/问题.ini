为什么要使用mq
    异步
    解偶
    削峰
用了的好处缺点？
    好处 上面
    缺点：  系统可用性降低 偏偏加一个mq
           系统复杂性提高
           一致性问题
rabbitmq ： 吞吐量万 erlang开发的 延迟低 功能强大 ui好
如何保证消息的高可用？
    rabbit：集群（镜像集群模式）
消息丢失 消息可靠性传输？
如何保证队列消息顺序执行
    一个queue 对应一个消费者
消息队列积压
rabbit设置过期时间长时间没处理掉就没了？
    queue和consume翻10倍
如何设计一个队列？
    1: 支持可伸缩 快读扩容
    2: 数据罗盘
    3: 可用性 高可用
    4: 消息0丢失
es如何实现分布式的
es写入工作原理
es数据流很大的情况下如何提高查询效率
es生产集群部署的架构是蛇被呢 每个索引的数据流大概多少 每个索引大概有多少个分片
项目汇总缓存如何使用的，使用不当后果
为什么要用缓存 -高性能 高并发
redis 线程模型
redis数据类型与场景
为啥redis单线程效率也这么高
    纯内存操作
    单线程范儿避免了多线程频繁上下文切换问题
    非阻塞的io多路复用
redis过期策略
怎么保证redis高并发高可用
redis重启之后怎么恢复
redis cluster 集群？ 一致性哈希
redis并发竞争问题

dubbo的工作原理
注册中心挂了能继续通信吗
dubbo支持哪些序列化协议
dubbo负载均衡和高可用策略都有哪些
spi思想
基于dubbo进行服务治理，服务降级，失败重试，超时重试
分布式几口的幂等性如何设计
分布式服务接口的请求顺序如何保障
如何自己设计一个累死dubbo的rpc框架
如何设计一个高并发系统
    系统拆分 分库分表 多节点部署
    系统高可用 缓存系统   复杂均衡
    数据安全 - 分布式锁 幂等姓 ip防刷 分布式事务
    异步队列
    缓存系统 数据预热
    限流熔断
    分布式追踪与日志收集
    安全性设计 数据加密
    自动化监控与报警

arthas -- 线上jvm监控诊断利器
baomidu -- dynamic

