1: Nacos在切面类上使用@refreshScope注解，接口请求会走两次切面
    原因：@RefreshScope的作用是 允许bean在配置变更时动态刷新，从而应用最新的配置 本质上 它代理类bean 使得bean变成动态代理对象

    Aop+@RefreshScope 代理 = 多次执行 ，当一个切面类又被@ref代理时
        springAop 代理会创建一个cglib的代理来增强目标bean
        @refreshScope也会创建一个代理（用于动态刷新bean）
        这两个代理可能会相互嵌套，导致同一个切面逻辑被触发两次
2: openFeign在进行求头数据转发时，如果多个线程并发请求，相同的请求key的值会被拼接在一起，而不是线程隔离的
    这通常是由于feign使用类reqhuestinterceptor，并且requestTemplate.header的方法是append而不是覆盖，导致多个线程共享一同一个threadlocal或mdc中的值

    解决方案：每次请求先清空header 然后在设置（需要反射暴力清除）
3:
     //将端口绑定的处理器放入解码器的尾端
                            handlerServers.stream()
                                    //按照设定的排序
                                    .sorted((a, b) -> a.getSoft() - b.getSoft())
                                    .forEach(x -> {
                                        try {
                                            //测试判断此处需要校验 已经存在的解码器 需要添加必须是重新实例化的 否则会报错 所以不能用bean对象
                                            p.addLast(x.getClass().newInstance());
                                        } catch (Exception e) {
                                            log.error("实例化对象失败:【{}】", e.getMessage());
                                        }
                                    });