## 官网地址https://rocketmq.apache.org/zh/docs/introduction/02concepts
rocket 支持的消息类型
    顺序消息
    事务消息
    延迟消息
    普通消息
RocketMQ 优点
    单机吞吐量：10w级
    可用性：可用性高 分布式架构
    消息可靠性：经过配置可以做到消息0丢失
    消息堆积：做多可达到10亿的数据堆积，无需担心消息堆积带来的性能下降问题
    功能丰富，支持各种消息类型，很适合分布式事务支持
如何保证消息的可用性/可靠性/不丢失呢？
消息可能在哪些阶段丢失呢？可能会在这三个阶段发生丢失：生产阶段、存储阶段、消费阶段。
所以要从这三个阶段考虑：
    生产阶段：同步消息注意响应的状态是否成功，注意响应结果和异常，如果返回ok 则说明消息已经发送到了broker 如果异常 需要重试
    存储阶段：保证消息持久化到日志文件，即使broker宕机，恢复之后也会重新恢复
             主从模式保证高可用
    消费阶段：确保消息消费成功，不要一收到消息就ack 确保消息消费完成没有问题在ack
怎么处理消息积压？
    消费者扩容
    消息迁移 Queue 扩容
怎么实现分布式消息事务的？半消息？
    半消息：是指暂时还不能被 Consumer 消费的消息，Producer 成功发送到 Broker 端的消息，但是此消息被标记为 “暂不可投递” 状态，只有等 Producer 端执行完本地事务后经过二次确认了之后，Consumer 才能消费此条消息。

    依赖半消息，可以实现分布式消息事务，其中的关键在于二次确认以及消息回查：

    RocketMQ实现消息事务
    RocketMQ实现消息事务
    1、Producer 向 broker 发送半消息
    2、Producer 端收到响应，消息发送成功，此时消息是半消息，标记为 “不可投递” 状态，Consumer 消费不了。
    3、Producer 端执行本地事务。
    4、正常情况本地事务执行完成，Producer 向 Broker 发送 Commit/Rollback，如果是 Commit，Broker 端将半消息标记为正常消息，Consumer 可以消费，如果是 Rollback，Broker 丢弃此消息。
    5、异常情况，Broker 端迟迟等不到二次确认。在一定时间后，会查询所有的半消息，然后到 Producer 端查询半消息的执行情况。
    6、Producer 端查询本地事务的状态
    7、根据事务的状态提交 commit/rollback 到 broker 端。（5，6，7 是消息回查）
    8、消费者段消费到消息之后，执行本地事务。

