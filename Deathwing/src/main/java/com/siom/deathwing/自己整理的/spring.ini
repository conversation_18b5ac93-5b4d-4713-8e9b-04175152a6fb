bean的生命周期
    AbstractApplicationContext 的refresh方法
    0: 获取/创建beanFactory --obtainFreshBeanFactory（将需要初始化的bean对象都提前加载好，放到对象BeanDefinition）
    1: prepareBeanFactory对beanFactory做预处理（设置各种解析器，后置处理器，注册各种组建，忽略aware接口）等
    2: postProcessBeanFactory 默认是空实现(扩展用)
    3: invokeBeanFactoryPostProcessors --执行beanFactory的后置处理 调用各种beanFactory处理器bfpp
        BeanFactoryPostProcessor   -> postProcessBeanFactory 增强的是（beanFactory）
        BeanDefinitionRegistryPostProcessor  ->postProcessBeanDefinitionRegistry（增强的是BeanDefinitionRegistry）
        最主要的就是上面两个接口的实现都要执行 其中有一个很重要的类 ConfigrationClassPostProcessor 继承了BeanDefinitionRegistryPostProcessor 在这个方法里面 解析了很多注解 如@configration @compount 2import
        在解析@import的时候 会把importSelect接口的子类倒入的所有的类都放到一个map里面，解析完成之后对map进行下一步的解析，如果是springbooo项目，会倒入一个类（AutoConfigurationImportSelector）在进行对所有倒入类解析的时候 AutoConfigurationImportSelector会自动加载默认得配置文件路径spring.factory并添加到BeanDefiniti容器中，后续会对所有的BeanDefinition进行初始化操作
    4:  注册bean处理器 bpp
    5: 初始化为上下文初始化message源 --国际化
    6: 初始化事件监听多路广播器 监听器等
    7: 初始化剩下的单实例对象 getBean doGetBean createBean doCreateBean
    8: 完成bean的初始化工作，完成ioc容器的创建
bean的初始化执行顺序
    构造函数 -> @postConstruct（初始化调用） -> afterProPertiesSet（初始化调用 实现了initializingBean） -> init-method(初始化完成后)
ioc
    控制反转 将对象的增删改查和管理权利交给spring 容器-》一级缓存 二级缓存 三级缓存
aop
    aspect 切面 包含下面两个
    potintCut 切入点 具体的方法
    advice  通知 前置后置返回异常环绕
如何解决循环依赖问题的
    三级缓存 一级二级三级 一级缓存存放的是完整的bean对象 二级缓存放的是半成品对象（已经实例化没有初始化） 三级缓存存放的是lamda表达式 生成代理对象的方法（）
    正常情况下三级缓存没有不影响循环以来的问题 但是如果是要生成代理对象 则三级缓存必须要有 因为spring在bean对象生成的时候 对同时生成代理对象和普通对象 而三级缓存的作用是只会返回一个对象（要么代理对象要么普通对象） 如果没有三级缓存，那就乱套了
构造器注入的循环依赖有办法解决嘛
    @lazy
事务传播特性
    Required（默认传播特性） 如果有事务加入 没有则创建
    Required_new 有没有都新建
    Supports 有加入 没有就没有以非事务运行
    not_Supports 不加入事务 以非事务运行
    Manadtory 必须有事务 没有报错
    Never 没有事务 正常跑 有就报错
    Nested 嵌套事务
@Autuwired 和@resource的去被
    1:  @a默认是通过byType查找 type一样然后通过name查找
        @r相反
    2: @a在方法构造器属性上 @r在方法属性上 （方法值得是setter方法）
    3: @a是spring提供的自动注入的主机，只有spring容器会支持
        @r 是jdk提供的 所有的ioc容器都会支持这个注解 迁移到其他的ioc容器不会修改代码
    4: @a修饰的bean对象必须存在 不在在报错 @r不存在为null
@beanfactory和factoryBean的关系
    都是创建对象的 beanfactory是spring的顶层接口 通过beanFactory创建的bean对象肯定会走spring的整个创建流程
    factoryBean创建对象是由自己创建的通过getObject方法返回一个具体的bean对象
bean的初始化过程
    实例化- 设置属性值 - 调用对应的aware接口 - bfp前置处理 - initialingBean处理 - initMethod - bfp后置处理
springboot和spring的区别是什么
    springboot是在Spring基础上做了一个扩展 优化了sprong的开发
    1: 自动装配
    2: 内置tomcat
    3: 约定大于配置
springboot中 怎么让你的bean对象优先加载
    1: @dependOn
    2: bfpp中加载自己的bean对象
    @注意：@order只能控制同一个bean类中中的集合数据，不能控制不同的bean顺序 数字越小越优先
springboot优雅停机
    配置：server.shutdown=graceful 通过这个设置，当你停止服务时候，不再接收新的请求，并且服务器也不会直接立即关闭。而是等待进行的请求处理完
aop什么情况下失效 --
    非代理对象调用的方法（内部方法 this. 静态方法）
    私有方法
    静态方法
    final修饰的方法
    类内部调用的
    内部类方法调用
事务在什情况下失效
    如上⬆️
    rollbackfor设置错误
    catch捕获导致失效
什么是mvc
    model 模型 数据结构和逻辑规则
    view  视图 展示数据元素交互
    controller  处理逻辑  视图和模型的交互