Linux 常用命令
    top 用来查看系统资源
    ps -ef | grep java 查看 Java 进程
    netstat 查看网络连接
    ping 测试网络连通性
    find 查找文件
    chmod 修改文件权限
    kill 终止进程
    df 查看磁盘空间
    mkdir 创建目录、rm 删除文件、cp 复制文件、mv 移动文件
    zip 压缩文件、unzip 解压文件等等这些。
系统管理的命令有哪些？
    ps：显示当前运行的进程。ps aux显示所有进程。
    top：实时显示进程动态。
    kill：终止进程。kill -9 PID强制终止。
    df：显示磁盘空间使用情况。df -h以易读格式显示。
    du：显示目录或文件的磁盘使用情况。
    free：显示内存和交换空间的使用情况。
    chmod：更改文件或目录的权限。 (7=4+2+1 分别代表读、写、执行权限)
    chown：更改文件或目录的所有者和所属组。
如何查看Linux内存使用情况？
    free -h
网络管理的命令有哪些？
    ping：检查与远程服务器的连接。
    wget：从网络上下载文件。
    ifconfig：显示网络接口的配置信息。
    netstat：显示网络连接、路由表和网络接口信息。
压缩和解压的命令有哪些？
    tar：打包或解包.tar文件。tar cvf archive.tar files打包，tar xvf archive.tar解包。
    gzip / gunzip：压缩或解压.gz文件。
    zip / unzip：压缩或解压.zip文件。
查找文件的命令有哪些？
    find：在目录树中查找文件。find /directory/ -name filename
        例如：在当前目录及其子目录中查找名为 "example.txt" 的文件
        find . -name "example.txt"
        例如：查找 /home 目录中所有 .txt 结尾的文件：
        find /home -name "*.txt"
        例如：查找 /var/log 目录中修改时间在 7 天以前的 .log 文件
        find /var/log -name "*.log" -mtime +7