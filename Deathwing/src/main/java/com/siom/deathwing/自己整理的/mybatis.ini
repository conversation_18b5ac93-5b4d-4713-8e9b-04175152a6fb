常用实用类
    Reflector
    ReflectorFactory
    invoker方法
    MateClass
    MeteObject

mybatis中用到了那些设计模式
    代理模式
    单例模式    ErrorContext LogFactory
    装饰着模式
    工厂模式    **factory
    组合模式
    模板方法模式 executor ...
    责任链模式   interceptionChain
    适配器模式   日志模块使用是配置模式,包含了各个子包实现
    构造者模式
mybatis和spring如何整合的
    BFPP
    BFP
    sqlsessionFactory sqlsession configration 等bean对象的创建
    sqlsessionFactoryBean ---getObject方法创建sqlsession
    mapper文件的代理对象的创建
#{} 和${}的区别
    #{}是预编译处理 后则会是字符串替换 不会经过预处理（在order by create by 后面进行排序必须需要用$）
