package com.siom.deathwing.demo;

import jodd.util.MathUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Date 2024-11-29 14:09
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
public class LuShi {

    static List<kp> ls = new ArrayList<kp>();

    static {
        ls.add(new kp("01", 604, 0, 1090, 235000, 0, 11000));
        ls.add(new kp("02", 505, 0, 810, 500000, 0, 11000));
        ls.add(new kp("03", 702, 0, 1332, 200000, 0, 10000));
        ls.add(new kp("07", 640, 0, 1389, 0, 0, 4500));
        ls.add(new kp("07", 702, 0, 1332, 0, 0, 11000));
        ls.add(new kp("08", 540, 0, 1010, 120000, 0, 5000));
        ls.add(new kp("08", 812, 500, 1467, 425747, 3415, 11000));
        ls.add(new kp("09", 634, 286, 1200, 78000, 2800, 7500));
        ls.add(new kp("10", 746, 177, 1634, 300000, 14970, 8500));
        ls.add(new kp("10", 814, 440, 1548, 90000, 2500, 8500));
        ls.add(new kp("11", 846, 252, 1672, 370000, 110000, 8500));

        /**
         国服黄金卡背871橙 不重复634橙 78000粉尘 2800金币 大螺丝等氪金皮肤 一共134皮肤 113卡背 31钻卡 40异画橙 246金橙 质量超高
         */
    }


    public static void main(String[] args) {
        for (kp data : ls) {
            log.info("【{}】橙:【{}】金橙:【{}】粉尘总和:【{}】价格:【{}】性价比:【{}】",
                    data.getNo(),
                    data.getCk(),
                    data.getJCk(),
                    data.getCk() * 400 + data.getJCk() * 1600 + data.getFc() + data.getJb(),
                    data.getPrice(),
                    (data.getCk() * 400 + data.getJCk() * 1600 + data.getJb() + data.getFc()) / data.getPrice()
            );

        }
    }

    String dict = "D://testmain";
    static File file = new File("D://testmain");
    static Path filePath = Paths.get("D:\\testmain");

    public static void main3(String[] args) throws InterruptedException, IOException {
        while (true) {
            if (Files.exists(filePath)) {

            } else {
                try {
                    Path filePath1 = Files.createFile(filePath);
                    System.out.println("文件创建成功==========="+filePath1);
                    break;
                } catch (Exception e) {

                }
            }
        }

        System.out.println("执行业务==============需要3秒");
        Thread.sleep(3000);
        Files.delete(filePath);
        System.out.println("删除文件");
    }


    public static void main333(String[] args) throws Exception {
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                try {
                    main3(args);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }).start();
        }
        ;
    }

    public static void main6(String[] args) {
        String rootPath = LuShi. class.getClassLoader().getResource("").getPath();
        System.out.println("资源目录: " + rootPath);

    }
}
