package com.siom.deathwing.demo;

/**
 * <AUTHOR>
 * @Date 2024-03-28 9:32
 * @PackageName:com.siom.deathwing.demo
 * @ClassName: CompletableFutureExample
 * @Description: TODO
 * @Version 1.0
 */
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class CompletableFutureExample {

    public static void main2(String[] args) {
        // 创建一个异步操作
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            // 模拟长时间运行的任务
            try {
                TimeUnit.SECONDS.sleep(2); // 任务需要2秒钟完成
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "Hello, World!";
        });

        try {
            // 等待执行结果，最多等待3秒钟
            String result = future.get(3, TimeUnit.SECONDS);
            System.out.println(result);
        } catch (InterruptedException e) {
            // 当前线程在等待时被中断
            Thread.currentThread().interrupt();
            System.out.println("任务在等待结果时被中断了。");
        } catch (ExecutionException e) {
            // 异步操作计算抛出异常
            System.out.println("异步操作执行中出现了异常: " + e.getCause());

        } catch (TimeoutException e) {

            // 在指定时间内，如果结果还未准备好，则会抛出此异常
            System.out.println("等待超时，未能在指定时间内获取结果。");
        }
    }

    public static final String a="90";
    public static final DemoDto dto=new DemoDto("zs");

    public static void main(String[] args) {
        dto.setName("ls");
        System.out.println(dto);
    }
}

