package com.siom.deathwing.demo;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-05-13 15:20
 **/
import java.io.*;
import java.util.*;

public class NetworkUsageUtil {

    // 获取指定网卡当前流量（单位：字节）
    public static long[] getNetworkBytes(String iface) {
        try (BufferedReader reader = new BufferedReader(new FileReader("/proc/net/dev"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(iface)) {
                    String[] fields = line.trim().split("\\s+");
                    long receiveBytes = Long.parseLong(fields[1]);
                    long transmitBytes = Long.parseLong(fields[9]);
                    return new long[]{receiveBytes, transmitBytes};
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new long[]{0, 0};
    }

    // 简单演示带宽利用率计算
    public static void main(String[] args) throws InterruptedException {
        String iface = "eth0"; // 网卡名称，视你服务器而定
        long[] start = getNetworkBytes(iface);
        Thread.sleep(1000); // 1秒后再取一次
        long[] end = getNetworkBytes(iface);

        long receiveSpeed = end[0] - start[0]; // 1秒内接收字节数
        long transmitSpeed = end[1] - start[1]; // 发送字节数

        double totalBytesPerSec = receiveSpeed + transmitSpeed;
        double mbps = totalBytesPerSec * 8 / 1024 / 1024; // 转为 Mbps
        double bandwidthMbps = 1000; // 假设是千兆网卡
        double usage = (mbps / bandwidthMbps) * 100;

        System.out.printf("当前网速：%.2f Mbps，占用带宽：%.2f%%\n", mbps, usage);
    }
}
