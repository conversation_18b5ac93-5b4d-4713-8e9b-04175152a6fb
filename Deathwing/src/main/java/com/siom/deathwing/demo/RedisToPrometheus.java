package com.siom.deathwing.demo;

import io.prometheus.client.Counter;
import io.prometheus.client.exporter.PushGateway;

import java.util.Random;
import java.util.concurrent.TimeUnit;

public class RedisToPrometheus {
    // 创建一个固定的 Counter 实例
    static Counter counter = Counter.build()
            .name("callback_data")
            .help("This is a custom counter")
            .register();
    static Counter counter2 = Counter.build()
            .name("callback_data_2")
            .help("This is a custom counter")
            .register();

    public static void main(String[] args) throws Exception {
        // Pushgateway 地址
        String pushgatewayAddress = "127.0.0.1:9091";  // 修改为 PushGateway 实际地址
        PushGateway pushGateway = new PushGateway(pushgatewayAddress);

        // 创建一个随机数生成器
        Random random = new Random();
        // 不要每次都删除，除非你想强制重置计数器
         pushGateway.delete("callback_data");
         pushGateway.delete("callback_data_2");
        while (true) {
            // 假设每次都增加随机数，模拟计数器的变化
            double randomIncrement = random.nextDouble() * 10;  // 随机生成一个增量
            // 增加计数器的值
            counter.inc(randomIncrement);
            counter2.inc(50);
            // 推送数据到 Prometheus Pushgateway
            pushGateway.push(counter, "callback_data");
            pushGateway.push(counter2, "callback_data_2");
            System.out.println("Pushed value: " + counter.get());  // 打印当前计数器的值
            // 等待 1 秒再抓取一次
            TimeUnit.SECONDS.sleep(1);
        }
    }
}
