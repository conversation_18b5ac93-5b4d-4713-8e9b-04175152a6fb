package com.siom.deathwing.demo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023-10-23 11:45
 * @PackageName:com.bot.assembly.cat.demo
 * @ClassName: Demotest
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
public class Demotest {


    public static void main(String[] args) {
        Object[][] list = {
                {"x1", 445, 49, 294, 100000, 6000},
                {"x2", 348, 94, 464, 6800, 5700},
                {"x3", 726, 291, 1000, 55860, 14700},
                {"x4", 435, 291, 1300, 55860, 13000},
                {"x5", 345, 226, 1000, 240000, 13000},
                {"x6", 420, 95, 800, 60000, 3000},
                {"x7", 155, 10, 202, 15000, 750},
                {"x8", 500, 133, 800, 50000, 3500},
                {"x9", 160, 160, 300, 10000, 2200},
        };
        for (Object[] objects : list) {
            Integer sum = Integer.valueOf(objects[1].toString()) * 400 + Integer.valueOf(objects[2].toString()) * 1600 +
                    Integer.valueOf(objects[3].toString()) * 100 + Integer.valueOf(objects[4].toString());
            log.info("{}-橙【{}】张 金橙【{}】张 紫卡【{}】张 尘【{}】分解后总尘数【{}】价格【{}】性价比:【{}】",
                    objects[0],
                    objects[1],
                    objects[2],
                    objects[3],
                    objects[4],
                    sum,
                    objects[5],
                    (sum / Integer.valueOf(objects[5].toString()))
            );
        }
    }
    //
}
