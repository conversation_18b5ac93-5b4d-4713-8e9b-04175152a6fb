package com.siom.deathwing.demo;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;

import java.util.Collections;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2025-01-09 17:31
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */

public class aaaa {

    public static void main(String[] args) throws InterruptedException {
        SystemInfo si = new SystemInfo();

        // CPU 对象
        CentralProcessor processor = si.getHardware().getProcessor();

        // 获取初始 CPU 时间片
        long[] prevTicks = processor.getSystemCpuLoadTicks();

        // 等待 1 秒
        Thread.sleep(1000);

        // 获取 CPU 占用率
        double cpuLoad = processor.getSystemCpuLoadBetweenTicks(prevTicks) * 100;
        System.out.printf("CPU 占用率：%.2f%%\n", cpuLoad);

        // 获取内存对象
        GlobalMemory memory = si.getHardware().getMemory();

        long totalMem = memory.getTotal();
        long availableMem = memory.getAvailable();
        long usedMem = totalMem - availableMem;
        double memUsage = (usedMem * 1.0 / totalMem) * 100;

        System.out.printf("总内存：%.2f GB\n", totalMem / 1024.0 / 1024 / 1024);
        System.out.printf("已用内存：%.2f GB\n", usedMem / 1024.0 / 1024 / 1024);
        System.out.printf("内存使用率：%.2f%%\n", memUsage);
    }

    //查询kafka的分区数
//
//    public void findPartitionCount() {
//        try {
//            Properties props = new Properties();
//            props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServer);
//            try (AdminClient adminClient = AdminClient.create(props)) {
//                DescribeTopicsResult result = adminClient.describeTopics(Collections.singletonList("DC_US_CLICK_STREAM"));
//                TopicDescription description = result.values().get("DC_US_CLICK_STREAM").get();
//                int partitionCount = description.partitions().size();
//                System.out.println("分区数========: " + partitionCount);
//            }
//        }catch (Exception e) {
//        }
//
//    }



}
