package com.siom.deathwing.demo;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-05-15 11:05
 **/

import com.obs.services.ObsClient;
import com.obs.services.model.AccessControlList;
import com.obs.services.model.PutObjectRequest;
import com.obs.services.model.PutObjectResult;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class AppsFlyerCsvUploader {

    private static final String API_TOKEN = "your_api_token";
    private static final String APP_ID = "your_app_id";
    private static final String FROM = "2025-05-14";
    private static final String TO = "2025-05-14";
    private static final String APPSFLYER_URL = String.format(
            "https://hq1.appsflyer.com/api/raw-data/export/app/%s/installs_report/v5?api_token=%s&from=%s&to=%s",
            APP_ID, API_TOKEN, FROM, TO
    );

    private static final String OBS_ENDPOINT = "https://obs.ap-southeast-1.myhuaweicloud.com";
    private static final String ACCESS_KEY = "KGMV1GGCLWU5PZUYSONH";
    private static final String SECRET_KEY = "NAfxlIBzuMNrQZXuR1m6ec4itolLwHJZ19jcV3gL";
    private static final String BUCKET_NAME = "atlas-data";

    public static void main(String[] args) throws Exception {
        // 1. 下载 CSV 文件到本地
        File localFile = downloadCsv(APPSFLYER_URL);
       // File localFile = new File("D:\\test.xlsx");

        // 2. 构建 OBS 路径
        //String dateFolder = LocalDate.now().format(DateTimeFormatter.ISO_DATE);
        String dateFolder ="archiving/20250515";
        String objectKey = dateFolder + "/" + localFile.getName();

        // 3. 上传到 OBS
        uploadToObs(localFile, objectKey);

        // 4. 删除本地文件
        //Files.delete(localFile.toPath());
    }

    private static File downloadCsv(String downloadUrl) throws IOException {
        File tempFile = File.createTempFile("installs_report", ".csv");
        HttpURLConnection connection = (HttpURLConnection) new URL(downloadUrl).openConnection();
        connection.setRequestMethod("GET");

        try (InputStream in = connection.getInputStream();
             FileOutputStream out = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[8192];
            int len;
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        }

        return tempFile;
    }

    private static void uploadToObs(File file, String objectKey) throws IOException {
        ObsClient obsClient = new ObsClient(ACCESS_KEY, SECRET_KEY, OBS_ENDPOINT);
        try {
            PutObjectRequest request = new PutObjectRequest(BUCKET_NAME, objectKey, file);
            request.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);
            PutObjectResult putObjectResult = obsClient.putObject(request);
            String objectUrl = putObjectResult.getObjectUrl();
            System.out.println("Upload successful to: " + objectKey);
            System.out.println("Upload successful objectUrl: " + URLDecoder.decode(objectUrl));
        } finally {
            obsClient.close();
        }
    }
}

