package com.siom.deathwing.demo;

import java.io.IOException;
import java.net.Socket;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2024-07-24 17:04
 * @PackageName:com.siom.deathwing.demo
 * @ClassName: TcpPing
 * @Description: TODO
 * @Version 1.0
 */
public class TcpPing {

    public static void main2(String[] args) {
        String ip = "**************";
        List<Integer> ps = new ArrayList<>();
        for (int port = 0; port < 10000; port++) {
            try {
                new Socket(ip, port);
                ps.add(port);
                System.out.println("Successfully connected to " + ip + " on port " + port);
            } catch (IOException e) {
                System.out.println(port + " f");
            }
        }
        System.out.println(ps);
    }

    public static void main(String[] args) {
        System.out.println(URLEncoder.encode("http://zhyztest.xiaomuding.com"));
    }
}
