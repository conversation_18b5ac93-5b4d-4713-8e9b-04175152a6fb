package com.siom.deathwing.demo;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-12-11 14:58
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
public class LuShi2 {


    public static void main(String[] args) {
        System.out.println(new Date());
        System.out.println("互联网公司打杂的");
        main2(args);
    }


        public static void main2(String[] args) {
            int n = 20; // 控制心的大小（数字越大，心越大）

            // 打印心形的上半部分
            for (int i = n / 2; i <= n; i += 2) {
                // 打印左半部分的空格
                for (int j = 1; j < n - i; j += 2) {
                    System.out.print(" ");
                }
                // 打印左上部分的星号
                for (int j = 1; j <= i; j++) {
                    System.out.print("*");
                }
                // 打印中间的空格
                for (int j = 1; j <= n - i; j++) {
                    System.out.print(" ");
                }
                // 打印右上部分的星号
                for (int j = 1; j <= i; j++) {
                    System.out.print("*");
                }
                System.out.println();
            }

            // 打印心形的下半部分
            for (int i = n; i >= 1; i--) {
                // 打印左侧的空格
                for (int j = 0; j < n - i; j++) {
                    System.out.print(" ");
                }
                // 打印下部分的星号
                for (int j = 1; j <= (i * 2) - 1; j++) {
                    System.out.print("*");
                }
                System.out.println();
            }
        }


}
