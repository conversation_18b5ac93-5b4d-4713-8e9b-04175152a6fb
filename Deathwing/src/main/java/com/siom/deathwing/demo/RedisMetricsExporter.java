package com.siom.deathwing.demo;

import com.utils.UUIDUtils;
import io.prometheus.client.Gauge;
import redis.clients.jedis.Jedis;
import io.prometheus.client.exporter.HTTPServer;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-05-12 14:37
 **/
public class RedisMetricsExporter {
    private static final Gauge redisKeyGauge = Gauge.build()
            .name("RedisMetricsExporter")
            .help("The value of a Redis key.")
            .labelNames("key")
            .register();

    public static void main(String[] args) throws Exception {
        String redisKey = "simon_key";  // 你要监控的 Redis key
        //Jedis jedis = new Jedis("localhost"); // 连接到本地的 Redis 实例
        // 启动 Prometheus HTTP 服务器，监听 1234 端口
        HTTPServer server = new HTTPServer(6688);
        // 定期更新指标
        while (true) {
            try {
                // 获取 Redis 中 key 的值
                Double numericValue = Math.random();
                // 如果 Redis 中的值是数字，更新 Prometheus 指标
                if (numericValue != null) {
                    try {
                        // 转换为数值（这里假设 Redis 中的值是一个数字）
                        redisKeyGauge.labels(redisKey).set(numericValue);
                    } catch (NumberFormatException e) {
                        // 如果无法转换为数字，则跳过
                        System.err.println("Invalid number format for Redis key value: " + numericValue);
                    }
                }
            } catch (Exception e) {
                System.err.println("Error reading from Redis: " + e.getMessage());
            }
            // 每隔 10 秒更新一次
            Thread.sleep(10000);
        }
    }
}
