package com.siom.deathwing.utils;



import cn.hutool.extra.spring.SpringUtil;
import com.amazonaws.util.IOUtils;
import com.obs.services.ObsClient;
import com.obs.services.model.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.util.List;


/**
 * @program: atlas_oversea_micro_services
 * @description: TODO
 * @author: renBo
 * @create: 2025-05-15 11:33
 **/
@Slf4j
public class ObsUtils {


    private static ObsClient obsClient = SpringUtil.getBean(ObsClient.class);

    /**
     @Value("${obs.accessKey:KGMV1GGCLWU5PZUYSONH}")
     private String accessKey;
     @Value("${obs.secretKey:NAfxlIBzuMNrQZXuR1m6ec4itolLwHJZ19jcV3gL}")
     private String secretKey;

     @Value("${obs.endPoint:https://obs.ap-southeast-1.myhuaweicloud.com}")
     private String endPoint;

     @Bean
     public ObsClient obsClient() {
     ObsClient obsClient = new ObsClient(accessKey, secretKey, endPoint);
     return obsClient;
     }
     */

    /**
     * 上传文件
     */
    public static String uploadToObs(String bucketName, String objectKey, File file) {
        try {
            PutObjectRequest request = new PutObjectRequest(bucketName, objectKey, file);
            request.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);
            PutObjectResult putObjectResult = obsClient.putObject(request);
            if (putObjectResult != null) {
                return URLDecoder.decode(putObjectResult.getObjectUrl());
            }
            throw new RuntimeException("Obs uploadFile is error");
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 功能描述: 连接obs并从obs中下载文件
     *
     * <AUTHOR>
     * @date 2025/5/15 17:43
     */
    private static String uploadToObs(String bucketName, String objectKey, InputStream inputStream) {
        try {
            PutObjectRequest request = new PutObjectRequest();
            request.setBucketName(bucketName);
            request.setObjectKey(objectKey);
            request.setInput(inputStream);
            // 设置对象访问权限为公共读
            request.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);
            PutObjectResult putObjectResult = obsClient.putObject(request);
            if (putObjectResult != null) {
                return URLDecoder.decode(putObjectResult.getObjectUrl());
            }
            throw new RuntimeException("Obs uploadFile is error");
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 功能描述: 连接obs并从obs中下载文件
     *
     * <AUTHOR>
     * @date 2025/5/15 17:43
     */
    public static byte[] downloadFile(String objectKey, String bucketName) {
        InputStream inputStream = null;
        try {
            ObsObject obsObject = obsClient.getObject(bucketName, objectKey);
            inputStream = obsObject.getObjectContent();
            return IOUtils.toByteArray(inputStream);
        } catch (Exception e) {
            log.error("Obs文件下载失败:{}", e.getMessage());
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return new byte[0];
    }

    /**
     *  下载文件
     */
    public static void downloadFile(String objectKey, String bucketName, OutputStream outputStream) {
        InputStream inputStream = null;
        try {
            ObsObject obsObject = obsClient.getObject(bucketName, objectKey);
            inputStream = obsObject.getObjectContent();
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.error("Obs downloadFile is error:{}", e.getMessage());
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除上传导出文件
     *
     * @param urlList
     * @return
     */
    public static boolean deleteFile(List<String> urlList, String bucketName) {
        // 创建ObsClient实例
        DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(bucketName);
        deleteRequest.setQuiet(true);
        for (String url : urlList) {
            deleteRequest.addKeyAndVersion(url);
        }
        DeleteObjectsResult deleteResult = obsClient.deleteObjects(deleteRequest);
        return deleteResult.getErrorResults().size() == 0;

    }

}
