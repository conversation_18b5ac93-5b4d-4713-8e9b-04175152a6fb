package com.siom.deathwing.designModel.观察者模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:19
 * @PackageName:com.bot.cat.common.设计模式.观察者模式
 * @ClassName: CurrentConditionsDisplay
 * @Description: TODO
 * @Version 1.0
 */
// 具体观察者
class CurrentConditionsDisplay implements Observer {
    private float temperature;
    private float humidity;

    @Override
    public void update(float temp, float humidity, float pressure) {
        this.temperature = temp;
        this.humidity = humidity;
        display();
    }

    public void display() {
        System.out.println("Current conditions: " + temperature
                + "F degrees and " + humidity + "% humidity");
    }
}

