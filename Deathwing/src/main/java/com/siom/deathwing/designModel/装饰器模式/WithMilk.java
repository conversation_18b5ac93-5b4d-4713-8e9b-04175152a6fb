package com.siom.deathwing.designModel.装饰器模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:31
 * @PackageName:com.bot.cat.common.设计模式.装饰器模式
 * @ClassName: WithMilk
 * @Description: TODO
 * @Version 1.0
 */
// 具体装饰类
class WithMilk extends CoffeeDecorator {
    public WithMilk(Coffee coffee) {
        super(coffee);
    }

    @Override
    public double cost() {
        return decoratedCoffee.cost() + 0.5;
    }

    @Override
    public String description() {
        return decoratedCoffee.description() + ", with milk";
    }
}
