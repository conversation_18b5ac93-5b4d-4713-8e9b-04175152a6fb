package com.siom.deathwing.designModel.策略模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 16:40
 * @PackageName:com.bot.cat.common.设计模式.策略魔兽
 * @ClassName: ShoppingCart
 * @Description: TODO
 * @Version 1.0
 */
class ShoppingCart {
    private PaymentStrategy paymentStrategy;

    // Set the payment strategy
    public void setPaymentStrategy(PaymentStrategy strategy) {
        this.paymentStrategy = strategy;
    }

    public void checkout(int amount) {
        paymentStrategy.pay(amount);
    }
}
