package com.siom.deathwing.designModel.状态模式.第二个;

import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024-02-26 16:32
 * @PackageName:com.bot.cat.common.设计模式.状态模式.第二个
 * @ClassName: BussiessHandler
 * @Description: TODO
 * @Version 1.0
 */
@ToString
public class BusHandler {

    @Setter
    private BusStatus busStatus;


    public static BusHandler init() {
        BusHandler bus = new BusHandler();
        bus.setBusStatus(new BusStatus_1());
        return bus;
    }


    /**
     * 业务处理 其实是每个不同状态的不同处理 业务处理= 状态处理 每个状态处理是不一样的
     */
    public Object doHander() {
        return busStatus.handler(this);
    }


}
