package com.siom.deathwing.designModel.组合模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:06
 * @PackageName:com.bot.cat.common.设计模式.组合模式
 * @ClassName: Graphic
 * @Description: TODO
 * @Version 1.0
 */
// 组件接口
abstract class Graphic {
    public void add(Graphic graphic) {
        throw new UnsupportedOperationException();
    }

    public void remove(Graphic graphic) {
        throw new UnsupportedOperationException();
    }

    public abstract void draw();
}
