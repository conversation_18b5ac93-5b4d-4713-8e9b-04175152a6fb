package com.siom.deathwing.designModel.策略模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 16:39
 * @PackageName:com.bot.cat.common.设计模式.策略魔兽
 * @ClassName: CreditCardStrategy
 * @Description: TODO
 * @Version 1.0
 */
// 具体策略类：信用卡支付
class CreditCardStrategy implements PaymentStrategy {
    private String name;
    private String cardNumber;

    public CreditCardStrategy(String nm, String ccNum) {
        this.name = nm;
        this.cardNumber = ccNum;
    }

    @Override
    public void pay(int amount) {
        System.out.println(amount + " paid with credit/debit card");
    }
}
