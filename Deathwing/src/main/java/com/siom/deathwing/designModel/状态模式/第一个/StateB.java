package com.siom.deathwing.designModel.状态模式.第一个;

/**
 * <AUTHOR>
 * @Date 2024-02-26 16:15
 * @PackageName:com.bot.cat.common.设计模式.状态模式
 * @ClassName: StateB
 * @Description: TODO
 * @Version 1.0
 */
public class StateB implements StatusIn {
    @Override
    public void handle(Context context) {
        System.out.println("State B is handling the request.");
        context.setState(new StateA());
    }
}
