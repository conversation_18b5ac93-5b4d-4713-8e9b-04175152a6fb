package com.siom.deathwing.designModel.适配器模式.s322;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:43
 * @PackageName:com.bot.cat.common.设计模式.适配器模式.s322
 * @ClassName: P2TAdapter
 * @Description: TODO
 * @Version 1.0
 */
public class P2TAdapter implements fruit {
    private potato potato;

    public P2TAdapter(potato potato) {
        this.potato = potato;
    }

    @Override
    public void eatFruit() {
        potato.eatPotato();
        System.out.println("开始吃。。。水果");
    }
}
