package com.siom.deathwing.designModel.命令模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 16:49
 * @PackageName:com.bot.cat.common.设计模式.命令模式
 * @ClassName: CommandPatternDemo
 * @Description: TODO
 * @Version 1.0
 */
// 客户端代码
public class CommandPatternDemo {
    public static void main(String[] args) {
        Light light = new Light();
        Command lightOn = new LightOnCommand(light);
        Command lightOff = new LightOffCommand(light);

        RemoteControl remote = new RemoteControl();
        remote.setCommand(lightOn);
        remote.pressButton(); // Light is ON

        remote.setCommand(lightOff);
        remote.pressButton(); // Light is OFF
    }
}
