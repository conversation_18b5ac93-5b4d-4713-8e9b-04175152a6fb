package com.siom.deathwing.designModel.状态模式.第二个;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024-02-26 16:33
 * @PackageName:com.bot.cat.common.设计模式.状态模式.第二个
 * @ClassName: BusStatus_1
 * @Description: 状态1
 * @Version 1.0
 */
@Slf4j
public class BusStatus_3 implements BusStatus {
    @Override
    public Object handler(BusHandler busHandler) {
        log.info("这是第3个状态 当前业务：{}", busHandler);
        log.info("当前状态是最终状态 无法再往下走状态");
        return null;
    }
}
