package com.siom.deathwing.designModel.装饰器模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:30
 * @PackageName:com.bot.cat.common.设计模式.装饰器模式
 * @ClassName: CoffeeDecorator
 * @Description: TODO
 * @Version 1.0
 */
// 装饰类
abstract class CoffeeDecorator implements Coffee {
    protected Coffee decoratedCoffee;

    public CoffeeDecorator(Coffee coffee) {
        this.decoratedCoffee = coffee;
    }

    public double cost() {
        return decoratedCoffee.cost();
    }

    public String description() {
        return decoratedCoffee.description();
    }
}
