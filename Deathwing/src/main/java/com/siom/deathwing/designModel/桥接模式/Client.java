package com.siom.deathwing.designModel.桥接模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:52
 * @PackageName:com.bot.cat.common.设计模式.桥接模式
 * @ClassName: Client
 * @Description: TODO
 * @Version 1.0
 */
public class Client {
    public static void main(String[] args) {
        Shape redCircle = new Circle(new Red());
        Shape blueCircle = new Circle(new Blue());

        redCircle.draw(); // 输出: Draw circle with Color is Red
        blueCircle.draw(); // 输出: Draw circle with Color is Blue
    }
}
