package com.siom.deathwing.designModel.组合模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:07
 * @PackageName:com.bot.cat.common.设计模式.组合模式
 * @ClassName: Client
 * @Description: TODO
 * @Version 1.0
 */
// 客户端代码
public class Client {
    public static void main(String[] args) {
        // 初始化四个叶节点
        Graphic circle1 = new Circle();
        Graphic circle2 = new Circle();
        Graphic rectangle = new Rectangle();
        // 创建一个组合图形，并添加叶节点
        CompositeGraphic graphic = new CompositeGraphic();
        graphic.add(circle1);
        graphic.add(circle2);
        graphic.add(rectangle);
        // 绘制组合图形
        graphic.draw(); // 输出: Draw a Circle, Draw a Circle, Draw a Rectangle
    }
}
