package com.siom.deathwing.designModel.状态模式.第一个;

/**
 * <AUTHOR>
 * @Date 2024-02-26 16:16
 * @PackageName:com.bot.cat.common.设计模式.状态模式
 * @ClassName: StatuTest
 * @Description: TODO
 * @Version 1.0
 */
public class StatuTest {
    // 主类
    public static void main(String[] args) {
        Context context = new Context();
        context.request(); // 输出：State A is handling the request.
        context.request(); // 输出：State B is handling the request.
    }
}
