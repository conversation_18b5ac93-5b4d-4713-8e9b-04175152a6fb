package com.siom.deathwing.designModel.适配器模式.shi1;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:33
 * @PackageName:com.bot.cat.common.设计模式.适配器模式
 * @ClassName: Client
 * @Description: TODO
 * @Version 1.0
 */
// 客户端代码
public class Client {
    public static void main(String[] args) {
        MicroUSBPort microUSBDevice = new MicroUSBPort();
        LightningPort lightningAdapter = new MicroUSBToLightningAdapter(microUSBDevice);
        lightningAdapter.chargeWithLightning(); // 输出：Charging with Micro USB
    }
}