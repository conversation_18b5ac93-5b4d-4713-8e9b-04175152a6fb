package com.siom.deathwing.designModel.原型模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:28
 * @PackageName:com.bot.cat.common.设计模式.原型模式
 * @ClassName: ConcretePrototypeTest
 * @Description: TODO
 * @Version 1.0
 */
public class ConcretePrototypeTest {

    public static void main(String[] args) {
        ConcretePrototype original = new ConcretePrototype("Value2");
        try {
            ConcretePrototype cloned = (ConcretePrototype) original.clone();
            System.out.println(cloned.getField()); // 输出：Value
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
    }
}
