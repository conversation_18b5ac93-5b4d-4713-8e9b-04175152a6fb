package com.siom.deathwing.designModel.策略模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 16:40
 * @PackageName:com.bot.cat.common.设计模式.策略魔兽
 * @ClassName: StrategyPatternDemo
 * @Description: TODO
 * @Version 1.0
 */
// 客户端代码
public class StrategyPatternDemo {
    public static void main(String[] args) {
        ShoppingCart cart = new ShoppingCart();
        // 使用信用卡支付
        cart.setPaymentStrategy(new CreditCardStrategy("<PERSON> Do<PERSON>", "1234567890123456"));
        cart.checkout(100);
        // 使用PayPal支付
        cart.setPaymentStrategy(new PaypalStrategy("<EMAIL>", "mypwd"));
        cart.checkout(200);
    }
}
