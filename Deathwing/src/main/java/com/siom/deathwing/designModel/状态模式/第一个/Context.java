package com.siom.deathwing.designModel.状态模式.第一个;

/**
 * <AUTHOR>
 * @Date 2024-02-26 16:14
 * @PackageName:com.bot.cat.common.设计模式.状态模式
 * @ClassName: Context
 * @Description: TODO
 * @Version 1.0
 */
public class Context {
    private StatusIn state;

    public Context() {
        // 初始状态为 StateA
        this.state = new StateA();
    }

    public void setState(StatusIn state) {
        this.state = state;
    }

    public void request() {
        state.handle(this);
    }
}
