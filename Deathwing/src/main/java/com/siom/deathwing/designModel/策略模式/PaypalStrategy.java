package com.siom.deathwing.designModel.策略模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 16:39
 * @PackageName:com.bot.cat.common.设计模式.策略魔兽
 * @ClassName: PaypalStrategy
 * @Description: TODO
 * @Version 1.0
 */
class PaypalStrategy implements PaymentStrategy {
    private String emailId;
    private String password;

    public PaypalStrategy(String email, String pwd) {
        this.emailId = email;
        this.password = pwd;
    }

    @Override
    public void pay(int amount) {
        System.out.println(amount + " paid using PayPal.");
    }
}
