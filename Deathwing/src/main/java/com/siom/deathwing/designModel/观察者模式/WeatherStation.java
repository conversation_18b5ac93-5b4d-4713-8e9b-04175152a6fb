package com.siom.deathwing.designModel.观察者模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:19
 * @PackageName:com.bot.cat.common.设计模式.观察者模式
 * @ClassName: WeatherStation
 * @Description: TODO
 * @Version 1.0
 */
// 客户端代码
public class WeatherStation {
    public static void main(String[] args) {
        WeatherData weatherData = new WeatherData();
        CurrentConditionsDisplay currentDisplay = new CurrentConditionsDisplay();

        weatherData.registerObserver(currentDisplay);

        weatherData.setMeasurements(80, 65, 30.4f);
        weatherData.setMeasurements(82, 70, 29.2f);
    }
}
