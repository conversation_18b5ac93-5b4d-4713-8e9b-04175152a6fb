package com.siom.deathwing.designModel.桥接模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:51
 * @PackageName:com.bot.cat.common.设计模式.桥接模式
 * @ClassName: Circle
 * @Description: TODO
 * @Version 1.0
 */
// 细化抽象
class Circle extends Shape {
    public Circle(Color color) {
        super(color);
    }

    @Override
    public void draw() {
        System.out.println("Draw circle with " + color.fill());
    }
}
