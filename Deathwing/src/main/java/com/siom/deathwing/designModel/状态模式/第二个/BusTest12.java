package com.siom.deathwing.designModel.状态模式.第二个;

/**
 * <AUTHOR>
 * @Date 2024-02-26 16:38
 * @PackageName:com.bot.cat.common.设计模式.状态模式.第二个
 * @ClassName: BusTest12
 * @Description: TODO
 * @Version 1.0
 */
public class BusTest12 {

    public static void main(String[] args) {
        BusHandler init = BusHandler.init();
        init.doHander();
        init.doHander();
        init.doHander();
        init.doHander();
    }
}
