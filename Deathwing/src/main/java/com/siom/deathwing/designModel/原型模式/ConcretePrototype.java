
package com.siom.deathwing.designModel.原型模式;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:27
 * @PackageName:com.bot.cat.common.设计模式.原型模式
 * @ClassName: ConcretePrototype
 * @Description: TODO
 * @Version 1.0
 */
// 具体原型类
    @Data
public class ConcretePrototype implements com.siom.deathwing.designModel.原型模式.Prototype {
    private String field;

    public ConcretePrototype(String field) {
        this.field = field;
    }

    // 浅复制方法
    @Override
    public Prototype clone() throws CloneNotSupportedException {
        return (Prototype) super.clone();
    }

    public String getField() {
        return field;
    }
}
