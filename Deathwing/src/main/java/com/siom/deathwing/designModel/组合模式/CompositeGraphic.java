package com.siom.deathwing.designModel.组合模式;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:06
 * @PackageName:com.bot.cat.common.设计模式.组合模式
 * @ClassName: CompositeGraphic
 * @Description: TODO
 * @Version 1.0
 */
class CompositeGraphic extends Graphic {
    private List<Graphic> children = new ArrayList<>();

    @Override
    public void add(Graphic graphic) {
        children.add(graphic);
    }

    @Override
    public void remove(Graphic graphic) {
        children.remove(graphic);
    }

    @Override
    public void draw() {
        for (Graphic graphic : children) {
            graphic.draw(); // 调用每个子对象的绘制方法
        }
    }
}
