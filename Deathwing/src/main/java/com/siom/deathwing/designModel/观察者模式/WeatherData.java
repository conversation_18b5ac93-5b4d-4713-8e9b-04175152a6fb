package com.siom.deathwing.designModel.观察者模式;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:18
 * @PackageName:com.bot.cat.common.设计模式.观察者模式
 * @ClassName: WeatherData
 * @Description: TODO
 * @Version 1.0
 */
// 具体主题
class WeatherData implements Subject {
    private List<Observer> observers;
    private float temperature;
    private float humidity;
    private float pressure;

    public WeatherData() {
        observers = new ArrayList<>();
    }

    @Override
    public void registerObserver(Observer o) {
        observers.add(o);
    }

    @Override
    public void removeObserver(Observer o) {
        observers.remove(o);
    }

    @Override
    public void notifyObservers() {
        for (Observer observer : observers) {
            observer.update(temperature, humidity, pressure);
        }
    }

    public void measurementsChanged() {
        notifyObservers();
    }

    public void setMeasurements(float temperature, float humidity, float pressure) {
        this.temperature = temperature;
        this.humidity = humidity;
        this.pressure = pressure;
        measurementsChanged();
    }
}
