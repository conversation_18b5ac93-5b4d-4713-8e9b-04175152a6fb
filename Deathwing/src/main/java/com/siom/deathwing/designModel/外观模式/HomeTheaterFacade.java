package com.siom.deathwing.designModel.外观模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 16:10
 * @PackageName:com.bot.cat.common.设计模式.外观模式
 * @ClassName: HomeTheaterFacade
 * @Description: TODO
 * @Version 1.0
 */
// 外观类
class HomeTheaterFacade {
    private Light light;
    private SoundSystem soundSystem;
    private Projector projector;

    public HomeTheaterFacade(Light light, SoundSystem soundSystem, Projector projector) {
        this.light = light;
        this.soundSystem = soundSystem;
        this.projector = projector;
    }

    public void watchMovie() {
        light.dim(10);
        soundSystem.turnOn();
        soundSystem.setVolume(50);
        projector.turnOn();
        projector.setImput("HDMI");
        System.out.println("Movie is starting...");
    }

    public void endMovie() {
        light.dim(100);
        soundSystem.setVolume(0);
        projector.turnOn(); // This might be a mistake, perhaps it should be turnOff.
        System.out.println("Movie has ended");
    }
    // 客户端代码

    public static void main(String[] args) {
        HomeTheaterFacade homeTheater = new HomeTheaterFacade(new Light(), new SoundSystem(), new Projector());
        homeTheater.watchMovie();
        homeTheater.endMovie();
    }
}
