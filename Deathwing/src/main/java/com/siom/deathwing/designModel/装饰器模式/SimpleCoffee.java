package com.siom.deathwing.designModel.装饰器模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:30
 * @PackageName:com.bot.cat.common.设计模式.装饰器模式
 * @ClassName: SimpleCoffee
 * @Description: TODO
 * @Version 1.0
 */
// 具体组件
class SimpleCoffee implements Coffee {
    @Override
    public double cost() {
        return 1.0;
    }

    @Override
    public String description() {
        return "Simple Coffee";
    }
}
