package com.siom.deathwing.designModel.命令模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 16:49
 * @PackageName:com.bot.cat.common.设计模式.命令模式
 * @ClassName: LightOffCommand
 * @Description: TODO
 * @Version 1.0
 */
class LightOffCommand implements Command {
    private Light light;

    public LightOffCommand(Light light) {
        this.light = light;
    }

    @Override
    public void execute() {
        light.off();
    }
}
