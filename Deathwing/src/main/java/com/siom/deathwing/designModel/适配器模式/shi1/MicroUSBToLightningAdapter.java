package com.siom.deathwing.designModel.适配器模式.shi1;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:33
 * @PackageName:com.bot.cat.common.设计模式.适配器模式
 * @ClassName: MicroUSBToLightningAdapter
 * @Description: TODO
 * @Version 1.0
 */
// 适配器类
class MicroUSBToLightningAdapter implements LightningPort {
    private MicroUSBPort microUSB;

    public MicroUSBToLightningAdapter(MicroUSBPort microUSB) {
        this.microUSB = microUSB;
    }

    @Override
    public void chargeWithLightning() {
        microUSB.chargeWithMicroUSB();
    }
}
