package com.siom.deathwing.designModel.装饰器模式;

/**
 * <AUTHOR>
 * @Date 2024-03-01 15:32
 * @PackageName:com.bot.cat.common.设计模式.装饰器模式
 * @ClassName: CoffeeShop
 * @Description: TODO
 * @Version 1.0
 */
// 客户端代码
public class CoffeeShop {
    public static void main(String[] args) {
        Coffee coffee = new SimpleCoffee();
        coffee = new WithMilk(coffee);
        coffee = new WithSugar(coffee);
        System.out.println(coffee.description() + " Cost: $" + coffee.cost());
    }
}
