#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本格式化工具 - 将长段落分行，避免IDE中出现横向滚动条
"""

import re
import os

def format_text_file(input_file, output_file=None, max_line_length=80):
    """
    格式化文本文件，将过长的行进行合理换行
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None则覆盖原文件
        max_line_length: 每行最大长度
    """
    if output_file is None:
        output_file = input_file
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    formatted_lines = []
    
    for line in lines:
        line = line.rstrip('\n\r')
        
        # 如果是章节标题、网址等特殊行，保持原样
        if (line.startswith('第') and '章' in line) or \
           line.startswith('最新网址') or \
           line.startswith('ps') or \
           line.startswith('————') or \
           len(line.strip()) == 0:
            formatted_lines.append(line)
            continue
        
        # 如果行长度超过限制，进行分行
        if len(line) > max_line_length:
            # 按标点符号分割
            parts = re.split(r'([，。！？；：""''（）【】])', line)
            current_line = ""
            
            for part in parts:
                if len(current_line + part) <= max_line_length:
                    current_line += part
                else:
                    if current_line:
                        formatted_lines.append(current_line)
                        current_line = part
                    else:
                        # 如果单个部分就超长，强制分割
                        while len(part) > max_line_length:
                            formatted_lines.append(part[:max_line_length])
                            part = part[max_line_length:]
                        current_line = part
            
            if current_line:
                formatted_lines.append(current_line)
        else:
            formatted_lines.append(line)
    
    # 写入格式化后的内容
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in formatted_lines:
            f.write(line + '\n')
    
    print(f"格式化完成！原文件 {len(lines)} 行，格式化后 {len(formatted_lines)} 行")

if __name__ == "__main__":
    input_file = "Deathwing/src/main/java/com/siom/deathwing/bk/jl.txt"
    
    if os.path.exists(input_file):
        # 创建备份
        backup_file = input_file + ".backup"
        with open(input_file, 'r', encoding='utf-8') as src, \
             open(backup_file, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
        print(f"已创建备份文件: {backup_file}")
        
        # 格式化文件
        format_text_file(input_file, max_line_length=80)
        print("格式化完成！现在文本应该更适合在IDE中阅读了。")
    else:
        print(f"文件不存在: {input_file}")
