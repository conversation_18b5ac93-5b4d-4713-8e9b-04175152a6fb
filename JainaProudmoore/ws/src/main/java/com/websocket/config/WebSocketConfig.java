package com.websocket.config;

import cn.hutool.extra.spring.SpringUtil;
import com.websocket.annocation.WsPort;
import com.websocket.service.SimonWebSocketService;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * <AUTHOR>
 * @Date 2025-01-13 15:04
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        String[] beanNamesForType = SpringUtil.getBeanNamesForType(SimonWebSocketService.class);
        for (String beanName : beanNamesForType) {
            SimonWebSocketService bean = SpringUtil.getBean(beanName);
            // 根据注解 @WsPort 的 path() 动态创建 WebSocket 端点
            WsPort wsPortAnnotation = bean.getClass().getAnnotation(WsPort.class);
            if (wsPortAnnotation != null) {
                registry.addHandler(bean, wsPortAnnotation.value())  // 动态路径
                        .setAllowedOrigins("*");  // 允许跨域
            }
        }
    }

}
