package com.websocket.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025-01-13 14:59
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
public abstract class SimonWebSocketService<T> extends TextWebSocketHandler {

    public static HashMap<String, WebSocketSession> clients = new HashMap<>();

    // 客户端连接时触发
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("客户端已连接：【{}】", session.getId());
        clients.put(session.getId(), session);
        session.sendMessage(new TextMessage("客户端连接成功 WebSocket！"));
    }

    // 接收客户端消息时触发
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        log.info("服务端收到消息：【{}】", message.getPayload());
        Object responseMessage = isValidJson(message.getPayload()) ?
                doHandlerMessage(JSONObject.parseObject(message.getPayload(), getGenericClass())) :
                doHandlerMessage((T) message.getPayload());
        // 回复客户端消息
        session.sendMessage(new TextMessage(isValidJson(responseMessage) ? JSONObject.toJSONString(responseMessage) : responseMessage.toString()));
    }

    // 客户端断开连接时触发
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        clients.remove(session.getId());
        log.info("客户端断开连接：【{}】", session.getId());
    }

    /**
    *  发送消息给客户端
    */
    public static void sendMessageToClient(String clientId, Object message) {
        try {
            if (!clients.containsKey(clientId)) {
                System.out.println("客户端" + clientId + "未建立连接");
                return;
            }
            clients.get(clientId).sendMessage(new TextMessage(isValidJson(message) ?
                    JSONObject.toJSONString(message) : message.toString()));
        } catch (Exception e) {
            System.out.println("发送消息错误");
            e.printStackTrace();
        }

    }


    /**
     * 处理事件
     */
    protected abstract Object doHandlerMessage(T t);


    /**
     * 获取当前泛型的class
     */
    public Class<T> getGenericClass() {
        // 获取当前类的泛型父类
        Type superClass = getClass().getGenericSuperclass();

        if (superClass instanceof ParameterizedType) {
            Type type = ((ParameterizedType) superClass).getActualTypeArguments()[0];
            return (Class<T>) type;
        }
        throw new RuntimeException("无法获取泛型类型");
    }

    public static boolean isValidJson(Object object) {
        if (object instanceof String) {
            String str = (String) object;
            try {
                // 尝试解析成 JSON 对象，如果成功，说明是 JSON 格式
                JSON.parse(str);
                return true;
            } catch (JSONException e) {
                // 解析失败，说明不是有效的 JSON 格式
                return false;
            }
        }
        return false;
    }
}
