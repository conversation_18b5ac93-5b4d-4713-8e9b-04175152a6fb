<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.rb</groupId>
		<artifactId>simon</artifactId>
		<version>1.0.0.RELEASE</version>
	</parent>

	<groupId>com.rb</groupId>
	<artifactId>JainaProudmoore</artifactId>
	<packaging>pom</packaging>

	<description>永恒之井</description>


	<!--子模块-->
	<modules>
		<module>JainaProudmoore-bom</module>
		<module>rocketMq</module>
		<module>rabbitMq</module>
		<module>ws</module>
		<module>jaina<PERSON>ommon</module>
		<module>noServerAssembly</module>
	</modules>
</project>
