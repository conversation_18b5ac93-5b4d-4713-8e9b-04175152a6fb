### 域名检测
https://github.com/gemgin/WeChatTools
### 雪花算法
https://github.com/baidu/uid-generato
https://blog.csdn.net/lyjluandy/article/details/112550276r
### 自定义banner
- https://www.bootschool.net/ascii-art
### 按钮权限控制文章
- https://blog.csdn.net/Ying_ph/article/details/116209318
### Spring Boot OAuth2
- [Spring Boot OAuth2 Social Login with Google, Facebook, and Github - Part 1](https://www.callicoder.com/spring-boot-security-oauth2-social-login-part-1/) Github:https://github.com/callicoder/spring-boot-react-oauth2-social-login-demo
- Github Demo：https://github.com/spring-projects/spring-security/tree/5.1.6.RELEASE/samples/boot/oauth2login#github-login
- [**深入理解OAuth2.0&基于OAuth2.0第三方登录之GitHub实践**](https://blog.csdn.net/jeffrey20170812/article/details/83588421)
- [实现GitHub OAuth第三方登录](https://zhuanlan.zhihu.com/p/35392186)
- [Using Spring Boot for OAuth2 and JWT REST Protection](https://www.toptal.com/spring/spring-boot-oauth2-jwt-rest-protection)
### 好用笔记
-- https://bright-boy.gitee.io/technical-notes/#/kafka/kafka?id=%e5%85%ab-%e3%80%81springboot%e4%b8%ad%e4%bd%bf%e7%94%a8kafka
