package com.noServerAssembly.mybatis.interceptor_001.annocation;






import com.enums.ProjectEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Description //产品标识
 * @Date 9:28 2023-09-20
 * @return
 * @Param
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ProjectHandler {
    ProjectEnum project() default ProjectEnum.EMPTY;
}
