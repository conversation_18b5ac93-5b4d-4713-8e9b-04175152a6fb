package com.noServerAssembly.mybatis.interceptor_002.interceptor;

import org.apache.ibatis.mapping.SqlCommandType;

/**
 * <AUTHOR>
 * @Date 2023-09-22 15:23
 * @PackageName:com.bot.assembly.cat.mybatisInter.interceptor
 * @ClassName: ExecutorInterceptor
 * @Description: TODO
 * @Version 1.0
 */
public interface ExecutorInterceptor {

    SqlCommandType findSqlCommandType();

    default Object executorBefore() {
        // do nothing
        return null;
    }

    default Boolean doNothing() {
        return Boolean.TRUE;
    }
}
