package com.noServerAssembly.mybatis.interceptor_001.aop.tpc;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2023-09-25 9:15
 * @PackageName:com.xmd.xiaomuding.project.server.interceptor.aop.tpc
 * @ClassName: ThreadParamsSetting
 * @Description: TODO
 * @Version 1.0
 */
public interface ThreadParamsSetting {


    String PROJECT = "project";


    default void settingParams(HttpServletRequest request) {
        if (needSettingParams(request)) {
            doSetting(request);
        }
    }

    default void removeParams(HttpServletRequest request) {
        if (needSettingParams(request)) {
            doRemove(request);
        }
    }

    default boolean needSettingParams(HttpServletRequest request) {
        return Boolean.FALSE;
    }


    void doSetting(HttpServletRequest request);

    void doRemove(HttpServletRequest request);

}
