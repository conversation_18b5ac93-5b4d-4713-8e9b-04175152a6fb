package com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.handler.impl;

import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.annocation.ProjectHandler;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.handler.ProjectEasy;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.schema.Table;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-09-20 9:58
 * @PackageName:com.xmd.xiaomuding.project.server.jsqlparse_demo.handler.impl
 * @ClassName: DefaultProjectEasy
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
@ProjectHandler
public class DefaultProjectEasy implements ProjectEasy {

    @Override
    public Expression doHandlerExpression(Table table) {
        return null;
    }
}
