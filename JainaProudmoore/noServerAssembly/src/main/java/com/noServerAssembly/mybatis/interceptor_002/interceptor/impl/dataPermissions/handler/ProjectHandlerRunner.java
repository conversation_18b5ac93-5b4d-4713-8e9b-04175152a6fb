package com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.handler;

import cn.hutool.extra.spring.SpringUtil;

import com.enums.ProjectEnum;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.annocation.ProjectHandler;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.handler.impl.DefaultProjectEasy;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023-09-20 9:29
 * @PackageName:com.xmd.xiaomuding.project.server.jsqlparse_demo.handler
 * @ClassName: ProjectHandlerRunner
 * @Description: TODO
 * @Version 1.0
 */
@Component
@Slf4j
public class ProjectHandlerRunner implements ApplicationRunner {
    /**
     * 扫描包
     */
    public static final String PATH = DefaultProjectEasy.class.getPackage().getName();
    public static Map<ProjectEnum, Object> cloudMap = new HashMap<>();

    @Override
    public void run(ApplicationArguments args) {
        new Reflections(PATH).getTypesAnnotatedWith(ProjectHandler.class).stream()
                .map(SpringUtil::getBean)
                .forEach(bean -> cloudMap.put(bean.getClass().getAnnotation(ProjectHandler.class).project(), bean));
        //设置默认值
        log.info("产品实例初始化完成: [{}]", cloudMap);
    }
}
