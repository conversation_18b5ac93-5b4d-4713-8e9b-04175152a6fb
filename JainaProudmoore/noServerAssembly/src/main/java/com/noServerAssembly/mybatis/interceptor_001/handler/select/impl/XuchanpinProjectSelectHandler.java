package com.noServerAssembly.mybatis.interceptor_001.handler.select.impl;



import com.enums.ProjectEnum;
import com.noServerAssembly.mybatis.interceptor_001.annocation.ProjectHandler;
import com.noServerAssembly.mybatis.interceptor_001.aop.tpc.impl.XuChanPinParamsSetting;
import com.noServerAssembly.mybatis.interceptor_001.handler.select.ProjectSelectHandler;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.schema.Table;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023-09-20 9:58
 * @PackageName:com.xmd.xiaomuding.project.server.jsqlparse_demo.handler.impl
 * @ClassName: DefaultProjectEasy
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
@ProjectHandler(project = ProjectEnum.XCP)
public class XuchanpinProjectSelectHandler implements ProjectSelectHandler {

    @Override
    public Expression doHandlerExpression(Table table) {
        // 获取表的列名列表
        List<String> tableColumnNames = getTableColumnNames(table.getName());
        // 创建一个空的条件表达式列表
        List<Expression> conditions = new ArrayList<>();
        // 检查 "application_id" 列是否存在，并且是否等于 13
        if (tableColumnNames.contains("application_id")) {
            EqualsTo e1 = new EqualsTo();
            e1.setLeftExpression(getAliasColumn(table, "application_id"));
            e1.setRightExpression(new LongValue(13));
            conditions.add(e1);
        }
        // 查询当前用户是否为超级管理员
        Boolean aBoolean = XuChanPinParamsSetting.isSuperManager.get();
        // 非超级管理员权限时，检查 "user_id" 列是否存在，并且是否等于当前用户的 ID
        if (aBoolean != null && !aBoolean && tableColumnNames.contains("user_id")) {
            EqualsTo e2 = new EqualsTo();
            e2.setLeftExpression(getAliasColumn(table, "user_id"));
            e2.setRightExpression(new LongValue(1));
            conditions.add(e2);
        }
        // 如果没有需要添加的条件，返回 null
        if (conditions.isEmpty()) {
            return null;
        }
        // 如果有条件，使用 AndExpression 连接所有条件
        Expression result = null;
        for (int i = 0; i < conditions.size(); i++) {
            if (result == null) {
                result = conditions.get(i);
            } else {
                result = new AndExpression(result, conditions.get(i));
            }
        }
        return result;
    }
}
