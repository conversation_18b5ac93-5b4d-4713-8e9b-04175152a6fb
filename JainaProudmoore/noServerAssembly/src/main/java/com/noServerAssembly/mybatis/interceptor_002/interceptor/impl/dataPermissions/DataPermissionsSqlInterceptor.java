package com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import com.enums.ProjectEnum;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.ExecutorInterceptor;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.annocation.InterceptorIgnore;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.handler.ProjectEasy;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.handler.ProjectHandlerRunner;
import com.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.util.List;
import java.util.Objects;

import static com.noServerAssembly.mybatis.interceptor_002.MybatisSqlInterceptor.boundSql_TH;
import static com.noServerAssembly.mybatis.interceptor_002.MybatisSqlInterceptor.method_TH;


/**
 * <AUTHOR>
 * @Date 2023-09-15 9:28
 * @PackageName:com.xmd.xiaomuding.project.server.jsqlparse_demo
 * @ClassName: MybatisSqlInterceptor
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
public class DataPermissionsSqlInterceptor implements ExecutorInterceptor {

    public static ThreadLocal<String> PROJECT = new InheritableThreadLocal<>();

    @Override
    public SqlCommandType findSqlCommandType() {
        return SqlCommandType.SELECT;
    }

    @Override
    public Boolean doNothing() {
        //获取project
        String project = WebUtils.getRequest().getHeader("project");
        return StringUtils.isBlank(project) ||
                //没有登录用户 @inner修饰
                //Objects.isNull(SecurityUtils.getUser()) ||
                //mapper方法本身忽略数据权限拦截
                method_TH.get().isAnnotationPresent(InterceptorIgnore.class);
    }

    @Override
    public Object executorBefore() {
        try {
            PROJECT.set(WebUtils.getRequest().getHeader("project"));
            //SELECT拦截
            BoundSql boundSql = boundSql_TH.get();
            Statement statement = CCJSqlParserUtil.parse(boundSql.getSql());
            if (statement instanceof Select) {
                this.processSelect((Select) statement);
            }
            //反射放入修改后的sql语句
            SystemMetaObject.forObject(boundSql).setValue("sql", statement.toString());
        } catch (Exception e) {
            log.error("解析sql出錯:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            PROJECT.remove();
        }
        return null;
    }


    protected void processSelect(Select select) {
        // 处理主查询的SELECT语句的主体部分
        processSelectBody(select.getSelectBody());
        // 获取WITH子句（常见于递归查询）中的所有子查询
        List<WithItem> withItemsList = select.getWithItemsList();
        // 检查是否存在WITH子句
        if (!CollectionUtils.isEmpty(withItemsList)) {
            // 遍历WITH子句中的每个子查询并处理
            withItemsList.forEach(this::processSelectBody);
        }

    }


    protected void processSelectBody(SelectBody selectBody) {
        if (selectBody == null) {
            // 如果selectBody为null，返回
            return;
        }
        if (selectBody instanceof PlainSelect) {
            // 如果selectBody是PlainSelect类型，调用processPlainSelect方法处理
            processPlainSelect((PlainSelect) selectBody);
        } else if (selectBody instanceof WithItem) {
            // 如果selectBody是WithItem类型，提取子查询的selectBody并递归处理
            WithItem withItem = (WithItem) selectBody;
            processSelectBody(withItem.getSubSelect().getSelectBody());
        } else {
            // 如果selectBody是SetOperationList类型，表示包含多个Select的复合查询
            SetOperationList operationList = (SetOperationList) selectBody;
            List<SelectBody> selectBodyList = operationList.getSelects();
            if (CollectionUtils.isNotEmpty(selectBodyList)) {
                // 遍历并递归处理每个SelectBody
                selectBodyList.forEach(this::processSelectBody);
            }
        }
    }


    /**
     * 处理 PlainSelect
     */
    protected void processPlainSelect(PlainSelect plainSelect) {
        FromItem fromItem = plainSelect.getFromItem();
        Expression where = plainSelect.getWhere();
        processWhereSubSelect(where);
        if (fromItem instanceof Table) {
            Table fromTable = (Table) fromItem;
            //业务
            Expression expression = builderExpressionPlatform(plainSelect.getWhere(), fromTable);
            if (expression != null) {
                plainSelect.setWhere(expression);
            }
            //处理数据
        } else {
            processFromItem(fromItem);
        }
        List<SelectItem> selectItems = plainSelect.getSelectItems();
        if (CollectionUtils.isNotEmpty(selectItems)) {
            selectItems.forEach(this::processSelectItem);
        }
        List<Join> joins = plainSelect.getJoins();
        if (CollectionUtils.isNotEmpty(joins)) {
            joins.forEach(j -> {
                processJoin(j);
                processFromItem(j.getRightItem());
            });
        }
    }


    /**
     * 处理子查询等
     */
    protected void processFromItem(FromItem fromItem) {
        if (fromItem instanceof SubJoin) {
            SubJoin subJoin = (SubJoin) fromItem;
            if (subJoin.getJoinList() != null) {
                subJoin.getJoinList().forEach(this::processJoin);
            }
            if (subJoin.getLeft() != null) {
                processFromItem(subJoin.getLeft());
            }
        } else if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if (subSelect.getSelectBody() != null) {
                processSelectBody(subSelect.getSelectBody());
            }
        } else if (fromItem instanceof ValuesList) {
            log.debug("Perform a subquery, if you do not give us feedback");
        } else if (fromItem instanceof LateralSubSelect) {
            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
            if (lateralSubSelect.getSubSelect() != null) {
                SubSelect subSelect = lateralSubSelect.getSubSelect();
                if (subSelect.getSelectBody() != null) {
                    processSelectBody(subSelect.getSelectBody());
                }
            }
        }
    }


    /**
     * 处理where条件内的子查询
     * <p>
     * 支持如下:
     * 1. in
     * 2. =
     * 3. >
     * 4. <
     * 5. >=
     * 6. <=
     * 7. <>
     * 8. EXISTS
     * 9. NOT EXISTS
     * <p>
     * 前提条件:
     * 1. 子查询必放在小括号中
     * 2. 子查询一般放在比较操作符的右边
     *
     * @param where where 条件
     */
    protected void processWhereSubSelect(Expression where) {
        if (where == null) {
            return;
        }
        if (where instanceof FromItem) {
            processFromItem((FromItem) where);
            return;
        }
        if (where.toString().indexOf("SELECT") > 0) {
            // 有子查询
            if (where instanceof BinaryExpression) {
                // 比较符号 , and , or , 等等
                BinaryExpression expression = (BinaryExpression) where;
                processWhereSubSelect(expression.getLeftExpression());
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof InExpression) {
                // in
                InExpression expression = (InExpression) where;
                ItemsList itemsList = expression.getRightItemsList();
                if (itemsList instanceof SubSelect) {
                    processSelectBody(((SubSelect) itemsList).getSelectBody());
                }
            } else if (where instanceof ExistsExpression) {
                // exists
                ExistsExpression expression = (ExistsExpression) where;
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof NotExpression) {
                // not exists
                NotExpression expression = (NotExpression) where;
                processWhereSubSelect(expression.getExpression());
            } else if (where instanceof Parenthesis) {
                Parenthesis expression = (Parenthesis) where;
                processWhereSubSelect(expression.getExpression());
            }
        }
    }


    protected void processSelectItem(SelectItem selectItem) {
        if (selectItem instanceof SelectExpressionItem) {
            SelectExpressionItem selectExpressionItem = (SelectExpressionItem) selectItem;
            if (selectExpressionItem.getExpression() instanceof SubSelect) {
                processSelectBody(((SubSelect) selectExpressionItem.getExpression()).getSelectBody());
            } else if (selectExpressionItem.getExpression() instanceof Function) {
                processFunction((Function) selectExpressionItem.getExpression());
            }
        }
    }


    /**
     * 处理函数
     * <p>支持: 1. select fun(args..) 2. select fun1(fun2(args..),args..)<p>
     * <p> fixed gitee pulls/141</p>
     *
     * @param function
     */
    protected void processFunction(Function function) {
        ExpressionList parameters = function.getParameters();
        if (parameters != null) {
            parameters.getExpressions().forEach(expression -> {
                if (expression instanceof SubSelect) {
                    processSelectBody(((SubSelect) expression).getSelectBody());
                } else if (expression instanceof Function) {
                    processFunction((Function) expression);
                }
            });
        }
    }


    /**
     * 处理联接语句
     */
    protected void processJoin(Join join) {
        if (join.getRightItem() instanceof Table) {
            Table fromTable = (Table) join.getRightItem();
            Expression expression = builderExpressionPlatform(join.getOnExpression(), fromTable);
            if (!Objects.isNull(expression)) {
                join.setOnExpression(expression);
            }
        }
    }

    /**
     * 处理条件 TODO
     */
    protected Expression builderExpressionPlatform(Expression currentExpression, Table table) {
        ProjectEasy cloudBean = (ProjectEasy) ProjectHandlerRunner.cloudMap.get(ProjectEnum.getEnum(PROJECT.get()));
        if (Objects.isNull(cloudBean)) {
            return currentExpression;
        }
        Expression expression = cloudBean.handlerExpression(table);
        if (expression == null) {
            return currentExpression;
        }
        if (currentExpression == null) {
            return expression;
        }
        if (currentExpression instanceof OrExpression) {
            return new AndExpression(new Parenthesis(currentExpression), expression);
        }
        return new AndExpression(currentExpression, expression);
    }




}
