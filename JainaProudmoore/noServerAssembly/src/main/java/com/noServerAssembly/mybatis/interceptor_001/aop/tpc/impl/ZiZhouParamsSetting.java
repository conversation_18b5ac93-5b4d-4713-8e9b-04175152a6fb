package com.noServerAssembly.mybatis.interceptor_001.aop.tpc.impl;



import com.noServerAssembly.mybatis.interceptor_001.aop.tpc.ThreadParamsSetting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2023-09-25 9:16
 * @PackageName:com.xmd.xiaomuding.project.server.interceptor.aop.tpc
 * @ClassName: ZiZhouParamsSetting
 * @Description: 子洲管理员判断
 * @Version 1.0
 */
@Service
@Slf4j
public class ZiZhouParamsSetting implements ThreadParamsSetting {

//    @Resource
//    private AuthMapper authMapper;
    public static ThreadLocal<Boolean> isManager = new InheritableThreadLocal<>();

    @Override
    public void doSetting(HttpServletRequest request) {
//        isManager.set(SecurityUtils.getUser() != null ?
//                authMapper.isZiZhouManager(SecurityUtils.getUser().getId()) > 0 : Boolean.FALSE);
    }

    @Override
    public void doRemove(HttpServletRequest request) {
        isManager.remove();
    }

    @Override
    public boolean needSettingParams(HttpServletRequest request) {
        //return ProjectEnum.ZI_ZHOU.getCode().equals(request.getHeader(PROJECT));
        return false;
    }
}
