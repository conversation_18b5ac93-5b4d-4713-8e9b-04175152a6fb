package com.noServerAssembly.mybatis.interceptor_001.handler.select;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import com.enums.ProjectEnum;
import com.noServerAssembly.mybatis.interceptor_001.annocation.DataPermissionsIgnore;
import com.noServerAssembly.mybatis.interceptor_001.handler.select.impl.DefaultProjectSelectHandler;
import com.noServerAssembly.mybatis.interceptor_001.handler.select.impl.XuchanpinProjectSelectHandler;
import com.noServerAssembly.mybatis.interceptor_001.handler.select.impl.ZizhouProjectSelectHandler;
import com.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.context.annotation.Import;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2023-10-08 10:00
 * @PackageName:com.xmd.xiaomuding.common.security.interceptor.handler.select
 * @ClassName: SelectInterceptor
 * @Description: 查询处理拦截
 * @Version 1.0
 */
@Slf4j
@Import({
        //查询启动类
        ProjectDataPermissionsSelectHandlerRunner.class,
        //默认 空
        DefaultProjectSelectHandler.class,
        //畜产品数据权限
        XuchanpinProjectSelectHandler.class,
        //子洲数据权限
        ZizhouProjectSelectHandler.class})
public class SelectInterceptor {

    public static ThreadLocal<String> PROJECT = new InheritableThreadLocal<>();

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description //查询处理拦截
     * @Date 10:42 2023-10-08
     * @Param [org.apache.ibatis.plugin.Invocation]
     */
    public Object intercept(Invocation invocation) throws Exception {
        if (isIgnore(invocation)) {
            return invocation.proceed();
        }
        Object target = invocation.getTarget();
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        Executor executor = (Executor) target;
        //SELECT拦截
        if (SqlCommandType.SELECT.equals(ms.getSqlCommandType())) {
            BoundSql boundSql = args.length == 4 ? ms.getBoundSql(parameter) : (BoundSql) args[5];
            RowBounds rowBounds = (RowBounds) args[2];
            CacheKey cacheKey = args.length == 4 ? executor.createCacheKey(ms, parameter, rowBounds, boundSql) : (CacheKey) args[4];
            Statement statement = CCJSqlParserUtil.parse(boundSql.getSql());
            if (statement instanceof Select) {
                this.processSelect((Select) statement);
            }
            //反射放入修改后的sql语句
            SystemMetaObject.forObject(boundSql).setValue("SQL", statement.toString());
            return executor.query(ms, parameter, rowBounds, (ResultHandler) args[3], cacheKey, boundSql);
        }
        return invocation.proceed();
    }


    public Boolean isIgnore(Invocation invocation) throws Exception {
        //获取project
        String project = WebUtils.getRequest().getHeader("project");
        //前段未传项目编码
        if (StringUtils.isBlank(project) ||
                //没有登录用户 @inner修饰
                Objects.isNull("SecurityUtils.getUser()") ||
                //不需要添加数据权限的项目
                Objects.isNull(ProjectEnum.getEnum(project))||
                //不需要添加数据权限的项目
                ProjectEnum.EMPTY.equals(ProjectEnum.getEnum(project))) {
            return true;
        }
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        //mapper方法本身忽略数据权限拦截
        String namespace = ms.getId();
        String className = namespace.substring(0, namespace.lastIndexOf("."));
        String methodName = namespace.substring(namespace.lastIndexOf(".") + 1, namespace.length()).split("_")[0];
        Method[] methods = Class.forName(className).getMethods();
        for (Method m : methods) {
            if (m.getName().equals(methodName)) {
                if (m.isAnnotationPresent(DataPermissionsIgnore.class)) {
                    return true;
                }
            }
        }
        PROJECT.set(project);
        return false;
    }


    protected void processSelect(Select select) {
        // 处理主查询的SELECT语句的主体部分
        processSelectBody(select.getSelectBody());
        // 获取WITH子句（常见于递归查询）中的所有子查询
        List<WithItem> withItemsList = select.getWithItemsList();
        // 检查是否存在WITH子句
        if (!CollectionUtils.isEmpty(withItemsList)) {
            // 遍历WITH子句中的每个子查询并处理
            withItemsList.forEach(this::processSelectBody);
        }

    }


    protected void processSelectBody(SelectBody selectBody) {
        if (selectBody == null) {
            // 如果selectBody为null，返回
            return;
        }
        if (selectBody instanceof PlainSelect) {
            // 如果selectBody是PlainSelect类型，调用processPlainSelect方法处理
            processPlainSelect((PlainSelect) selectBody);
        } else if (selectBody instanceof WithItem) {
            // 如果selectBody是WithItem类型，提取子查询的selectBody并递归处理
            WithItem withItem = (WithItem) selectBody;
            processSelectBody(withItem.getSubSelect().getSelectBody());
        } else {
            // 如果selectBody是SetOperationList类型，表示包含多个Select的复合查询
            SetOperationList operationList = (SetOperationList) selectBody;
            List<SelectBody> selectBodyList = operationList.getSelects();
            if (CollectionUtils.isNotEmpty(selectBodyList)) {
                // 遍历并递归处理每个SelectBody
                selectBodyList.forEach(this::processSelectBody);
            }
        }
    }


    /**
     * 处理 PlainSelect
     */
    protected void processPlainSelect(PlainSelect plainSelect) {
        FromItem fromItem = plainSelect.getFromItem();
        Expression where = plainSelect.getWhere();
        processWhereSubSelect(where);
        if (fromItem instanceof Table) {
            Table fromTable = (Table) fromItem;
            //业务
            Expression expression = builderExpressionPlatform(plainSelect.getWhere(), fromTable);
            if (expression != null) {
                plainSelect.setWhere(expression);
            }
            //处理数据
        } else {
            processFromItem(fromItem);
        }
        List<SelectItem> selectItems = plainSelect.getSelectItems();
        if (CollectionUtils.isNotEmpty(selectItems)) {
            selectItems.forEach(this::processSelectItem);
        }
        List<Join> joins = plainSelect.getJoins();
        if (CollectionUtils.isNotEmpty(joins)) {
            joins.forEach(j -> {
                processJoin(j);
                processFromItem(j.getRightItem());
            });
        }
    }


    /**
     * 处理子查询等
     */
    protected void processFromItem(FromItem fromItem) {
        if (fromItem instanceof SubJoin) {
            SubJoin subJoin = (SubJoin) fromItem;
            if (subJoin.getJoinList() != null) {
                subJoin.getJoinList().forEach(this::processJoin);
            }
            if (subJoin.getLeft() != null) {
                processFromItem(subJoin.getLeft());
            }
        } else if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if (subSelect.getSelectBody() != null) {
                processSelectBody(subSelect.getSelectBody());
            }
        } else if (fromItem instanceof ValuesList) {
            log.debug("Perform a subquery, if you do not give us feedback");
        } else if (fromItem instanceof LateralSubSelect) {
            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
            if (lateralSubSelect.getSubSelect() != null) {
                SubSelect subSelect = lateralSubSelect.getSubSelect();
                if (subSelect.getSelectBody() != null) {
                    processSelectBody(subSelect.getSelectBody());
                }
            }
        }
    }


    /**
     * 处理where条件内的子查询
     * <p>
     * 支持如下:
     * 1. in
     * 2. =
     * 3. >
     * 4. <
     * 5. >=
     * 6. <=
     * 7. <>
     * 8. EXISTS
     * 9. NOT EXISTS
     * <p>
     * 前提条件:
     * 1. 子查询必放在小括号中
     * 2. 子查询一般放在比较操作符的右边
     *
     * @param where where 条件
     */
    protected void processWhereSubSelect(Expression where) {
        if (where == null) {
            return;
        }
        if (where instanceof FromItem) {
            processFromItem((FromItem) where);
            return;
        }
        if (where.toString().indexOf("SELECT") > 0) {
            // 有子查询
            if (where instanceof BinaryExpression) {
                // 比较符号 , and , or , 等等
                BinaryExpression expression = (BinaryExpression) where;
                processWhereSubSelect(expression.getLeftExpression());
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof InExpression) {
                // in
                InExpression expression = (InExpression) where;
                ItemsList itemsList = expression.getRightItemsList();
                if (itemsList instanceof SubSelect) {
                    processSelectBody(((SubSelect) itemsList).getSelectBody());
                }
            } else if (where instanceof ExistsExpression) {
                // exists
                ExistsExpression expression = (ExistsExpression) where;
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof NotExpression) {
                // not exists
                NotExpression expression = (NotExpression) where;
                processWhereSubSelect(expression.getExpression());
            } else if (where instanceof Parenthesis) {
                Parenthesis expression = (Parenthesis) where;
                processWhereSubSelect(expression.getExpression());
            }
        }
    }


    protected void processSelectItem(SelectItem selectItem) {
        if (selectItem instanceof SelectExpressionItem) {
            SelectExpressionItem selectExpressionItem = (SelectExpressionItem) selectItem;
            if (selectExpressionItem.getExpression() instanceof SubSelect) {
                processSelectBody(((SubSelect) selectExpressionItem.getExpression()).getSelectBody());
            } else if (selectExpressionItem.getExpression() instanceof Function) {
                processFunction((Function) selectExpressionItem.getExpression());
            }
        }
    }


    /**
     * 处理函数
     * <p>支持: 1. select fun(args..) 2. select fun1(fun2(args..),args..)<p>
     * <p> fixed gitee pulls/141</p>
     *
     * @param function
     */
    protected void processFunction(Function function) {
        ExpressionList parameters = function.getParameters();
        if (parameters != null) {
            parameters.getExpressions().forEach(expression -> {
                if (expression instanceof SubSelect) {
                    processSelectBody(((SubSelect) expression).getSelectBody());
                } else if (expression instanceof Function) {
                    processFunction((Function) expression);
                }
            });
        }
    }


    /**
     * 处理联接语句
     */
    protected void processJoin(Join join) {
        if (join.getRightItem() instanceof Table) {
            Table fromTable = (Table) join.getRightItem();
            Expression expression = builderExpressionPlatform(join.getOnExpression(), fromTable);
            if (!Objects.isNull(expression)) {
                join.setOnExpression(expression);
            }
        }
    }

    /**
     * 处理条件 TODO
     */
    protected Expression builderExpressionPlatform(Expression currentExpression, Table table) {
        ProjectSelectHandler cloudBean = (ProjectSelectHandler) ProjectDataPermissionsSelectHandlerRunner.cloudMap.get(ProjectEnum.getEnum(PROJECT.get()));
        if (Objects.isNull(cloudBean)) {
            return currentExpression;
        }
        Expression expression = cloudBean.handlerExpression(table);
        if (expression == null) {
            return currentExpression;
        }
        if (currentExpression == null) {
            return expression;
        }
        if (currentExpression instanceof OrExpression) {
            return new AndExpression(new Parenthesis(currentExpression), expression);
        }
        return new AndExpression(currentExpression, expression);
    }
}
