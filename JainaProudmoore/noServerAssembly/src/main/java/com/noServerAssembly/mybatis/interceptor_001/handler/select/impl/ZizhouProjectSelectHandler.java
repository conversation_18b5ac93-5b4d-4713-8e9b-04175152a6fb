package com.noServerAssembly.mybatis.interceptor_001.handler.select.impl;



import com.enums.ProjectEnum;
import com.noServerAssembly.mybatis.interceptor_001.annocation.ProjectHandler;
import com.noServerAssembly.mybatis.interceptor_001.aop.tpc.impl.ZiZhouParamsSetting;
import com.noServerAssembly.mybatis.interceptor_001.handler.select.ProjectSelectHandler;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.schema.Table;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023-09-20 9:58
 * @PackageName:com.xmd.xiaomuding.project.server.jsqlparse_demo.handler.impl
 * @ClassName: ZizhouProjectEasy
 * @Description: 子洲项目权限
 * @Version 1.0
 */
@Service
@Slf4j
@ProjectHandler(project = ProjectEnum.XCP)
public class ZizhouProjectSelectHandler implements ProjectSelectHandler {

    @Override
    public Expression doHandlerExpression(Table table) {
        // 获取表的列名列表
        List<String> tableColumnNames = getTableColumnNames(table.getName());
        // 创建一个空的条件表达式列表
        List<Expression> conditions = new ArrayList<>();
        // 查询当前用户是否为超级管理员
        Boolean aBoolean = ZiZhouParamsSetting.isManager.get();
        //如果不是超管 拼接用户id
        if (aBoolean != null && !aBoolean) {
            EqualsTo e2 = new EqualsTo();
            if (tableColumnNames.contains("create_user_id")) {
                e2.setLeftExpression(getAliasColumn(table, "create_user_id"));
                e2.setRightExpression(new LongValue(1));
                conditions.add(e2);
            } else if (tableColumnNames.contains("expert_user_id")) {
                e2.setLeftExpression(getAliasColumn(table, "expert_user_id"));
                e2.setRightExpression(new LongValue(1));
                conditions.add(e2);
            } else if (tableColumnNames.contains("user_id")) {
                e2.setLeftExpression(getAliasColumn(table, "user_id"));
                e2.setRightExpression(new LongValue(1));
                conditions.add(e2);
            }
        }
        // 检查 "project_code" 列是否存在，并且是否等于 zizhou
        if (tableColumnNames.contains("project_code")) {
            EqualsTo e1 = new EqualsTo();
            e1.setLeftExpression(getAliasColumn(table, "project_code"));
            e1.setRightExpression(new StringValue("zizhou"));
            conditions.add(e1);
        }
        // 如果没有需要添加的条件，返回 null
        if (conditions.isEmpty()) {
            return null;
        }
        // 如果有条件，使用 AndExpression 连接所有条件
        Expression result = null;
        for (int i = 0; i < conditions.size(); i++) {
            if (result == null) {
                result = conditions.get(i);
            } else {
                result = new AndExpression(result, conditions.get(i));
            }
        }
        return result;
    }
}
