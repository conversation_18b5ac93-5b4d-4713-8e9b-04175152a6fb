package com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-09-22 11:38
 * @PackageName:com.xmd.xiaomuding.project.server.interceptor.aop
 * @ClassName: AuthAspect
 * @Description: TODO
 * @Version 1.0
 */
@Aspect
@Component
@Slf4j
public class AuthAspect {


    public static ThreadLocal<Boolean> isSuperManager = new InheritableThreadLocal<>();

    /**
     * 定义切点
     */
    @Pointcut("execution(* com..controller..*.*(..))")
    public void pot() {
    }

    @Around("pot()")
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            //XiaomudingUser user = SecurityUtils.getUser();
            if (true) {
                isSuperManager.set(true);
                return joinPoint.proceed();
            }
            isSuperManager.set(Boolean.FALSE);
        } catch (Throwable throwable) {
            throw throwable;
        } finally {
            isSuperManager.remove();
        }
        return joinPoint.proceed();
    }


}
