package com.noServerAssembly.mybatis.interceptor_001.handler.select;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description //ProjectEasy
 * @Date 10:03 2023-09-20
 * @return
 * @Param
 */
public interface ProjectSelectHandler {

    //  表忽略名单 TODO 不能在拦截器中写查询sql**
    List<String> ignoreTables = new ArrayList<>();
    JdbcTemplate JDBC_TEMPLATE = SpringUtil.getBean(JdbcTemplate.class);

    /**
     * 划拨
     */
    default Expression handlerExpression(Table table) {
        if (ignoreTables.contains(table.getName())) {
            return null;
        }
        return doHandlerExpression(table);
    }


    Expression doHandlerExpression(Table table);

    default Column getAliasColumn(Table table, String fieldName) {
        StringBuilder column = new StringBuilder();
        if (table.getAlias() != null) {
            column.append(table.getAlias().getName()).append(StringPool.DOT);
        }
        column.append(fieldName);
        return new Column(column.toString());
    }

    /**
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Description //获取表当前所有的字段
     * @Date 14:38 2023-09-20
     * @Param [java.lang.String]
     */
    default List<String> getTableColumnNames(String tableName) {
        // 查询指定表的所有字段名，你需要根据你的数据库类型编写相应的 SQL 查询
        // 这里的示例使用 MySQL 查询指定表的字段名
        List<String> columnNames = new ArrayList<>();
        JDBC_TEMPLATE.query("SHOW COLUMNS FROM " + tableName, (resultSet, rowNum) -> {
            columnNames.add(resultSet.getString("Field"));
            return null;
        });
        return columnNames;
    }


}