package com.noServerAssembly.mybatis.interceptor_001.aop.tpc.impl;



import com.enums.ProjectEnum;
import com.noServerAssembly.mybatis.interceptor_001.aop.tpc.ThreadParamsSetting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2023-09-25 9:16
 * @PackageName:com.xmd.xiaomuding.project.server.interceptor.aop.tpc
 * @ClassName: XuchanpinParamsSetting
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
public class XuChanPinParamsSetting implements ThreadParamsSetting {

//    @Resource
//    private AuthMapper authMapper;
    public static ThreadLocal<Boolean> isSuperManager = new InheritableThreadLocal<>();

    @Override
    public void doSetting(HttpServletRequest request) {
//        isSuperManager.set(SecurityUtils.getUser() != null ?
//                authMapper.isXuChanPinManager(SecurityUtils.getUser().getId()) > 0 : Boolean.FALSE);

    }

    @Override
    public void doRemove(HttpServletRequest request) {
        isSuperManager.remove();
    }

    @Override
    public boolean needSettingParams(HttpServletRequest request) {
        return ProjectEnum.XCP.getCode().equals(request.getHeader(PROJECT));
    }
}
