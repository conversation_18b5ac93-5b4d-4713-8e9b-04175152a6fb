package com.noServerAssembly.mybatis.interceptor_001;


import cn.hutool.extra.spring.SpringUtil;

import com.noServerAssembly.mybatis.interceptor_001.handler.insert.InsertInterceptor;
import com.noServerAssembly.mybatis.interceptor_001.handler.select.SelectInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-09-15 9:28
 * @PackageName:com.xmd.xiaomuding.project.server.jsqlparse_demo
 * @ClassName: XiaomudingSqlInterceptor
 * @Description: mybatis 拦截器
 * @Version 1.0
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
@Import({SelectInterceptor.class, InsertInterceptor.class})
public class XiaomudingSqlInterceptor implements Interceptor {


    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description //拦截业务sql并进行拦截
     * @Date 10:52 2023-10-08
     * @Param [org.apache.ibatis.plugin.Invocation]
     */
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
            //SELECT拦截
            if (SqlCommandType.SELECT.equals(ms.getSqlCommandType())) {
                return SpringUtil.getBean(SelectInterceptor.class).intercept(invocation);
            }
            //INSERT拦截
            if (SqlCommandType.INSERT.equals(ms.getSqlCommandType())) {
                return SpringUtil.getBean(InsertInterceptor.class).intercept(invocation);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("拦截出现异常:{}", e.getMessage());
        }
        return invocation.proceed();
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

}
