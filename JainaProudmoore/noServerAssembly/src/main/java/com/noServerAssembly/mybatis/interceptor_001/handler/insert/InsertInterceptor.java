package com.noServerAssembly.mybatis.interceptor_001.handler.insert;

import cn.hutool.core.util.ObjectUtil;

import com.enums.ProjectEnum;
import com.utils.WebUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.plugin.Invocation;

import java.lang.reflect.Field;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.noServerAssembly.mybatis.interceptor_001.aop.tpc.ThreadParamsSetting.PROJECT;


/**
 * <AUTHOR>
 * @Date 2023-10-08 10:00
 * @PackageName:com.xmd.xiaomuding.common.security.interceptor.handler.select
 * @ClassName: SelectInterceptor
 * @Description: 查询处理拦截
 * @Version 1.0
 */
@Slf4j
public class InsertInterceptor {

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description //查询处理拦截
     * @Date 10:42 2023-10-08
     * @Param [org.apache.ibatis.plugin.Invocation]
     */
    public Object intercept(Invocation invocation) throws Exception {
        //目前只有project需要次拦截器 所以判断该条件是否满足 后续如有其余字段 需要改动
        if (Objects.isNull(WebUtils.getRequest()) || StringUtils.isEmpty(WebUtils.getRequest().getHeader("PROJECT")) || ProjectEnum.EMPTY.equals(ProjectEnum.getEnum(WebUtils.getRequest().getHeader("PROJECT")))) {
            return invocation.proceed();
        }
        Object parameter = invocation.getArgs()[1];
        if (parameter instanceof Map) {
            insertList(parameter);
        } else {
            doInsert(parameter.getClass(), parameter);
        }
        return invocation.proceed();
    }

    /**
     * 批量新增
     *
     * @param parameter
     */
    private void insertList(Object parameter) {
        getData(parameter).stream().forEach(x -> this.doInsert(x.getClass(), x));
    }

    /**
     * 执行入库操作
     */
    @SneakyThrows
    private void doInsert(Class<?> aClass, Object parameter) {
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            switch (field.getName()) {
                //设置唯一ID
                case "PROJECT":
                    field.set(parameter, WebUtils.getRequest().getHeader(PROJECT));
                    break;
            }
        }
        //如果是继承关系 父类含有此属性 给父类赋值
        if (ObjectUtil.isNotNull(aClass.getSuperclass())) {
            doInsert(aClass.getSuperclass(), parameter);
        }
    }


    /**
     * 获取参数中的数据
     *
     * @param parameter
     * @return
     */
    private List getData(Object parameter) {
        Map map = (Map) parameter;
        Iterator data = map.keySet().iterator();
        while (data.hasNext()) {
            Object obj = map.get(data.next());
            if (obj instanceof List) {
                return (List) obj;
            }
        }
        return null;
    }
}
