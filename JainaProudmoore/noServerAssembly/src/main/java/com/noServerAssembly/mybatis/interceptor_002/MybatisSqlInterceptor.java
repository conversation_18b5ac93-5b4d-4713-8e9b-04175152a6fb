package com.noServerAssembly.mybatis.interceptor_002;

import com.noServerAssembly.mybatis.interceptor_002.interceptor.ExecutorInterceptor;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.DataPermissionsSqlInterceptor;
import com.noServerAssembly.mybatis.interceptor_002.interceptor.impl.dataPermissions.annocation.InterceptorIgnore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-09-22 14:37
 * @PackageName:com.bot.assembly.cat.mybatisInter
 * @ClassName: MybatisSqlInterceptor
 * @Description: mybatis修改sql的拦截器集合处理
 * @Version 1.0
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class}),
        @Signature(type = StatementHandler.class, method = "getBoundSql", args = {}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class MybatisSqlInterceptor implements Interceptor {
    public static ThreadLocal<Executor> executor_TH = new InheritableThreadLocal<>();
    public static ThreadLocal<Method> method_TH = new InheritableThreadLocal<>();
    public static ThreadLocal<MappedStatement> mappedStatement_TH = new InheritableThreadLocal<>();
    public static ThreadLocal<Object> parameter_TH = new InheritableThreadLocal<>();
    public static ThreadLocal<BoundSql> boundSql_TH = new InheritableThreadLocal<>();
    public static ThreadLocal<RowBounds> rowBounds_TH = new InheritableThreadLocal<>();
    public static ThreadLocal<CacheKey> cacheKey_TH = new InheritableThreadLocal<>();
    public static List<ExecutorInterceptor> executorInterceptors = new ArrayList<>();


    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            if (isIgnore(invocation)) {
                return invocation.proceed();
            }
            setParams(invocation);
            Object target = invocation.getTarget();
            Object[] args = invocation.getArgs();
            if (target instanceof Executor) {
                if (CollectionUtils.isEmpty(executorInterceptors)) {
                    return invocation.proceed();
                }
                Map<SqlCommandType, List<ExecutorInterceptor>> es =
                        executorInterceptors.stream().collect(Collectors.groupingBy(ExecutorInterceptor::findSqlCommandType));
                switch (mappedStatement_TH.get().getSqlCommandType()) {
                    case SELECT:
                        List<ExecutorInterceptor> selectInterceptors = es.get(SqlCommandType.SELECT);
                        if (CollectionUtils.isEmpty(selectInterceptors)) {
                            return invocation.proceed();
                        }
                        selectInterceptors.stream().filter(interceptor -> interceptor.doNothing()).forEach(ExecutorInterceptor::executorBefore);
                        return executor_TH.get().query(mappedStatement_TH.get(), parameter_TH.get(), rowBounds_TH.get(), (ResultHandler) args[3], cacheKey_TH.get(), boundSql_TH.get());
                    case UPDATE:
                        List<ExecutorInterceptor> updateInterceptors = es.get(SqlCommandType.UPDATE);
                        if (CollectionUtils.isEmpty(updateInterceptors)) {
                            return invocation.proceed();
                        }
                        updateInterceptors.stream().filter(interceptor -> interceptor.doNothing()).forEach(ExecutorInterceptor::executorBefore);
                        return executor_TH.get().update(mappedStatement_TH.get(), parameter_TH.get());
                    case INSERT:
                        List<ExecutorInterceptor> insertInterceptors = es.get(SqlCommandType.INSERT);
                        if (CollectionUtils.isEmpty(insertInterceptors)) {
                            return invocation.proceed();
                        }
                        insertInterceptors.stream().filter(interceptor -> interceptor.doNothing()).forEach(ExecutorInterceptor::executorBefore);
                        return executor_TH.get().update(mappedStatement_TH.get(), parameter_TH.get());
                    default:
                        return invocation.proceed();
                }
            } else {
                //TODO
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Mybatis SQL拦截器异常{}", e.getMessage());
        } finally {
            removeParams();
        }
        return invocation.proceed();
    }


    private void setParams(Invocation invocation) {
        if (invocation.getTarget() instanceof Executor) {
            method_TH.set(invocation.getMethod());
            Object[] args = invocation.getArgs();
            executor_TH.set((Executor) invocation.getTarget());
            MappedStatement mappedStatement = (MappedStatement) args[0];
            mappedStatement_TH.set(mappedStatement);
            Object parameter = args[1];
            parameter_TH.set(parameter);
            BoundSql boundSql = args.length == 4 ? mappedStatement.getBoundSql(parameter) : (BoundSql) args[5];
            boundSql_TH.set(boundSql);
            RowBounds rowBounds = (RowBounds) args[2];
            rowBounds_TH.set(rowBounds);
            CacheKey cacheKey = args.length == 4 ? executor_TH.get().createCacheKey(mappedStatement, parameter, rowBounds, boundSql) : (CacheKey) args[4];
            cacheKey_TH.set(cacheKey);
        }
    }

    private void removeParams() {
        if (!Objects.isNull(executor_TH)) {
            executor_TH.remove();
        }
        if (!Objects.isNull(method_TH)) {
            method_TH.remove();
        }
        if (!Objects.isNull(mappedStatement_TH)) {
            mappedStatement_TH.remove();
        }
        if (!Objects.isNull(parameter_TH)) {
            parameter_TH.remove();
        }
        if (!Objects.isNull(boundSql_TH)) {
            boundSql_TH.remove();
        }
        if (!Objects.isNull(rowBounds_TH)) {
            rowBounds_TH.remove();
        }
        if (!Objects.isNull(cacheKey_TH)) {
            cacheKey_TH.remove();
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }


    @PostConstruct
    private void initInterceptorList() {
        executorInterceptors.clear();
        executorInterceptors.add(new DataPermissionsSqlInterceptor());
    }

    /**
     * <AUTHOR>
     * @Description //忽略过滤注解
     * @Date 18:10 2023-09-22
     * @return java.lang.Boolean
     * @Param [org.apache.ibatis.plugin.Invocation]
    */
    public Boolean isIgnore(Invocation invocation) throws Exception {
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        //mapper方法本身忽略数据权限拦截
        String namespace = ms.getId();
        String className = namespace.substring(0, namespace.lastIndexOf("."));
        String methodName = namespace.substring(namespace.lastIndexOf(".") + 1, namespace.length()).split("_")[0];
        Method[] methods = Class.forName(className).getMethods();
        for (Method m : methods) {
            if (m.getName().equals(methodName)) {
                if (m.isAnnotationPresent(InterceptorIgnore.class)) {
                    return true;
                }
            }
        }
        return false;
    }

}
