package com.noServerAssembly.mybatis.interceptor_001.annocation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2023-09-20 14:42
 * @PackageName:com.xmd.xiaomuding.project.server.interceptor.annocation
 * @ClassName: DataAuthInterceptorIgnore
 * @Description: TODO
 * @Version 1.0
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermissionsIgnore {
}
