package com.noServerAssembly.nettyclient;

import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @Date 2023-11-13 11:40
 * @PackageName:com.bot.assembly.cat.netty
 * @ClassName: Constant
 * @Description: TODO
 * @Version 1.0
 */
public class Constant {
    // 常量，Client需要的一些参数信息，需要根据实际情况进行自定义修改
    static final String HOST = "127.0.0.1";   // 要连接的服务器地址
    static final int PORT = 9898;   // 要连接的服务器端口号
    static final String CLIENT_PREFIX = "Client received:";   // Client显示接收到Server发送的信息的前缀

    public static void main(String[] args) {
        System.out.println(URLDecoder.decode("password=%5B666666%5D&grant_type=%5Bmobile%5D&passwordEncode=%5Bnoop%5D&scope=%5Bserver%5D&username=%5B15091405755%5D"));
    }
}
