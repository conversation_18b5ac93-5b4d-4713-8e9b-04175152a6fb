package com.noServerAssembly.pay.ali.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;

import com.noServerAssembly.pay.ali.properties.AliPayProperties;
import lombok.SneakyThrows;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 项目名称: pay_core
 *
 * @ClassName AliPayConfig
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/5/7 16:42
 */
@Component
@Configuration
@ComponentScan("com.noServerAssembly.pay.**")
@ConditionalOnProperty(prefix = "cat.pay.alipay", name = "use", havingValue = "false")
@EnableConfigurationProperties({AliPayProperties.class})
public class PayConfig {

    private AliPayProperties aliPay;


    @Bean
    public AlipayConfig alipayConfig(AliPayProperties aliPay) {
        this.aliPay=aliPay;
        AlipayConfig alipayConfig=new AlipayConfig();
        alipayConfig.setAppId(aliPay.getApp_id());
        alipayConfig.setServerUrl("https://openapi.alipaydev.com/gateway.do");
        alipayConfig.setPrivateKey(aliPay.getPrivateKey());
        alipayConfig.setAlipayPublicKey(aliPay.getAlipayPublicKey());
        return alipayConfig;
    }
    /**
    * new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",
     * "app_id",
     * "your private_key",
     * "json",
     * "GBK",
     * "alipay_public_key",
     * "RSA2");
    */
    @Bean
    @SneakyThrows
    public AlipayClient alipayClient(AlipayConfig alipayConfig) {
        return new DefaultAlipayClient(alipayConfig);
    }

    @Bean
    public AlipayTradePagePayRequest alipayTradePagePayRequest(AliPayProperties aliPay){
        AlipayTradePagePayRequest request=new AlipayTradePagePayRequest();
        request.setReturnUrl(aliPay.getReturn_url());
        request.setNotifyUrl(aliPay.getNotify_url());
        return request;
    }

    /**
    * 獲取配置文件信息
    */
    public AliPayProperties getConfig() {
        return aliPay;
    }
}