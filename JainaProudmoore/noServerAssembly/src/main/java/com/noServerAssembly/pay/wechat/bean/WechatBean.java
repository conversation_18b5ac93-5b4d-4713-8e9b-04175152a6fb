package com.noServerAssembly.pay.wechat.bean;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import org.springframework.context.annotation.Bean;

/**
 * @ClassNAME WechatBean
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/20 13:18
 */
//@Configuration
public class WechatBean {
    /**
     * 商户号
     */
    public static String merchantId = "";
    /**
     * 商户API私钥路径
     */
    public static String privateKeyPath = "";
    /**
     * 商户证书序列号
     */
    public static String merchantSerialNumber = "";
    /**
     * 商户APIV3密钥
     */
    public static String apiV3key = "";

    @Bean
    public Config config() {
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(merchantId)
                .privateKeyFromPath(privateKeyPath)
                .merchantSerialNumber(merchantSerialNumber)
                .apiV3Key(apiV3key)
                .build();
    }

    @Bean
    public JsapiService jsapiService(Config config) {
        return new JsapiService.Builder().config(config).build();
    }

}
