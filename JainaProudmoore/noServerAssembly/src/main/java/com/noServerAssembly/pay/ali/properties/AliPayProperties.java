package com.noServerAssembly.pay.ali.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 项目名称: pay_core
 *
 * @ClassName CpAliPayProperties
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/5/7 17:01
 */
@Data
@ConfigurationProperties(prefix = "ali.pay")
public class AliPayProperties {
    // 商户appid
    public String app_id;
    // 服务器异步通知页面路径 需http://或者https://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
    public String notify_url;
    // 页面跳转同步通知页面路径 需http://或者https://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问 商户可以自定义同步跳转地址
    public String return_url;
    // 请求网关地址
    public String url = "https://openapi.alipay.com/gateway.do";
    // 编码
    public String charset = "UTF-8";
    // 返回格式
    public String format = "json";
    // 支付宝公钥
    public String alipayPublicKey;
    // 日志记录目录
    public String log_path = "/log";
    // RSA2
    public String signType = "RSA2";

    public String privateKey;

}