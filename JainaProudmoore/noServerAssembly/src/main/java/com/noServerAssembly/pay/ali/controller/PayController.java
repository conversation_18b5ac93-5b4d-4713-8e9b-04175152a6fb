package com.noServerAssembly.pay.ali.controller;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 项目名称: pay_core
 *
 * @ClassName DemoController
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/5/7 17:50
 */
@RestController
@Slf4j
public class PayController {

    @Resource
    @Lazy
    private AlipayClient alipayClient;
    @Resource
    @Lazy
    private AlipayTradePagePayRequest request;

    @GetMapping("/pay")
    public String d1() throws AlipayApiException {
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", "20210817010101004");
        bizContent.put("total_amount", 0.01);
        bizContent.put("subject", "测试商品");
        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
        request.setBizContent(bizContent.toString());
        AlipayTradePagePayResponse response = alipayClient.pageExecute(request);
        System.out.println(response.getBody());
        return response.getBody();
    }
}