package com.noServerAssembly.filter;



import com.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.servlet.ServletFileUpload;


import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

/**
 * 默认的access filter
 * 主要用于调试时打印访问日志
 * 可由配置文件的配置控制在filter registry生成注册
 *
 * <AUTHOR>
 * @date 2021/7/26
 */
@Slf4j
public class DefaultAccessFilter implements Filter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        ServletRequest requestWrapper = null;
        try {
            HttpServletRequest request = (HttpServletRequest) servletRequest;
            String requestMethod = request.getMethod();
            log.info("请求方式:【{}】", requestMethod);
            log.info("请求地址:【{}】", request.getRequestURI());
            log.info("请求来源:【{}】", IpUtils.getRealIp(request));
            if ("POST".equals(requestMethod)) {
                if (ServletFileUpload.isMultipartContent(request)) {
                    log.info("请求包含文件");
                } else {
                    requestWrapper = new BodyReaderHttpServletRequestWrapper(
                            (HttpServletRequest) servletRequest);
                    log.info("请求体:【{}】", ((BodyReaderHttpServletRequestWrapper) requestWrapper).getBodyStr());
                }
            } else {
                log.info("请求参数:【{}】", getParameterNames(request));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (null == requestWrapper) {
            filterChain.doFilter(servletRequest, servletResponse);
        } else {
            filterChain.doFilter(requestWrapper, servletResponse);
        }
    }

    private String getParameterNames(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            stringBuilder.append(entry.getKey()).append("=");
            stringBuilder.append(Arrays.toString(entry.getValue())).append(", ");
        }
        return stringBuilder.length() > 2 ? stringBuilder.substring(0, stringBuilder.length() - 2) : "";
    }

}