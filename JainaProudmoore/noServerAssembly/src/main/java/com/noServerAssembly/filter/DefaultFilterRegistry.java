package com.noServerAssembly.filter;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.*;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * <AUTHOR>
 * @date 2021/7/28
 */
@Configuration
@Slf4j
public class DefaultFilterRegistry {

    @Bean
    @Conditional(LoggingAccessCheck.class)
    public FilterRegistrationBean<DefaultAccessFilter> registryAccessFilter() {
        FilterRegistrationBean<DefaultAccessFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(new DefaultAccessFilter());
        filterRegistrationBean.setOrder(1);

        return filterRegistrationBean;
    }

    private static class LoggingAccessCheck implements Condition {
        @Override
        public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
            Environment environment = context.getEnvironment();
            String loggingAccess = environment.getProperty("logging.method");
            return Boolean.parseBoolean(loggingAccess);
        }
    }

}