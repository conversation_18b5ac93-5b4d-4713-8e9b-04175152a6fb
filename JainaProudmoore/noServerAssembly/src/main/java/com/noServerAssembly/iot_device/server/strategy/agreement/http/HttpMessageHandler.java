package com.noServerAssembly.iot_device.server.strategy.agreement.http;



import com.bo.Result;
import com.noServerAssembly.iot_device.server.enums.MessageSourceEnum;
import com.noServerAssembly.iot_device.server.service.AcceptMessageDataService;
import com.noServerAssembly.iot_device.server.strategy.agreement.BaseAgreement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2023-11-10 15:46
 * @PackageName:com.xmd.xiaomuding.iot.server.controller
 * @ClassName: HttpMessageHandler
 * @Description: TODO
 * @Version 1.0
 */
@RequestMapping("/iot")
@Slf4j
@RestController

public class HttpMessageHandler implements BaseAgreement {


    /**
     * @return com.xmd.xiaomuding.common.core.util.R
     * <AUTHOR>
     * @Description http方式接收到消息转发
     * @Date 15:48 2023-11-10
     * @Param [javax.servlet.http.HttpServletRequest, java.lang.Object]
     */

    @PostMapping("/acceptMessage")
    public Result acceptMessage(HttpServletRequest request, Object message) {
        return Result.ok(AcceptMessageDataService.acceptMessage(MessageSourceEnum.HTTP, request, message));
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //TODO HTTP目前无初始化要求 什么都不实现
     * @Date 17:21 2023-11-13
     * @Param []
     */
    @Override
    public void init() {

    }
}
