package com.noServerAssembly.iot_device.server.strategy.business.message_forward;

import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2023-11-10 14:44
 * @PackageName:com.xmd.xiaomuding.iot.server.strategy.device
 * @ClassName: DeviceHandlerService
 * @Description: 设备消息转发相关接口
 * @Version 1.0
 */
public interface DeviceMessageForwardService<MessageAccept extends Object> {

    Log log = LogFactory.get(DeviceMessageForwardService.class);

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description //数据转发
     * @Date 14:47 2023-11-10
     * @Param [MessageAccept]
     */
    default Object forwardMessage(MessageAccept message) {
        log.info("【{}】接收到消息:【{}】", this.getClass().getSimpleName(), JSONObject.toJSON(message));
        //进行消息验证 消息的可靠性及准确性
        if (checkedMessage(message)) {
            log.info("【{}】消息验证通过", this.getClass().getSimpleName());
            //消息验证没问题之后 进行消息转发
            return doForwardMessage(message);
        }
        //消息校验失败错误处理
        return doCheckedErrorHandler();
    }

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description //数据校验失败处理逻辑 子类若需要自行处理实现即可
     * @Date 14:47 2023-11-10
     * @Param [MessageAccept]
     */
    default Object doCheckedErrorHandler() {
        log.info("【{}】消息验证失败", this.getClass().getSimpleName());
        throw new RuntimeException("消息校验失败");
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description //消息校验 每个设备消息需要自行校验
     * @Date 14:49 2023-11-10
     * @Param [MessageAccept]
     */
    boolean checkedMessage(MessageAccept message);

    /**
     * @return boolean
     * <AUTHOR>
     * @Description //消息转发 每个消息自行转发
     * @Date 14:49 2023-11-10
     * @Param [MessageAccept]
     */
    Object doForwardMessage(MessageAccept message);


    /**
     * @return boolean
     * <AUTHOR>
     * @Description //校验当前消息是否为我的设备 默认返回Null 如需自行校验请实现此方法
     * @Date 15:27 2023-11-10
     * @Param [MessageAccept]
     */
    default Boolean checkedIsMyMessage(HttpServletRequest request,MessageAccept message) {
        return null;
    }

}
