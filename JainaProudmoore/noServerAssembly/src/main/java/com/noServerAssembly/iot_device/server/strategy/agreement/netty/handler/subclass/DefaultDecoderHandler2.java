package com.noServerAssembly.iot_device.server.strategy.agreement.netty.handler.subclass;


import com.noServerAssembly.iot_device.server.annotation.TcpPortBinding;
import com.noServerAssembly.iot_device.server.strategy.agreement.netty.handler.AbstractDecoderHandlerServer;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-11-13 9:45
 * @PackageName:com.xmd.xiaomuding.iot.server.strategy.agreement.netty.handler
 * @ClassName: DefaultDecoderHandler
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@TcpPortBinding(port = {9897, 9898},soft = 2)
@Service
public class DefaultDecoderHandler2 extends AbstractDecoderHandlerServer {

    @Override
    public void doHandlerMessage(ChannelHandlerContext ctx, Object msg) {
        System.out.println("DefaultDecoderHandler2---");
        //TODO 处理消息。。。。。
    }

}
