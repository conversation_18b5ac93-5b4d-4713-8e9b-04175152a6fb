package com.noServerAssembly.iot_device.server.strategy.agreement.netty.handler;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @Date 2023-11-13 9:55
 * @PackageName:com.xmd.xiaomuding.iot.server.strategy.agreement.netty.handler
 * @ClassName: DecoderHandlerServer
 * @Description: 消息接受监听抽象类
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractDecoderHandlerServer extends ChannelInboundHandlerAdapter {

    /**
     * 排序字段 责任链处理中执行链式排序 默认是0 soft越小越优先执行 优先注解 若配置文件有 则会被配置文件覆盖
     */
    protected Integer soft = 0;


    /**
     * 消息接受处理器
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        try {
            log.info("处理器【{}】接受到消息", this.getClass().getSimpleName());
            //执行具体的处理逻辑
            doHandlerMessage(ctx, msg);
            //消息传递到下一个处理器
            ctx.fireChannelRead(msg);
        } catch (Exception e) {
            log.error("消息处理报错:【{}】", e.getMessage());
        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //具体的处理逻辑 每个子类单独实现消息处理逻辑
     * @Date 16:58 2023-11-13
     * @Param [io.netty.channel.ChannelHandlerContext, MessageType]
     */
    protected abstract void doHandlerMessage(ChannelHandlerContext ctx, Object msg) throws InterruptedException;


    /**
     * 设置排序
     */
    public void setSoft(Integer soft) {
        this.soft = soft;
    }

    /**
     * 获取排序
     */
    public Integer getSoft() {
        return this.soft;
    }


}
