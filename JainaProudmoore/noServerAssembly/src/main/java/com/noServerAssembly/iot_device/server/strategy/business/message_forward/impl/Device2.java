package com.noServerAssembly.iot_device.server.strategy.business.message_forward.impl;

import com.noServerAssembly.iot_device.server.strategy.business.message_forward.DeviceMessageForwardService;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2023-11-10 15:49
 * @PackageName:com.xmd.xiaomuding.iot.server.strategy.device.impl
 * @ClassName: CameraDevice
 * @Description: 摄像头相关
 * @Version 1.0
 */
public class Device2 implements DeviceMessageForwardService<String> {


    /**
     * @return boolean
     * <AUTHOR>
     * @Description //摄像头数据校验
     * @Date 15:51 2023-11-10
     * @Param [com.xmd.xiaomuding.iot.server.vo.CameraVo]
     */
    @Override
    public boolean checkedMessage(String message) {
        //TODO 示例
        if (message.length() == 12) {
            throw new RuntimeException("当前设备接受到消息为12位");
        }
        return Boolean.TRUE;
    }

    @Override
    public Object doForwardMessage(String message) {
        //TODO 次处可做消息处理 然后转发到对应的消息里面
        //MqttUtils.sendToMqtt(JSONObject.toJSONString(message), "CAMERA-TOPIC");
        return "success";
    }

    @Override
    public Boolean checkedIsMyMessage(HttpServletRequest request, String message) {
        //TODO 示例
        if (StringUtils.isNotBlank(request.getHeader("DE"))) {
            return Boolean.TRUE;
        }
        if (message.length() == 12 && message.startsWith("DE")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
