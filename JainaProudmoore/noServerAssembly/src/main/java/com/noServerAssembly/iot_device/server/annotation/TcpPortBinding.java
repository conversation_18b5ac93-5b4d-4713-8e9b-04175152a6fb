package com.noServerAssembly.iot_device.server.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2023-11-13 9:39
 * @PackageName:com.xmd.xiaomuding.iot.server.annotation
 * @ClassName: PortBinding
 * @Description: 端口绑定
 * @Version 1.0
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface TcpPortBinding {


    int[] port() default {};


    /**
     * 排序
     */
    int soft() default 0;
}
