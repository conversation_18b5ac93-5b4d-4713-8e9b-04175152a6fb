package com.noServerAssembly.iot_device.server.service;

import cn.hutool.extra.spring.SpringUtil;
import com.noServerAssembly.iot_device.server.enums.MessageSourceEnum;
import com.noServerAssembly.iot_device.server.strategy.business.message_forward.DeviceMessageForwardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-11-10 15:18
 * @PackageName:com.xmd.xiaomuding.iot.server.service
 * @ClassName: AcceptMessageDataService
 * @Description: 消息处理类 主要与业务对接
 * @Version 1.0
 */
@Service
@Slf4j
public class AcceptMessageDataService {

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description //消息接受处理类 需要mq转发到其他业务线 需要根据设备不同进行消息校验
     * @Date 15:20 2023-11-10
     * @Param [javax.servlet.http.HttpServletRequest, java.lang.Object]
     */
    public static Object acceptMessage(MessageSourceEnum messageSourceEnum, HttpServletRequest request, Object message) {
        log.info("来源:【{}】-接收到设备推送消息:【{}】--", messageSourceEnum, message);
        //接受到消息为空直接抛出异常
        if (Objects.isNull(message)) {
            throw new RuntimeException("设备接收到消息为空！");
        }
        //TODO 此处不清楚怎么校验消息来源是需要处理哪个设别 先统一处理
        return checkedMessage(request, message).forwardMessage(message);
    }


    /**
     * @return com.xmd.xiaomuding.iot.server.strategy.device.DeviceMessageForwardService
     * <AUTHOR>
     * @Description //根据消息查找对应的设备处理类
     * @Date 15:48 2023-11-10
     * @Param [javax.servlet.http.HttpServletRequest, java.lang.Object]
     */
    private static DeviceMessageForwardService checkedMessage(HttpServletRequest request, Object message) {
        //TODO 此处根据消息ti message 匹配规则 校验
        return SpringUtil.getBeansOfType(DeviceMessageForwardService.class)
                .values()
                .stream()
                .filter(x -> {
                    //如果设备子类没发判断当时是否为自己的消息 则根据泛型判断
                    if (Objects.isNull(x.checkedIsMyMessage(request, message))) {
                        //根据当前实现类泛型获取泛型属性
                        ParameterizedType parameterizedType = (ParameterizedType) x.getClass().getGenericInterfaces()[0];
                        //获取所有的泛型 消息校验只有一个泛型
                        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                        return actualTypeArguments[0].getTypeName().equals(message.getClass().getTypeName());
                    }
                    //如果设别子类又把那判断当前是否为自己的消息 则根据设备判断
                    return x.checkedIsMyMessage(request, message);
                })
                .findFirst()
                .orElseThrow(() -> new RuntimeException("此消息未找到合适的设备接受!"));
    }

}
