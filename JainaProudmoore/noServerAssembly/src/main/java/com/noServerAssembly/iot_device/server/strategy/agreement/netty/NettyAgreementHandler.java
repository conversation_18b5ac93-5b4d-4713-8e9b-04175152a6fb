package com.noServerAssembly.iot_device.server.strategy.agreement.netty;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;

import com.noServerAssembly.iot_device.server.annotation.TcpPortBinding;
import com.noServerAssembly.iot_device.server.config.TcpBingProperties;
import com.noServerAssembly.iot_device.server.strategy.agreement.BaseAgreement;
import com.noServerAssembly.iot_device.server.strategy.agreement.netty.handler.AbstractDecoderHandlerServer;
import com.noServerAssembly.iot_device.server.utils.PoolExecutorInstance;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.InetSocketAddress;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023-10-31 14:42
 * @PackageName:com.xmd.xiaomuding.iot.server.strategy.agreement.impl
 * @ClassName: NettyAgreementHandler
 * @Description: Netty 服务业务处理
 * @Version 1.0
 */
@Service
@Slf4j
public class NettyAgreementHandler implements BaseAgreement {

    /**
     * 存储tcp监听端口与解析器的容器
     */
    private static Map<Integer, Set<AbstractDecoderHandlerServer>> originData = new HashMap<>();

    @Resource
    private TcpBingProperties tcpBingProperties;

    /**
     * @return void
     * <AUTHOR>
     * @Description //netty 初始化绑定相关
     * @Date 9:44 2023-11-13
     * @Param []
     */
    @Override
    public void init() {
        log.info("初始化 TCP server ...start");
        findAllHandlerServer().entrySet().forEach(x ->
                //此处必须要用多线程处理 要不然tcp会阻塞
                PoolExecutorInstance.getThreadPoolExecutor().execute(() -> start(x.getKey(), x.getValue()))
        );
        log.info("初始化 TCP server ...end");
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description //启动服务监听
     * @Date 14:16 2023-11-13
     * @Param [int, java.util.Set<com.xmd.xiaomuding.iot.server.strategy.agreement.netty.handler.AbstractDecoderHandlerServer>]
     */
    public void start(int port, Set<AbstractDecoderHandlerServer> handlerServers) {
        // 连接处理group
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        // 事件处理group
        EventLoopGroup workerGroup = new NioEventLoopGroup();
        // 创建ServerBootStrap实例
        ServerBootstrap bootstrap = new ServerBootstrap();
        //设置两个线程组
        bootstrap.group(bossGroup, workerGroup)
                //设置并绑定服务端的channel
                .channel(NioServerSocketChannel.class)
                // 保持连接数
                .option(ChannelOption.SO_BACKLOG, 1024)
                // 有数据立即发送
                .childOption(ChannelOption.TCP_NODELAY, true)
                // 保持连接
                .childOption(ChannelOption.SO_KEEPALIVE, true)
                //监听多个端口，设置了客户端连接socket属性。
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel sc) {
                        ChannelPipeline p = sc.pipeline();
                        //将端口绑定的处理器放入解码器的尾端
                        handlerServers.stream()
                                //按照设定的排序
                                .sorted((a, b) -> a.getSoft() - b.getSoft())
                                .forEach(x -> {
                                    try {
                                        //测试判断此处需要校验 已经存在的解码器 需要添加必须是重新实例化的 否则会报错 所以不能用bean对象
                                        p.addLast(x.getClass().newInstance());
                                    } catch (Exception e) {
                                        log.error("实例化对象失败:【{}】", e.getMessage());
                                    }
                                });
                    }
                });
        //监听端口
        try {
            // 绑定端口
            ChannelFuture cf = bootstrap.bind(new InetSocketAddress(port)).sync();
            if (cf.isSuccess()) {
                log.info("netty 启动成功，端口：【{}】", port);
            } else {
                log.info("netty 启动失败，端口：【{}】", port);
            }
            // 等待服务监听端口关闭,就是由于这里会将线程阻塞，导致无法发送信息，所以我这里开了线程
            cf.channel().closeFuture().sync();
        } catch (Exception e) {
            log.error("netty 启动失败，message：【{}】", e.getMessage());
            e.printStackTrace();
        } finally {
            //发送异常关闭
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        }
    }


    /**
     * @return java.util.List<com.xmd.xiaomuding.iot.server.strategy.agreement.netty.handler.AbstractDecoderHandlerServer>
     * <AUTHOR>
     * @Description //查询所有的消息解码处理器
     * @Date 9:57 2023-11-13
     * @Param []
     */
    public Map<Integer, Set<AbstractDecoderHandlerServer>> findAllHandlerServer() {
        log.info("开始查询所有的消息解码处理器");
        //此框架tcp解码器入口为 AbstractDecoderHandlerServer抽象类 后续任何消息处理只需要实现AbstractDecoderHandlerServer 实现其中的消息处理即可
        SpringUtil.getBeansOfType(AbstractDecoderHandlerServer.class)
                //遍历所有的bean对象 （其实不需要定位为bean对象的 只是为了获取方便）
                .keySet()
                .stream()
                .map(SpringUtil::getBean)
                //过滤掉没有端口绑定的bean
                .filter(x -> isAnnotationPresent(x.getClass()))
                .forEach(x -> {
                    //找出所有的port端口与对应的解码bean 放入originData中
                    Arrays.stream(findPort(x.getClass())).forEach(port -> {
                        //向下强转
                        AbstractDecoderHandlerServer server = (AbstractDecoderHandlerServer) x;
                        //设置排序
                        server.setSoft(findSoft(x.getClass()));
                        //已存在当前key value值添加一个bean
                        Set<AbstractDecoderHandlerServer> servers = originData.containsKey(port) ? originData.get(port) : new HashSet<>();
                        servers.add(server);
                        originData.put(port, servers);
                    });
                });
        //TODO 解析配置文件中的绑定关系
        bindYml();
        log.info("查询所有的消息解码处理器完成:【{}】", originData);
        return originData;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //绑定yml中的绑定关系
     * @Date 9:44 2023-11-17
     * @Param []
     */
    public void bindYml() {
        log.info("开始绑定yml配置绑定关系--");
        Set<TcpBingProperties.TcpConfig> tcp = tcpBingProperties.getTcp();
        if (CollectionUtil.isEmpty(tcp)) {
            return;
        }
        log.info("Yml配置绑定关系:【{}】", tcp);
        tcp.forEach(x ->
                //遍历端口绑定的所有实体类路径
                x.getHandlers().stream().forEach(j -> {
                    try {
                        Set<AbstractDecoderHandlerServer> handlerServers = originData.containsKey(x.getPort()) ? originData.get(x.getPort()) : new HashSet<>();
                        AbstractDecoderHandlerServer server;
                        if (j.contains("#")) {
                            String[] split = j.split("#");
                            server = (AbstractDecoderHandlerServer) Class.forName(split[0]).newInstance();
                            try {
                                server.setSoft(Integer.valueOf(split[1]));
                            } catch (Exception e) {
                                throw new RuntimeException("请检查配置文件 帮口绑定排序应为int类型");
                            }
                        } else {
                            server = (AbstractDecoderHandlerServer) Class.forName(j).newInstance();
                        }
                        handlerServers.add(server);
                        originData.put(x.getPort(), handlerServers);
                    } catch (Exception e) {
                        log.error("实例化对象出错:【{}】", e.getMessage());
                    }
                })
        );
    }

    /**
     * 递归判断是否存在TcpPortBinding 注解
     */
    public static Boolean isAnnotationPresent(Class zClass) {
        if (Objects.isNull(zClass)) {
            return Boolean.FALSE;
        }
        return zClass.isAnnotationPresent(TcpPortBinding.class) ? Boolean.TRUE : isAnnotationPresent(zClass.getSuperclass());
    }


    /**
     * 递归查询所有的端口号
     */
    public static int[] findPort(Class zClass) {
        if (zClass.isAnnotationPresent(TcpPortBinding.class)) {
            TcpPortBinding annotation = (TcpPortBinding) zClass.getAnnotation(TcpPortBinding.class);
            return annotation.port();
        }
        return findPort(zClass.getSuperclass());
    }

    /**
     * 递归查询排序处理的顺序
     */
    public static int findSoft(Class zClass) {
        if (zClass.isAnnotationPresent(TcpPortBinding.class)) {
            TcpPortBinding annotation = (TcpPortBinding) zClass.getAnnotation(TcpPortBinding.class);
            return annotation.soft();
        }
        return findSoft(zClass.getSuperclass());
    }


}

