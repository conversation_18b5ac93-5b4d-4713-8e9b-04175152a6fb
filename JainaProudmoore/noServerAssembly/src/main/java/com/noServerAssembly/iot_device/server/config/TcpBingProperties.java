package com.noServerAssembly.iot_device.server.config;

import cn.hutool.extra.spring.SpringUtil;
import com.noServerAssembly.iot_device.server.strategy.agreement.BaseAgreement;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023-11-17 9:21
 * @PackageName:com.xmd.xiaomuding.iot.server.config
 * @ClassName: TcpBingProperties
 * @Description: TODO
 * @Version 1.0
 */
@Data
@Slf4j
//@RefreshScope
@Component
@ConfigurationProperties(prefix = "")
public class TcpBingProperties {

    private Set<TcpConfig> tcp;


    @Data
    public static class TcpConfig {
        private int port;
        private Set<String> handlers;
    }


    /**
     * 初始化netty服務
     */
    @PostConstruct
    public void initNettyServer() {
        // 启动多个 Netty 服务器实例 初始化所有协议前置事项
        log.info("开始初始化所有协议前置事项");
        SpringUtil.getBeansOfType(BaseAgreement.class).keySet().stream()
                .map(SpringUtil::getBean)
                .map(x -> (BaseAgreement) x)
                .forEach(x -> x.init());
        log.info("开始初始化所有协议前置结束");
    }

}
