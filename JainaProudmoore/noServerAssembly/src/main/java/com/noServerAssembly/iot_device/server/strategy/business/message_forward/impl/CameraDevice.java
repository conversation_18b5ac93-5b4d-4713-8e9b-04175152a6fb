package com.noServerAssembly.iot_device.server.strategy.business.message_forward.impl;


import com.noServerAssembly.iot_device.server.strategy.business.message_forward.DeviceMessageForwardService;
import com.noServerAssembly.iot_device.server.vo.CameraVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-11-10 15:49
 * @PackageName:com.xmd.xiaomuding.iot.server.strategy.device.impl
 * @ClassName: CameraDevice
 * @Description: 摄像头相关
 * @Version 1.0
 */
@Service
@Slf4j
public class CameraDevice implements DeviceMessageForwardService<CameraVo> {


    /**
     * @return boolean
     * <AUTHOR>
     * @Description //摄像头数据校验
     * @Date 15:51 2023-11-10
     * @Param [com.xmd.xiaomuding.iot.server.vo.CameraVo]
     */
    @Override
    public boolean checkedMessage(CameraVo message) {
        //TODO 示例
        if (StringUtils.isEmpty(message.getFarmId())) {
            log.info("农场id不能为空");
            return Boolean.FALSE;
        }
        if (!message.getMacNo().startsWith("cm")) {
            log.info("mac地址输入有误");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public Object doForwardMessage(CameraVo message) {
        //TODO 次处可做消息处理 然后转发到对应的消息里面
        //MqttUtils.sendToMqtt(JSONObject.toJSONString(message), "CAMERA-TOPIC");
        return "success";
    }

    @Override
    public Object doCheckedErrorHandler() {
        //TODO 消息校验失败 此处做失败处理 入库、联系三方，直接不处理等等
        return null;
    }
}
