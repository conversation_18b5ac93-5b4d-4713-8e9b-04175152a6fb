package com.noServerAssembly.tree;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024-03-28 11:57
 * @PackageName:com.noServerAssembly.tree
 * @ClassName: TreeVo
 * @Description: TODO
 * @Version 1.0
 */
@Data
@AllArgsConstructor
public class TreeVo {
    private String user;
    private String address;
    private String permission;
    private Integer age;
    private Integer id;
    private Integer pid;
    private Integer soft;

    public TreeVo(Integer id, Integer pid, String user,String address) {
        this.user = user;
        this.id = id;
        this.pid = pid;
    }


    @Override
    public String toString() {
        return "TreeVo{" +
                "user='" + user + '\'' +
                ", address='" + address + '\'' +
                ", permission='" + permission + '\'' +
                ", age=" + age +
                ", id=" + id +
                ", pid=" + pid +
                ", soft=" + soft +
                '}';
    }
}
