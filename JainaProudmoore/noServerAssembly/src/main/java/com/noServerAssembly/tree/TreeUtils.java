package com.noServerAssembly.tree;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-03-28 11:49
 * @PackageName:com.noServerAssembly.tree
 * @ClassName: TreeUtil
 * @Description: TODO
 * @Version 1.0
 */
public class TreeUtils {


    public static void main(String[] args) {
        List<TreeNode<Integer>> collect = data()
                .stream()
                .map(getNodeFunction())
                .collect(Collectors.toList());
        System.out.println(TreeUtil.build(collect, -1));

    }


    @NotNull
    private static Function<TreeVo, TreeNode<Integer>> getNodeFunction() {
        // 返回一个Lambda表达式，实现了Function接口，接受一个XmdFinanceSysMenuEntity对象，返回一个TreeNode<Long>对象
        return menu -> {
            TreeNode<Integer> node = new TreeNode<>(); // 创建一个新的树节点
            node.setId(menu.getId()); // 设置节点ID为菜单ID
            node.setName(menu.getUser()); // 设置节点名称为菜单名称
            node.setParentId(menu.getPid()); // 设置父节点ID为菜单的父ID
          //  node.setWeight(menu.getSoft()); // 设置节点的权重（排序）为菜单的排序值
            // 创建一个HashMap来存放菜单的扩展属性
            Map<String, Object> extra = new HashMap<>();
            // 添加菜单的各种属性到扩展属性Map中
            //地址
            extra.put("address", menu.getAddress());
            //权限名
            //extra.put("permission", menu.getPermission());
            // 将扩展属性Map设置到树节点的extra属性中
            node.setExtra(extra);
            return node; // 返回构建好的树节点对象
        };
    }

    public static List<TreeVo> data() {
        List<TreeVo> data = new ArrayList<>();
        data.add(new TreeVo(0, -1, "顶节点","顶节点的地址"));
        data.add(new TreeVo(1, 0, "菜单1","这个是菜单1的描述"));
        data.add(new TreeVo(2, 0, "菜单2","这个是菜单2的描述"));
        data.add(new TreeVo(3, 1, "菜单3","这个是菜单3的描述"));
        data.add(new TreeVo(4, 1, "菜单4","这个是菜单4的描述"));
        data.add(new TreeVo(5, 1, "菜单5","这个是菜单5的描述"));
        data.add(new TreeVo(6, 2, "菜单6","这个是菜单6的描述"));
        data.add(new TreeVo(7, 2, "菜单7","这个是菜单7的描述"));
        // data.add();
        return data;
    }
}
