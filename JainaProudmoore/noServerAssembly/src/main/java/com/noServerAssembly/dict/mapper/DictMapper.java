package com.noServerAssembly.dict.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.noServerAssembly.dict.entity.DictEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DictMapper extends BaseMapper<DictEntity> {
    @Select("select code,value,pid from interactive_dict where pid = (select id from interactive_dict where code = #{code})")
    List<DictEntity> queryDict(String code);
}
