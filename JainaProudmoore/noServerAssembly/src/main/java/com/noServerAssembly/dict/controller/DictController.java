package com.noServerAssembly.dict.controller;


import cn.hutool.core.bean.BeanUtil;

import com.bo.Result;
import com.noServerAssembly.dict.mapper.DictMapper;
import com.noServerAssembly.dict.model.DictDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/dict")
@Api(value = "DictController", tags = "字典相关")
public class DictController {

    @Resource
    private DictMapper dictMapper;


    @GetMapping("/queryDict")
    @ApiOperation("查询字典")
    public Result queryDict(@RequestParam("code") String code) {
       // BeanUtils.copyProperties(dictMapper, code);
        return Result.ok(BeanUtil.copyToList(dictMapper.queryDict(code), DictDto.class));
    }

}
