package com.noServerAssembly.chainofResponsibility.demo;


import com.annotation.WalterChainOfResponsibilityPatternAnnotation;
import com.noServerAssembly.chainofResponsibility.handler.ChainOfResponsibilityPattern;

/**
 * 项目名称: pay_core
 *
 * @ClassName Demo1
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/6/13 14:25
 */
@WalterChainOfResponsibilityPatternAnnotation(chainId = "demo[a-z]*[1-9]*",isPattern = true,order = 3)
public class Demo2 implements ChainOfResponsibilityPattern {
    @Override
    public Object doHandler(Object... objects) {
        System.out.println("demo2 doing -----");
        return "demo2 succcess";
    }
}