package com.noServerAssembly.chainofResponsibility.handler;

/**
 * Functional description:  责任链基础接口
 *
 * @param * @param null
 * @<NAME_EMAIL>
 * @date 2022/6/13 9:14
 * @return
 * @throws
 **/

@FunctionalInterface
public interface ChainOfResponsibilityPattern<R> {

    /**
     * 执行业务
     */
    R doHandler(Object... objects);


    default <U> U oneParamsToPojo(Class<U> uClass, Object... objects) {
        return (U) objects[0];
    }

}
