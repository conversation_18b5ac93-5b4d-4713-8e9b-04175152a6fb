package com.noServerAssembly.chainofResponsibility.handler;


import com.exception.AssemblyCatServiceException;

/**
 * 项目名称: pay_core
 * 责任链抽象接口
 *
 * @ClassName DeafultAbstractChainOfResponsibilityPattern
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/6/22 9:55
 */
public abstract class DefaultAbstractChainOfResponsibilityPattern<R> implements ChainOfResponsibilityPattern {

    @Override
    public R doHandler(Object... objects) {
        String object = (String) objects[0];
        Object object1 = objects[1];
        Object object2 = objects[2];
        throw new AssemblyCatServiceException("子类需实现本方法");
    }


    public <P> R doHandler(P p) {
        throw new AssemblyCatServiceException("子类需实现本方法");
    }
}