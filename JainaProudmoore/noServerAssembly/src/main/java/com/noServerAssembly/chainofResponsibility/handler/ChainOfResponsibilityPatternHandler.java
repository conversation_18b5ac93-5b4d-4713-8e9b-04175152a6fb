package com.noServerAssembly.chainofResponsibility.handler;

import cn.hutool.extra.spring.SpringUtil;

import com.annotation.WalterChainOfResponsibilityPatternAnnotation;
import org.reflections.Reflections;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目名称: pay_core
 *
 * @ClassName ChainOfResponsibilityPatternHandler
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/6/13 9:43
 */
@Configuration
public class ChainOfResponsibilityPatternHandler {
    public static final String BASE_PACKAGE = "com.noServerAssembly.*";
    /**
     * 属性私有 不允许外部添加过程中在修改
     */
    private static final Map<String, List<ChainOfResponsibilityPattern>> crpMap = new HashMap<>();
    /**
     * 初始化容器数据过滤
     */
    private static final Map<String, String> crpCheckedMap = new HashMap<>();
    /**
     * 默认value值
     */
    private static final String VALUE = "A";


    /**
     * 提供外部访问接口
     */
    public static Map<String, List<ChainOfResponsibilityPattern>> getCrpMap() {
        return new HashMap<>(crpMap);
    }

    /**
     * Functional description:  初始化容器
     *
     * @param * @param
     * @return void
     * @throws
     * @<NAME_EMAIL>
     * @date 2022/6/13 9:43
     **/
    @PostConstruct
    public void init() {
        new Reflections(BASE_PACKAGE).getTypesAnnotatedWith(WalterChainOfResponsibilityPatternAnnotation.class)
                .stream()
                .map(SpringUtil::getBean)
                .forEach(bean -> {
                    Class<?> aClass = bean.getClass();
                    //获取到注解信息
                    WalterChainOfResponsibilityPatternAnnotation crpAnnotation = aClass.getAnnotation(WalterChainOfResponsibilityPatternAnnotation.class);
                    //校验参数的正确性
                    if (!crpAnnotation.isPattern()) {
                        String key = Arrays.asList(crpAnnotation.group()).stream().collect(Collectors.joining()) + crpAnnotation.chainId();
                        if (crpCheckedMap.containsKey(key)) {
                            throw new RuntimeException("Bean[" + aClass.getName() + "]已存在相同的过滤链! 请检查注解参数是否正确");
                        }
                        crpCheckedMap.put(key, VALUE);
                    }
                    //获取组信息
                    Arrays.stream(crpAnnotation.group()).forEach(group -> {
                        List<ChainOfResponsibilityPattern> cSet = crpMap.containsKey(group) ? crpMap.get(group) : new ArrayList<>();
                        cSet.add((ChainOfResponsibilityPattern) bean);
                        crpMap.put(group, cSet);
                    });
                });
        //删除校验缓存数据
        crpCheckedMap.clear();
    }
}