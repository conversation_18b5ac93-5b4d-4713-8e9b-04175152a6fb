package com.noServerAssembly.chainofResponsibility;

import cn.hutool.core.collection.CollectionUtil;

import com.annotation.WalterChainOfResponsibilityPatternAnnotation;
import com.exception.AssemblyCatServiceException;
import com.noServerAssembly.chainofResponsibility.handler.ChainOfResponsibilityPattern;
import com.noServerAssembly.chainofResponsibility.handler.ChainOfResponsibilityPatternHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.noServerAssembly.chainofResponsibility.constants.CrpConstant.DEFAULT_GROUP;


/**
 * 项目名称: pay_core
 *
 * @ClassName ExecuteService
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/6/13 14:34
 */
@Slf4j
public class ExecuteService {

    private static ThreadLocal<List> RESULT = new ThreadLocal();

 // zhushi
    private static void addResult(Object o) {
        List list = RESULT.get() == null ? new ArrayList() : RESULT.get();
        list.add(o);
        RESULT.set(list);
    }

    /**
     * @param chainId: 执行链编码
     * @param args:    请求参数
     */
    public static Object doChain(String chainId, Object... args) {
        return doChain(chainId, CollectionUtil.newArrayList(), args);
    }

    /**
     * @param chainId: 执行链编码
     * @param group:   组信息集合
     * @param args:    请求参数
     */
    public static Object doChain(String chainId, String group, Object... args) {
        return doChain(chainId, new ArrayList() {{
            add(group);
        }}, args);
    }

    /**
     * @param chainId: 执行链编码
     * @param groups:  组信息集合
     * @param args:    请求参数
     */
    public static Object doChain(String chainId, List<String> groups, Object... args) {
        try {
            if (StringUtils.isEmpty(chainId)) {
                throw new AssemblyCatServiceException("执行链参数[chainId]不能为空");
            }
            if (CollectionUtil.isEmpty(groups)) {
                groups.add(DEFAULT_GROUP);
            }
            Map<String, List<ChainOfResponsibilityPattern>> crpMap = ChainOfResponsibilityPatternHandler.getCrpMap();
            groups.forEach(group ->
                    crpMap.get(group).stream().sorted((o1, o2) ->
                                    o2.getClass().getAnnotation(WalterChainOfResponsibilityPatternAnnotation.class).order() -
                                            o1.getClass().getAnnotation(WalterChainOfResponsibilityPatternAnnotation.class).order()
                            )
                            .forEach(chain -> doHandler(chain, chainId, args)));
            return RESULT.get();
        } finally {
            RESULT.remove();
        }
    }

    /**
     * 调用业务逻辑
     */
    private static void doHandler(ChainOfResponsibilityPattern chain, String chainId, Object... args) {
        //获取当前责任链的注解信息
        WalterChainOfResponsibilityPatternAnnotation an = chain.getClass().getAnnotation(WalterChainOfResponsibilityPatternAnnotation.class);
        //费正则表达式并且全匹配
        if ((!an.isPattern() && chainId.equals(an.chainId()))
                //正则表达式并且match成功
                || Pattern.compile(an.chainId()).matcher(chainId).matches()) {
            //执行业务
            try {
                addResult(chain.doHandler(args));
            } catch (Exception e) {
                log.error("当前链[{}]业务异常:{}", chain, e.getMessage());
                e.printStackTrace();
                if (an.needThrowException()) {
                    throw e;
                }
            }
        }
    }
}