package com.noServerAssembly.es;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-12-27 15:41
 * @PackageName:com.bot.cat.demo.es
 * @ClassName: UserController
 * @Description: TODO
 * @Version 1.0
 */
//@RestController
//@RequestMapping("/esUser")
public class UserController {
    @Resource
    private UserService userService;

    @Resource
    private UserRepository userRepository;


    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @RequestMapping("/insert")
    public Object insert(@RequestBody EsUser user) {
        //return userRepository.save(user);
        return null;
    }

    @RequestMapping("/find")
    public Object find(@RequestBody EsUser user) {
        //return userRepository.findById(user.getId());
        return null;
    }

    @RequestMapping("/searchSimilar")
    public Object searchSimilar(String address) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("address", address));
        // boolQueryBuilder.must(QueryBuilders.matchQuery("age",30));
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder);
        NativeSearchQuery nativeSearchQuery = nativeSearchQueryBuilder.build();
        SearchHits<EsUser> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsUser.class);
        searchHits.forEach(personSearchHit -> {
            System.out.println(personSearchHit.getContent());
        });
        return searchHits.getSearchHits();
    }

    @RequestMapping("/searchSimilar2")
    public Object searchSimilar2(String address) {
        return userRepository.findAllByAddress(address);
    }


    @RequestMapping("/delete")
    public String delete() {
        EsUser user = new EsUser();
        user.setId("1");
        userService.delete(user);
        return getAll();
    }

    @RequestMapping("/getAll")
    public String getAll() {
        List<EsUser> list = new ArrayList<>();
        Iterable<EsUser> iterable = userService.getAll();
        iterable.forEach(e -> list.add((EsUser) e));
        return list.toString();
    }
}
