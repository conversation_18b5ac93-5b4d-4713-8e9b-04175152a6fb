package com.noServerAssembly.es;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * <AUTHOR>
 * @Date 2023-12-27 15:38
 * @PackageName:com.bot.cat.demo.es
 * @ClassName: EsUser
 * @Description: TODO
 * @Version 1.0
 */
@Data
@Document(indexName = "esuser")
public class EsUser {
    @Id
    private String id; // id
    @Field(store = true, type = FieldType.Text, analyzer = "ik_smart")
    private String username; // 用户名
    @Field(store = true, type = FieldType.Keyword)
    private String password; // 密码
    @Field(store = true, type = FieldType.Text, analyzer = "ik_smart")
    private String address; // 地址
}
