package com.noServerAssembly.es;

import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-12-27 15:39
 * @PackageName:com.bot.cat.demo.es
 * @ClassName: UserRepository
 * @Description: TODO
 * @Version 1.0
 */

public interface UserRepository extends ElasticsearchRepository<EsUser, String> {


    List<EsUser> findAllByAddress(String address);
}
