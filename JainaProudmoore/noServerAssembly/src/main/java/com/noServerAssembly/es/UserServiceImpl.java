package com.noServerAssembly.es;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2023-12-27 15:40
 * @PackageName:com.bot.cat.demo.es
 * @ClassName: UserServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
//@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Override
    public EsUser save(EsUser user) {
        //return userRepository.save(user);
        return null;
    }

    @Override
    public void delete(EsUser user) {
       // userRepository.delete(user);
    }

    @Override
    public Iterable<EsUser> getAll() {
       // return userRepository.findAll();
        return null;
    }

}