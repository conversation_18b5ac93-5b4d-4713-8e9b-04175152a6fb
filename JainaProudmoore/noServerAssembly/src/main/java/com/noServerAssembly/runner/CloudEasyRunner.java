package com.noServerAssembly.runner;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * 项目名称: springboot-base-project
 *
 * @ClassName CloudEasyRunner
 * @Description 项目初始化。。。
 * @<NAME_EMAIL>
 * @Date 2022/2/14 15:14
 */
@Component
@Slf4j
public class CloudEasyRunner implements ApplicationRunner {
    public static final String PATH = "com.bot.*";
    public static Map<SupplierEnum, Object> cloudMap = new HashMap<>();

    @Override
    public void run(ApplicationArguments args){
        new Reflections(PATH).getTypesAnnotatedWith(Cloud.class).stream()
                .map(SpringUtil::getBean)
                .forEach(bean -> cloudMap.put(bean.getClass().getAnnotation(Cloud.class).supplier(), bean));
        log.info("云厂商实例初始化完成: [{}]", cloudMap);
    }
}