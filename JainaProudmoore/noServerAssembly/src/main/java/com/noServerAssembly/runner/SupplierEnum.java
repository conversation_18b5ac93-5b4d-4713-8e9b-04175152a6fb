package com.noServerAssembly.runner;


import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 项目名称: springboot-base-project
 *
 * @ClassName SupplierEnum
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/2/17 9:23
 */
@NoArgsConstructor
@Getter
public enum SupplierEnum {
    /**
     * 空占位符
     */
    EMPTY ("", "空占位符"),
    /**
     * 天翼云
     */
    CT_YUN("vc.code.ctyun", "天翼云");

    /**
     * 编码
     */
    private String code;

    /**
     * 信息
     */
    private String desc;


    SupplierEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SupplierEnum getEnum(String value) {
        SupplierEnum[] supplierEnums = values();
        for (SupplierEnum supplierEnum : supplierEnums) {
            if ((supplierEnum.code).equals(value)) {
                return supplierEnum;
            }
        }
        return null;
    }
}