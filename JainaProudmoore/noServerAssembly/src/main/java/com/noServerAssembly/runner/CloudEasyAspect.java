package com.noServerAssembly.runner;

import cn.hutool.core.util.ObjectUtil;

import com.bo.Result;
import com.exception.AssemblyCatServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Objects;


/**
 * Functional description:  云厂商切面  注入beam
 *
 * @param * @param null
 * @<NAME_EMAIL>
 * @date 2022/2/14 16:42
 * @return
 * @throws
 **/
@Aspect
//@Component
@Slf4j
public class CloudEasyAspect {
    public static final String SUPPLIER_CODE = "supplierCode";


    @Pointcut("execution(* com..*Controller.*(..))")
    private void serviceAspect() {
    }

    @Around(value = "serviceAspect()")
    public Result around(ProceedingJoinPoint point) {
        try {
            try {
                return Arrays.stream(point.getArgs())
                        //空参校验
                        .filter(Objects::nonNull)
                        //获取其中的参数事都包含 supplierCode 字段 包含并返回该字段
                        .map(arg -> {
                            HashMap<Field, Object> map = new HashMap<>();
                            //获取该类属性中是否包含supplierCode字段
                            Field field = Arrays.stream(arg.getClass().getDeclaredFields())
                                    .filter(filed -> filed.getName().equals(SUPPLIER_CODE))
                                    .findFirst().orElse(null);
                            if (ObjectUtil.isNull(field)) {
                                //获取父类属性中是否包含supplierCode字段
                                field = Arrays.stream(arg.getClass().getSuperclass().getDeclaredFields())
                                        .filter(filed -> filed.getName().equals(SUPPLIER_CODE))
                                        .findFirst().get();
                            }
                            map.put(field, arg);
                            return map;
                        })
                        //校验改字段值是否为空
                        .filter(MapUtils::isNotEmpty)
                        //根据该字段进行赋值
                        .map(map -> {
                            try {
                                Field field = (Field) map.keySet().toArray()[0];
                                field.setAccessible(true);
                                //转化成需要的接口
//                                CloudEasy cloudBean = (CloudEasy) CloudEasyRunner.cloudMap.get(SupplierEnum.getEnum((String) field.get(map.get(field))));
//                                if (ObjectUtil.isNull(cloudBean)) {
//                                    throw new AllCloudServiceException(ResultErrorEnum.BAS00004);
//                                }
//                                //放入临时副本
//                                CloudInstance.setCloud(cloudBean);
                                return (Result) point.proceed();
                            } catch (Throwable throwable) {
                                log.error("业务执行异常:{}", throwable);
                                throw new AssemblyCatServiceException("error");
                            }
                        }).findFirst().orElse(null);
            } catch (AssemblyCatServiceException e) {
                throw e;
            }
        } finally {
            //释放threadLocal
           // CloudInstance.remove();
            //释放锁资源
        }
    }
}