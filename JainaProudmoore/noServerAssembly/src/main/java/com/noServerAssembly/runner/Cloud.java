package com.noServerAssembly.runner;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Functional description:
 *
 * @param * @param null
 * @<NAME_EMAIL>
 * @date 2022/2/14 15:15
 * @return
 * @throws
 **/
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Cloud {
    /**
     * 云厂商编码
     */
    SupplierEnum supplier() default SupplierEnum.EMPTY;
}
