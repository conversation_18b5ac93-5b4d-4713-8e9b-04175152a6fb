package com.noServerAssembly.fatory;

import cn.hutool.extra.spring.SpringUtil;

import com.annotation.CatAnnotationFactory;
import com.enums.FactoryObjectEnum;
import com.noServerAssembly.fatory.demo.People;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: renBo.ren <EMAIL>
 * @Description:
 * @Data: Created in 11:57 2022/9/5
 */
@Slf4j
public class AnnotationFactory {

    public static Map<String, List<People>> serviceMap = new HashMap<>();


    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 根据编码查询符合条件的流量主
     * @Date 11:58 2022/9/5
     * @Param
     **/
    public static List<People> create(String masterCode) {
        if (serviceMap.containsKey(masterCode)) {
            return serviceMap.get(masterCode);
        }
        List<People> service = SpringUtil.getBeansOfType(People.class)
                .values().stream()
                .filter(x -> x.getClass().isAnnotationPresent(CatAnnotationFactory.class))
                .filter(x ->
                        Arrays.asList(x.getClass().getAnnotation(CatAnnotationFactory.class)
                                .value()).contains(FactoryObjectEnum.getEnumByStatus(masterCode))
                )
                .collect(Collectors.toList());
        serviceMap.put(masterCode, service);
        return service;
    }

    /**
     * @param masterCode
     * @ClassName: AnnotationFactory
     * @Description: 执行
     * @Author: <EMAIL>
     * @Date: 2023/2/2 17:36
     * @return:
     */
    public void doHandle(String masterCode) {
        serviceMap.get(masterCode).forEach(People::eat);
    }

}
