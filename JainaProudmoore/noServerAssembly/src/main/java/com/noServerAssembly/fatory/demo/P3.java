package com.noServerAssembly.fatory.demo;


import com.annotation.CatAnnotationFactory;
import com.enums.FactoryObjectEnum;
import org.springframework.stereotype.Service;

/**
 * @ClassNAME P1
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/2 17:27
 */
@Service
@CatAnnotationFactory({FactoryObjectEnum.O_2,FactoryObjectEnum.O_3})
public class P3 implements People{
    @Override
    public void eat() {

    }
}
