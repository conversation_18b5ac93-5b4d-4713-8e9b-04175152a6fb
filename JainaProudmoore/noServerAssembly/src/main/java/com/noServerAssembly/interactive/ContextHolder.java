package com.noServerAssembly.interactive;


import com.bo.LoginUser;

/**
 * @Author: renBo.ren <EMAIL>
 * @Description:
 * @Data: Created in 18:16 2022/8/15
 */
public class ContextHolder {

    /**
     * 用户相关 保存当前登录用户信息
     */
    private static final InheritableThreadLocal<LoginUser> CURRENT_USER = new InheritableThreadLocal<>();

    public static void setLoginUser(LoginUser info) {
        CURRENT_USER.set(info);
    }
    /**
     *  获取当前副本中得用户对象
     */
    public static LoginUser getLoginUser() {
        return CURRENT_USER.get();
    }

    /**
     *  获取当前副本中得uid
     */
    public static String getLoginUserId() {
        return CURRENT_USER.get().getUserId();
    }

    /**
     *  删除当前副本得用户信息
     */
    public static void removeLoginUser() {
        CURRENT_USER.remove();
    }
}
