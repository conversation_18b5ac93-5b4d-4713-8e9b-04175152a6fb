package com.noServerAssembly.interactive;

import cn.hutool.core.util.ObjectUtil;

import com.bo.LoginUser;
import com.bo.UserInfo;
import com.enums.ErrorEnum;
import com.exception.AssemblyCatServiceException;
import com.utils.RedisUtil;
import com.utils.ServletUtils;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Service
public class TokenService {
    private final static long expireTime = 100;

    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 根据用户信息生成token
     * @Date 14:55 2022/8/15
     * @Param
     **/
    public String createToken(UserInfo userInfo) {
        return Optional.of(userInfo)
                .map(x ->
                        new HashMap<String, Object>() {
                            {
                                // Jwt存储信息  //将用户id放入jwt中
                                put("userId", userInfo.getUserId());
                            }
                        }
                )
                //生成token
                .map(claimsMap -> {
                    String token = JwtUtils.createToken(claimsMap);
                    //token信息存入缓存中 方便token刷新！
                    RedisUtil.set("cat:login:token:".concat(userInfo.getUserId()),
                            LoginUser.createLoginUser(userInfo, token),
                            expireTime, TimeUnit.SECONDS);
                    return token;
                })
                .orElseThrow(() -> new AssemblyCatServiceException(ErrorEnum.IS00001));
    }

    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 获取用户身份信息
     * @Date 18:31 2022/8/15
     * @Param
     **/
    public LoginUser getLoginUser() {
        return getLoginUser(ServletUtils.getRequest());
    }


    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 获取用户身份信息
     * @Date 18:31 2022/8/15
     * @Param
     **/
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        return getLoginUser(SecurityUtils.getToken(request));
    }

    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 获取用户身份信息
     * @Date 18:31 2022/8/15
     * @Param
     **/
    public LoginUser getLoginUser(String token) {
        try {
            if (StringUtils.isNotEmpty(token)) {
                return (LoginUser)RedisUtil.get("TOKEN_REDIS".concat(JwtUtils.getValue(token, "USER_ID")));
            }
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 删除用户缓存信息
     * @Date 18:30 2022/8/15
     * @Param
     **/
    public boolean delLoginUser(LoginUser loginUser) {
        if (ObjectUtil.isNotEmpty(loginUser)) {
            RedisUtil.del("TOKEN_REDIS".concat(loginUser.getUserId()));
        }
        return false;
    }

    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 验证令牌有效期，自动刷新缓存
     * @Date 18:30 2022/8/15
     * @Param
     **/
    public void verifyToken(String token) {
        refreshToken(JwtUtils.getValue(token, "USER_ID"));
    }

    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 刷新令牌有效期
     * @Date 18:30 2022/8/15
     * @Param
     **/
    public void refreshToken(String userId) {
        if (!RedisUtil.hasKey("TOKEN_REDIS".concat(userId))) {
            throw new AssemblyCatServiceException("用户未登录!");
        }
        //用户已登录  直接刷新事件
        RedisUtil.set("TOKEN_REDIS".concat(userId),
                RedisUtil.get("TOKEN_REDIS".concat(userId)),
                expireTime, TimeUnit.SECONDS);
    }
}