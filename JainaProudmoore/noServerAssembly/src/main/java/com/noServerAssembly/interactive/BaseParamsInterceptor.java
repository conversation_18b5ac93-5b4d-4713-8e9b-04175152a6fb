package com.noServerAssembly.interactive;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;


import com.bo.LoginUser;
import com.bo.UserInfo;
import com.utils.UUIDUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;


import static com.constants.BaseFieldConstants.*;


/**
 * @Author: renBo.ren <EMAIL>
 * @Description: 基础字段填充插件
 * @Data: Created in 13:33 2022/8/25
 */
@Slf4j
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class BaseParamsInterceptor implements Interceptor {


    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            Object[] args = invocation.getArgs();
            MappedStatement ms = (MappedStatement) args[0];
            Object parameter = args[1];
            SqlCommandType sqlCommandType = ms.getSqlCommandType();
            if (sqlCommandType.equals(SqlCommandType.INSERT)) {
                if (parameter instanceof Map) {
                    insertList(parameter);
                } else {
                    doInsert(parameter.getClass(), parameter, getUserInfo());
                }
                //入库操作
            } else if (sqlCommandType.equals(SqlCommandType.UPDATE)) {
                if (parameter instanceof Map && CollectionUtil.isNotEmpty(getData(parameter))) {
                    updateList(parameter);
                } else {
                    if (parameter instanceof Map) {
                        ((Map<?, ?>) parameter).forEach((k, v) ->
                                doUpdate(v.getClass(), v, getUserInfo()));
                    } else {
                        //更新操作
                        doUpdate(parameter.getClass(), parameter, getUserInfo());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("拦截器出错: ", e.getMessage());
        }
        return invocation.proceed();
    }

    /**
     * 执行入库操作
     */
    @SneakyThrows
    private void doUpdate(Class<?> aClass, Object parameter, UserInfo userInfo) {
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            switch (field.getName()) {
                case UPDATE_USER_NAME:
                    field.set(parameter, ObjectUtil.isNotNull(userInfo) ? userInfo.getUserId() : SYSTEM);
                    break;
                case UPDATE_USER_ID:
                    field.set(parameter, ObjectUtil.isNotNull(userInfo) ? userInfo.getUserId() : SYSTEM_ID);
                    break;
                case UPDATE_TIME:
                    field.set(parameter, LocalDateTime.now());
                    break;
            }
            //如果是继承关系 父类含有此属性 给父类赋值
            if (ObjectUtil.isNotNull(aClass.getSuperclass())) {
                doUpdate(aClass.getSuperclass(), parameter, userInfo);
            }
        }
    }

    /**
     * 执行更新操作
     */
    @SneakyThrows
    private void doInsert(Class<?> aClass, Object parameter, UserInfo userInfo) {
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            switch (field.getName()) {
                //设置唯一ID
                case ID:
                    field.set(parameter, UUIDUtils.createRandomStr(32));
                    break;
                case CREATE_USER_NAME:
                    field.set(parameter, ObjectUtil.isNotNull(userInfo) ? userInfo.getUserId() : SYSTEM);
                    break;
                case CREATE_USER_ID:
                    field.set(parameter, ObjectUtil.isNotNull(userInfo) ? userInfo.getUserId() : SYSTEM_ID);
                    break;
                case CREATE_TIME:
                    field.set(parameter, LocalDateTime.now());
                    break;
                case DEL_STATUS:
                    field.set(parameter, "0");
                    break;
            }
        }
        //如果是继承关系 父类含有此属性 给父类赋值
        if (ObjectUtil.isNotNull(aClass.getSuperclass())) {
            doInsert(aClass.getSuperclass(), parameter, userInfo);
        }
    }

    /**
     * 获取当前登录用户信息
     */
    private com.bo.UserInfo getUserInfo() {
        LoginUser loginUser = ContextHolder.getLoginUser();
        if (ObjectUtil.isNull(loginUser) || ObjectUtil.isNull(loginUser.getUserInfo())) {
            UserInfo userInfo = new UserInfo();
//            userInfo.setUserId("001");
//            userInfo.setUserAccount("cat");
//            userInfo.setStaffName("球球");
            return userInfo;
        }
        return loginUser.getUserInfo();
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    /**
     * 批量新增
     *
     * @param parameter
     */
    private void insertList(Object parameter) {
        getData(parameter).stream().forEach(x -> this.doInsert(x.getClass(), x, getUserInfo()));
    }

    /**
     * 批量更新
     *
     * @param parameter
     */
    private void updateList(Object parameter) {
        getData(parameter).stream().forEach(x -> this.doUpdate(x.getClass(), x, getUserInfo()));
    }

    /**
     * 获取参数中的数据
     *
     * @param parameter
     * @return
     */
    private List getData(Object parameter) {
        Map map = (Map) parameter;
        Iterator data = map.keySet().iterator();
        while (data.hasNext()) {
            Object obj = map.get(data.next());
            if (obj instanceof List) {
                return (List) obj;
            }
        }
        return null;
    }
}
