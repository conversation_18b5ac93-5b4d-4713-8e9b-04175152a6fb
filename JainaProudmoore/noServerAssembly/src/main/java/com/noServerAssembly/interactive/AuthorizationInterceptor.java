package com.noServerAssembly.interactive;

import cn.hutool.extra.spring.SpringUtil;

import com.bo.LoginUser;
import com.exception.AssemblyCatServiceException;
import com.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> <EMAIL>
 * @Description 服务认证拦截器
 * @Date 9:53 2022/8/16
 * @Param
 * @return
 **/
@Slf4j
@Component
public class AuthorizationInterceptor implements HandlerInterceptor {


    private static TokenService tokenService = SpringUtil.getBean(TokenService.class);

    /**
     * @return
     * <AUTHOR> <EMAIL>
     * @Description 请求认证
     * @Date 9:54 2022/8/16
     * @Param
     **/
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            String requestURI = request.getRequestURI();
            log.info("当前访问请求:【{}】",requestURI);
            //获取请求头中令牌
            String userId = JwtUtils.getValue(request.getHeader("Authorization"), "userId");
            if (!RedisUtil.hasKey("cat:login:token:".concat(userId))) {
                response.setStatus(401);
                throw new AssemblyCatServiceException("你的账户信息已过期,请重新登录!");
            }
            //延长Token时间
            LoginUser loginUser = (LoginUser) RedisUtil.get("cat:login:token:".concat(userId));
            tokenService.verifyToken(loginUser.getToken());
            //将当前登录用户放置到线程副本中
            ContextHolder.setLoginUser(loginUser);
            return true;
        } catch (AssemblyCatServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证token出错：【{}】", e.getMessage());
            e.printStackTrace();
            throw new AssemblyCatServiceException("你的账户信息已过期,请重新登录!");
        }
    }


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
        //删除当前线程副本
        ContextHolder.removeLoginUser();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
        //删除当前线程副本
        ContextHolder.removeLoginUser();
    }
}
