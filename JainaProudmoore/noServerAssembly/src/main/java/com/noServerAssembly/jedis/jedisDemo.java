package com.noServerAssembly.jedis;

import com.alibaba.fastjson.JSONObject;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.params.SetParams;

import javax.sound.midi.Soundbank;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023-07-27 4:41
 * @PackageName:com.xmd.xiaomuding.filebiz.configure.redis
 * @ClassName: Demo
 * @Description: TODO
 * @Version 1.0
 */

public class jedisDemo {


    static String str = "***********#1648882564856233986,***********#1646454235731927041,***********#1651794651927916546,***********#1676561519019335681,***********#1671449190254575618,***********#1671767423173701633,***********#1647029953538510849,***********#1646487683033014273,***********#1651551273685057538,***********#1647888291989291010,***********#1648892565503033345,***********#1647078465693241345,***********#1644205967544229890,***********#1647186308633554945,***********#1649598611859140610,***********#1647885711116599297,***********#1647533951253172226,***********#1649636355807760386,***********#1648249433941082113,***********#1647823040609611778,***********#1647557649184018434,***********#1647510776761380866,***********#1647489694953111554,***********#**********911790593,13571263400#1647434962356142082,13571274557#1649581073249456129,13571274575#1647411043990196226,13629122879#1647456458378792961,13636792583#1680069693831684098,13636889978#1649974919843770370,13689222937#1647198772939026433,13891238248#1647424974255247362,13891252489#1647490792715714562,13891295097#1647090556477468674,15029322241#1647440217425530881,15029407113#1650704244275941377,15091406755#1648634114574385154,15129120989#1647842694167744513,15191949833#1647525678046511106,15229928363#1647167460664504322,15249029328#1647563216433016833,15291213444#1648277503511842817,15291234860#1646043300732157953,15319659829#1671719467266994178,15399307864#1648147099847323649,15529846286#1647539294673137665,15529851079#1648601212977414145,15877544315#1647762148153884673,15891235443#1647919058656165890,15929701750#1647425355495768066,15991214689#1649976877001773058,15991219233#1647888800456376321,17319827611#1647541576795639810,17365626797#1647844604744519682,17691130386#1638026055669112834,17709222432#1494158832823727296,17729289350#1672527671966167041,17734669127#1658098098637701122,17795960602#1649968572312440834,17795961255#1648231180970848257,17868311372#1669318407431720961,18091208224#1647775464839139330,18091212043#1648469459200733185,18091243695#1672200912369004545,18091998968#1669665532653891586,18098018437#1647404515912536066,18098027068#1646834582865838081,18098085596#1670689573049233410,18109125833#1671399828476461058,18161727395#1666710342752591873,18165196592#1646737095438106625,18220244500#1649382370975715329,18292248180#1647103629336203265,18591839283#1648266201827995650,18791217798#1646446129486229505,18791225550#1648174940798414850,18991081825#1646762903649124353,18992225099#103714007,18992239922#1651192626974994433,18992250827#1663022984694976513,19191284579#1669932305814941697,19929340653#1646776341539680258,19929345143#1665679878633873409,19929369899#1670000133090922497,19929400998#1670641085649285121,19929409359#1676793312025878529,19929551082#1648239760990105602,19929552241#1669987466663636993,19929614868#1646730546939121666,19929641288#1647515260687962113,19991070203#1650311716292792321,19991091337#1672587995692003329";

    private static HashMap<String, Object> maps = new HashMap<>();

    //private RedisTemplate<String,String> redisTemplate
    public static void main(String[] args) {
        //Jedis jedis = new Jedis("47.104.30.91", 6899);
        Jedis jedis = new Jedis("119.8.39.19", 6379);
         jedis.auth("atlasNav2024.com");
        jedis.select(4); // 切换到 db4
        //Set<String> keys = jedis.keys("*token:user:userdetail:app*");
        //app:1:18810056855:access_token:c235a960-b290-4e84-bda6-6034cba6db32
        Set<String> keys = jedis.keys("funnel:20250617:RU*");
//        jedis.set("bnbo", "demo111");
//        System.out.println(jedis.get("bnbo"));
//        System.out.println(jedis.keys("*"));
        //Set<String> keys = jedis.keys("app:1:18810056855:access_token:*");
        for (String key : keys) {
            jedis.del(key);
            //System.out.println(key);
            //long seconds = 86400 * 365 * 5;   // 5年
            //jedis.expire(key, (int)seconds);
            //Long ttl = jedis.ttl(key);
            //System.out.println("token :" + key + "过期时间: " + ttl);
        }
        //System.out.println(s1);

    }


    public static void main2(String[] args) {
        // 定义主节点的名称、密码和端口号
        String masterName = "mymaster";
        String password = "N8cZgk8&@n!Wp$5C";
        // 创建 JedisPoolConfig 对象，设置最大连接数等参数
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(10);
        poolConfig.setMinIdle(5);

        // 创建 JedisSentinelPool 对象，指定主节点信息和哨兵地址列表
        Set<String> sentinels = new HashSet<>();
        sentinels.add("47.104.30.91:26899");
        sentinels.add("47.104.66.120:26899");
        sentinels.add("47.104.30.74:26899");
        JedisSentinelPool jedisPool = new JedisSentinelPool(masterName, sentinels, poolConfig, password);
        try (Jedis jedis = jedisPool.getResource()) {
            String phones = "13992216052,13892294901,13892239761,13891279521,15336225609,1389" +
                    "2212572,13992271740,15289328881,15289418150,15609120876,13038966750,13892234574,13488088383,13772364261,13227993611,18037567736,18966958763,13325425756,17309126001,18220869705,13571566691,18629190500,17600139207,17791972572";
            // 现在可以通过 jedis 对象与 Redis Sentinel 交互了
            for (String phone : phones.split(",")) {
                Set<String> keys = jedis.keys("*" + phone + "*");
                System.out.println("手机号phone ：" + phone + "keys  - ：" + keys);
                keys.forEach(key -> {
                    //System.out.println("key: " + key);
                   /// long res = jedis.del(key);
                  //  System.out.println(key + "删除结果: " + res);
                });
                Set<String> news = jedis.keys("*" + phone + "*");
                System.out.println("手机号phone ：" + phone + "news  - ：" + news);
            }
            // ...其他操作...
        } catch (Exception e) {
            e.getMessage();
            //e.printStackTrace();
        } finally {
            if (jedisPool != null) {
                jedisPool.close();
            }
            System.out.println("总共存了数据个数: " + maps.size());
            doWrite();

        }
    }


    public static void doWrite() {
        Jedis jedis = new Jedis("43.155.30.104", 7702);
        //jedis.auth("bobo");
        Iterator<Map.Entry<String, Object>> iterator = maps.entrySet().iterator();
        while (iterator.hasNext()) {
            try {
                Map.Entry<String, Object> next = iterator.next();
                String key = next.getKey();
                Object value = next.getValue();
                jedis.set(key, JSONObject.toJSONString(value));
                //iterator.remove(key);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        System.out.println("结束 maps：" + maps);
    }


    public static void doWrite2(String s) {
        Jedis jedis = new Jedis("47.104.30.91", 6379);
        jedis.auth("xiaomuding123");
        SetParams params = new SetParams();
        params.ex(259200);
        String key = "CAMERA:REFRESH_TOKEN:9:".concat(s.split("#")[0]);
        String set = jedis.set(key, s.split("#")[1], params);
        System.out.println(set);
    }
}
