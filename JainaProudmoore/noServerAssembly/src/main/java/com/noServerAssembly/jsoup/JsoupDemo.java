package com.noServerAssembly.jsoup;

import cn.hutool.core.net.URLDecoder;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @ClassNAME JsoupDemo
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/15 09:59
 */

public class JsoupDemo {

    public static void main2(String[] args) throws IOException {

        String url = "http://www.gaosan.com/gaokao/537192.html";
        url = URLDecoder.decode(url, StandardCharsets.UTF_8);
        //Document document = Jsoup.parse(new URL(url), 30000);
        //模拟浏览器访问，防止阻止跳到登录页面
        Connection mozilla = Jsoup.connect(url).userAgent("Mozilla");
        Document document = mozilla.get();
        Element element = document.body();
        Element j_goodsList = element.getElementById("data537192");
        //Elements lis = j_goodsList.getElementsByTag("li");
        Elements paragraphs  = element.select("#data537192 p");
        // 遍历所有找到的p标签，并打印它们的文本
        for (Element paragraph : paragraphs) {
            System.out.println(paragraph.text());
        }
//        Elements lis = j_goodsList.getElementsByClass("layout-logo");
//        for (Element li : lis) {
//            String text = li.text();
//            System.out.println(text);
//            String title = li.getElementsByClass("title").eq(0).text();
//            System.out.println(title);
//        }

    }


    public static void main(String[] args) {
        try {
            // 目标网址
            String url = "http://www.gaosan.com/gaokao/537192.html";

            // 使用Jsoup连接到网站并获取HTML文档
            Document doc = Jsoup.connect(url).get();

            // 假设文章标题使用了<h1>标签（需要根据实际情况调整选择器）
            Element titleElement = doc.select("h1").first();
            String title = titleElement != null ? titleElement.text() : "标题未找到";

            // 假设正文内容位于某个具有特定类名的<div>标签内（需要根据实际情况调整选择器）
            // 例如，如果正文内容位于class为"content"的div内，选择器可以是".content"
            Element contentElement = doc.select("div.article-content").first();
            String content = contentElement != null ? contentElement.text() : "内容未找到";

            System.out.println("标题: " + title);
            System.out.println("内容: " + content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
