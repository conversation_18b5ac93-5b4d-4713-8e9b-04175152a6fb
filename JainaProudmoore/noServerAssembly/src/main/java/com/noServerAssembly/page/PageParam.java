package com.noServerAssembly.page;


import lombok.Data;

import java.io.Serializable;


@Data
public class PageParam implements Serializable {

    private static final Integer PAGE_NO = 1;
    private static final Integer PAGE_SIZE = 10;

    /**
     * 每页条数 - 不分页
     *
     * 例如说，导出接口，可以设置 {@link #pageSize} 为 -1 不分页，查询所有数据。
     */
    public static final Integer PAGE_SIZE_NONE = -1;


    private Integer pageNo = PAGE_NO;

    private Integer pageSize = PAGE_SIZE;

}
