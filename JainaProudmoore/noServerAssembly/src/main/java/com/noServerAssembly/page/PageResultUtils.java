package com.noServerAssembly.page;


import com.github.pagehelper.ISelect;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.SneakyThrows;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: atlas_oversea_micro_services
 * @description: TODO
 * @author: renBo
 * @create: 2025-05-07 14:07
 **/
public class PageResultUtils {


    /**
     * 分页 + 转换（将分页结果映射为目标类型）
     */
    public static <T, R> PageResult<R> startAndConvert(PageParam pageParam, ISelect select, Function<T, R> converter, Class<T> inputType) {
        PageResult<T> pageResult = startPage(pageParam, select, inputType);
        List<R> mappedList = pageResult.getList().stream()
                .map(converter)
                .collect(Collectors.toList());
        PageResult<R> result = new PageResult<R>();
        result.setList(mappedList);
        result.setTotal(pageResult.getTotal());
        return result;
    }


    /***
     * <AUTHOR>
     * @Description //pageHelper -> mp.page
     * @Date 2025年4月11日17:44:29
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * @Param [int, int, com.github.pagehelper.ISelect]
     */
    public static <E> PageResult<E> startPage(PageParam pageParam, ISelect iSelect, Class<E> eClass) {
        return startPage(pageParam.getPageNo(), pageParam.getPageSize(), iSelect, eClass);
    }

    public static PageResult<Object> startPage(PageParam pageParam, ISelect iSelect) {
        return startPage(pageParam.getPageNo(), pageParam.getPageSize(), iSelect, Object.class);
    }

    /***
     * <AUTHOR>
     * @Description //pageHelper -> mp.page
     * @Date 2025年4月11日17:44:29
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * @Param [int, int, com.github.pagehelper.ISelect]
     */
    public static <E> PageResult<Object> startPage(int pageNum, int pageSize, ISelect iSelect) {
        return startPage(pageNum, pageSize, iSelect, Object.class);
    }

    @SneakyThrows
    public static <E> PageResult<E> startPage(int pageNum, int pageSize, ISelect iSelect, Class<E> eClass) {
        PageInfo<E> pageInfo = PageHelper.startPage(pageNum, pageSize)
                .doSelectPageInfo(iSelect);
        PageResult<E> result = new PageResult<E>();
        result.setList(BotBeanutils.copyToList(pageInfo.getList(), eClass));
        result.setTotal(pageInfo.getTotal());
        return result;
    }


    /**
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * <AUTHOR>
     * @Description //手动分页 默认不要调用 性能差 若是统计的数据进行分页非sql直接查出没办法用这个
     * @Date 2025年4月11日17:44:29
     * @Param [int, int, java.util.List<E>]
     */
    public static <E> PageResult<E> startPage(int pageNum, int pageSize, List<E> list) {
        return PageInfoUtils.list2PageResult(list, pageNum, pageSize);
    }

    public static <E> PageResult<E> startPage(PageParam pageParam, List<E> list) {
        return PageInfoUtils.list2PageResult(list, pageParam.getPageNo(), pageParam.getPageSize());
    }
}
