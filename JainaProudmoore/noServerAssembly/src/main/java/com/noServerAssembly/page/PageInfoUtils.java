package com.noServerAssembly.page;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description //手动分页
 * @Date 17:33 2023-07-12
 * @return
 * @Param
 */
@Component
public class PageInfoUtils {

    private PageInfoUtils() {
    }


    public static <E> PageResult<E> list2PageResult(List<E> arrayList, Integer pageNum, Integer pageSize) {
        PageInfo<E> pageInfo = list2PageInfo(arrayList, pageNum, pageSize);
        PageResult<E> page = new PageResult<>();
        page.setList(pageInfo.getList());
        page.setTotal(pageInfo.getTotal());
        return page;
    }

    public static <E> Page<E> list2Page(List<E> arrayList, Integer pageNum, Integer pageSize) {
        PageInfo<E> pageInfo = list2PageInfo(arrayList, pageNum, pageSize);
        Page<E> page = new Page<>();
        page.setSize(pageSize);
        page.setRecords(pageInfo.getList());
        page.setTotal(pageInfo.getTotal());
        page.setSize(pageInfo.getPageSize());
        page.setCurrent(pageInfo.getPageNum());
        return page;
    }

    public static <T> PageInfo<T> list2PageInfo(List<T> arrayList, Integer pageNum, Integer pageSize) {
        //实现list分页
        PageHelper.startPage(pageNum, pageSize);
        int pageStart = pageNum == 1 ? 0 : (pageNum - 1) * pageSize;
        //int pageEnd = arrayList.size() < pageSize * pageNum ? arrayList.size() : pageSize * pageNum;
        //优化后
        int pageEnd = Math.min(arrayList.size(), pageSize * pageNum);
        List<T> pageResult = new LinkedList<>();
        if (arrayList.size() > pageStart) {
            pageResult = arrayList.subList(pageStart, pageEnd);
        }
        PageInfo<T> pageInfo = new PageInfo<>(pageResult);
        //获取PageInfo其他参数
        pageInfo.setTotal(arrayList.size());
        long endRow = pageInfo.getEndRow() == 0 ? 0 : (long) (pageNum - 1) * pageSize + pageInfo.getEndRow() + 1;
        pageInfo.setEndRow(endRow);
        boolean hasNextPage = arrayList.size() > pageSize * pageNum;
        pageInfo.setHasNextPage(hasNextPage);
        boolean hasPreviousPage = pageNum != 1;
        pageInfo.setHasPreviousPage(hasPreviousPage);
        pageInfo.setIsFirstPage(!hasPreviousPage);
        boolean isLastPage = arrayList.size() > pageSize * (pageNum - 1) && arrayList.size() <= pageSize * pageNum;
        pageInfo.setIsLastPage(isLastPage);
        int pages = arrayList.size() % pageSize == 0 ? arrayList.size() / pageSize : (arrayList.size() / pageSize) + 1;
        pageInfo.setNavigateLastPage(pages);
        int[] navigatePageNums = new int[pages];
        for (int i = 1; i < pages; i++) {
            navigatePageNums[i - 1] = i;
        }
        pageInfo.setNavigatepageNums(navigatePageNums);
        int nextPage = pageNum < pages ? pageNum + 1 : 0;
        pageInfo.setNextPage(nextPage);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages(pages);
        pageInfo.setPrePage(pageNum - 1);
        pageInfo.setSize(pageInfo.getList().size());
        int starRow = arrayList.size() < pageSize * pageNum ? 1 + pageSize * (pageNum - 1) : 0;
        pageInfo.setStartRow(starRow);
        PageHelper.clearPage();
        return pageInfo;
    }


    public static boolean isNotEmpty(PageInfo pageInfo) {
        if (ObjectUtil.isNotNull(pageInfo) && CollectionUtil.isNotEmpty(pageInfo.getList())) {
            return true;
        }
        return false;
    }
}