package com.noServerAssembly.page;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.SneakyThrows;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2025年4月11日17:44:29
 * @PackageName:com.xmd.xiaomuding.base.server.controller
 * @ClassName: PageUtils
 * @Description: TODO
 * @Version 1.0
 */
public class PageUtils {


    /**
     * 分页 + 转换（将分页结果映射为目标类型）
     */
    public static <T, R> Page<R> startAndConvert(PageParam pageParam, ISelect select, Function<T, R> converter, Class<T> inputType) {
        Page<T> page = startPage(pageParam, select, inputType);
        List<R> mappedList = page.getRecords().stream()
                .map(converter)
                .collect(Collectors.toList());
        Page<R> pageResult = new Page<>();
        pageResult.setSize(page.getSize());
        pageResult.setRecords(mappedList);
        pageResult.setTotal(page.getTotal());
        pageResult.setSize(page.getSize());
        pageResult.setCurrent(page.getCurrent());
        return pageResult;
    }

    /***
     * <AUTHOR>
     * @Description //pageHelper -> mp.page
     * @Date 2025年4月11日17:44:29
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * @Param [int, int, com.github.pagehelper.ISelect]
     */
    public static <E> Page<E> startPage(PageParam pageParam, ISelect iSelect, Class<E> eClass) {
        return startPage(pageParam.getPageNo(), pageParam.getPageSize(), iSelect, eClass);
    }

    public static <E> Page<Object> startPage(PageParam pageParam, ISelect iSelect) {
        return startPage(pageParam.getPageNo(), pageParam.getPageSize(), iSelect, Object.class);
    }

    /***
     * <AUTHOR>
     * @Description //pageHelper -> mp.page
     * @Date 2025年4月11日17:44:29
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * @Param [int, int, com.github.pagehelper.ISelect]
     */
    public static <E> Page<E> startPage(int pageNum, int pageSize, ISelect iSelect) {
        PageInfo<E> pageInfo = PageHelper.startPage(pageNum, pageSize)
                .doSelectPageInfo(iSelect);
        Page<E> page = new Page<>();
        page.setSize(pageSize);
        page.setRecords(pageInfo.getList());
        page.setTotal(pageInfo.getTotal());
        page.setSize(pageInfo.getPageSize());
        page.setCurrent(pageInfo.getPageNum());
        return page;
    }

    @SneakyThrows
    public static <E> Page<E> startPage(int pageNum, int pageSize, ISelect iSelect, Class<E> eClass) {
        PageInfo<E> pageInfo = PageHelper.startPage(pageNum, pageSize)
                .doSelectPageInfo(iSelect);
        Page<E> page = new Page<>();
        page.setSize(pageSize);
        page.setRecords(BotBeanutils.copyToList(pageInfo.getList(), eClass));
        page.setTotal(pageInfo.getTotal());
        page.setSize(pageInfo.getPageSize());
        page.setCurrent(pageInfo.getPageNum());
        return page;
    }


    /**
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * <AUTHOR>
     * @Description //手动分页 默认不要调用 性能差 若是统计的数据进行分页非sql直接查出没办法用这个
     * @Date 2025年4月11日17:44:29
     * @Param [int, int, java.util.List<E>]
     */
    public static <E> Page<E> startPage(int pageNum, int pageSize, List<E> list) {
        return PageInfoUtils.list2Page(list, pageNum, pageSize);
    }
}
