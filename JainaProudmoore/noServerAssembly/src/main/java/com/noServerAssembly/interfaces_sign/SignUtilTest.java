package com.noServerAssembly.interfaces_sign;

import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: SignUtilTest
 * @Description: TODO
 * @date 2023-04-21 17:24
 */
public class SignUtilTest {

    /**
     * 生成签名sign
     * 加密前：appId=wx123456789&timestamp=1583332804914&key=7214fefff0cf47d7950cb2fc3b5d670a
     * 加密后：E2B30D3A5DA59959FA98236944A7D9CA
     */
    public static String createSign(SortedMap<String, String> params, String key){
        StringBuilder sb = new StringBuilder();
        Set<Map.Entry<String, String>> es =  params.entrySet();
        Iterator<Map.Entry<String,String>> it =  es.iterator();
        //生成
        while (it.hasNext()){
            Map.Entry<String,String> entry = it.next();
            String k = entry.getKey();
            String v = entry.getValue();
            if(null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)){
                sb.append(k+"="+v+"&");
            }
        }
        sb.append("key=").append(key);
        String sign = MD5(sb.toString()).toUpperCase();
        return sign;
    }

    /**
     * md5常用工具类
     */
    public static String MD5(String data){
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte [] array = md5.digest(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString().toUpperCase();
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        //第二步：用户端发起请求，生成签名后发送请求
        String appSecret = "";
        String appId = "";
        long nowTime = new Date().getTime();
        //生成签名
        SortedMap<String, String> sortedMap = new TreeMap<>();
        sortedMap.put("appId", appId);
        sortedMap.put("timestamp", String.valueOf(nowTime));
        System.out.println("appId:"+appId+" 时间戳:"+nowTime+" 签名："+ SignUtil.createSign(sortedMap, appSecret));
    }
}