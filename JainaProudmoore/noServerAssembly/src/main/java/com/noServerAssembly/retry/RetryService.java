package com.noServerAssembly.retry;

import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassNAME RetryService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/14 14:13
 */
@Service
@Slf4j
public class RetryService {

    @Resource
    private RetryTemplate retryTemplate;

    /**
     * @param param
     * @ClassName: RetryService
     * @Description: 重试逻辑
     * @Author: <EMAIL>
     * @Date: 2023/2/14 14:16
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     */
    public Map<String, Object> sendClientRequest(String param) {
        return retryTemplate.execute(context -> {
            Map<String, Object> resultMap = new HashMap<>();
            if (resultMap.get("param").equals("error")) {
                log.info("查询出现异常 启动重试第{}次", String.valueOf(context.getRetryCount()));
                throw new RuntimeException();
            }
            return resultMap;
        }, context -> {
            //TODO 重试次数上限 补救?
            log.error("当重试次数上限 {}", param.toString());
            return null;
        });

    }
}
