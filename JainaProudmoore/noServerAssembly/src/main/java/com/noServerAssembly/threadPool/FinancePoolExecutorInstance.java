package com.noServerAssembly.threadPool;

import java.util.concurrent.*;

/**
 * 项目名称: broker_finance_service
 *
 * @ClassName FinancePoolExecutorInstance
 * @Description 手动线程池工厂
 * <AUTHOR> <EMAIL>   @Date 2020/5/19 14:03
 */
public class FinancePoolExecutorInstance
{
    /**
     * 核心线程数
     */
    private static int corePoolSize = 2;
    /**
     * 最大线程数
     */
    private static int maximumPoolSize = 200;

    /**
     * 空闲线程存活时间
     */
    private static long keepAliveTime = 30;

    /**
     * 空闲线程存活时间的单位
     */
    private static TimeUnit timeUnit = TimeUnit.SECONDS;
    /**
     * 使用ArrayBlockingQueue有界任务队列，若有新的任务需要执行时，线程池会创建新的线程，
     * 直到创建的线程数量达到corePoolSize时，则会将新的任务加入到等待队列中。
     * 若等待队列已满，即超过ArrayBlockingQueue初始化的容量，
     * 则继续创建线程，直到线程数量达到maximumPoolSize设置的最大线程数量，
     * 若大于maximumPoolSize，则执行拒绝策略。
     * 在这种情况下，线程数量的上限与有界任务队列的状态有直接关系，
     * 如果有界队列初始容量较大或者没有达到超负荷的状态，
     * 线程数将一直维持在corePoolSize以下，反之当任务队列已满时，
     * 则会以maximumPoolSize为最大线程数上限。
     */
    private static BlockingQueue<Runnable> arrayBlockingQueue = new ArrayBlockingQueue<>(10);
    /**
     * 普通线程池
     */
    private static ThreadPoolExecutor threadPoolExecutor;

    private FinancePoolExecutorInstance()
    {
    }
    public static ThreadPoolExecutor getThreadPoolExecutor()
    {
        if (threadPoolExecutor == null)
        {
            synchronized (FinancePoolExecutorInstance.class)
            {
                if (threadPoolExecutor == null)
                {
                    threadPoolExecutor = new ThreadPoolExecutor(
                            corePoolSize, maximumPoolSize,
                            keepAliveTime, timeUnit, arrayBlockingQueue,
                            Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());
                }
            }
        }
        return threadPoolExecutor;
    }
}
