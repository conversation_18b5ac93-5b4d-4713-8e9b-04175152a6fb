<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rb</groupId>
		<artifactId>JainaProudmoore</artifactId>
		<version>1.0.0.RELEASE</version>
	</parent>

	<groupId>com.rb</groupId>
	<artifactId>rocketMq</artifactId>
	<packaging>jar</packaging>

	<description>rocket</description>

	<properties>
		<redisson.version>3.17.3</redisson.version>
		<rocketmq.version>2.3.0</rocketmq.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.rb</groupId>
			<artifactId>j<PERSON><PERSON><PERSON><PERSON><PERSON></artifactId>
			<version>1.0.0.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-spring-boot-starter</artifactId>
			<version>${rocketmq.version}</version> <!-- 请替换为实际兼容的版本号 -->
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-integration</artifactId>
		</dependency>
	</dependencies>
</project>
