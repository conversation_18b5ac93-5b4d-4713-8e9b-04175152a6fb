package com.noServerAssembly.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;


import com.noServerAssembly.enums.MessageDelayLevelEnums;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2024-03-05 16:13
 * @PackageName:com.bot.cat.assembly.rocketMQ.utils
 * @ClassName: RocketSendService
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
public class RocketSendService {

    private static RocketMQTemplate rocketMQTemplate = SpringUtil.getBean(RocketMQTemplate.class);


    /**
     * 发送消息
     *
     * @param topic   主体
     * @param payload 消息报文
     */
    public static void send(String topic, Object payload) {
        // 创建消息，使用MessageBuilder来设置消息的key
        send(topic, null, payload);
    }

    /**
     * 发送消息
     *
     * @param topic   主体
     * @param tar     标签 类似与rabbit的 rookingKey
     * @param payload 消息报文
     */
    public static void send(String topic, String tar, Object payload) {
        // 创建消息，使用MessageBuilder来设置消息的key
        topic = StringUtils.isBlank(tar) ? topic : topic.concat(":").concat(tar);
        send(topic, payload, MessageDelayLevelEnums.NOTHING);
    }

    /**
     * 这里注意的是RocketMQ不支持任意时间的延时
     * 只支持以下几个固定的延时等级，等级1就对应1s，以此类推，最高支持2h延迟
     * private String messageDelayLevel = "1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h";
     * 延时发送
     *
     * @param tar     标签 类似与rabbit的 rookingKey
     * @param topic   主体
     * @param enums   延时配置时间
     * @param payload 消息报文
     */
    public static void send(String topic, String tar, Object payload, MessageDelayLevelEnums enums) {
        topic = StringUtils.isBlank(tar) ? topic : topic.concat(":").concat(tar);
        send(topic, payload, enums);
    }


    /**
     * 这里注意的是RocketMQ不支持任意时间的延时
     * 只支持以下几个固定的延时等级，等级1就对应1s，以此类推，最高支持2h延迟
     * private String messageDelayLevel = "1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h";
     * 延时发送
     *
     * @param topic   主体
     * @param enums   延时配置时间
     * @param payload 消息报文
     */
    public static void send(String topic, Object payload, MessageDelayLevelEnums enums) {
        // 创建消息，使用MessageBuilder来设置消息的key
        Message message = MessageBuilder.withPayload(JSONObject.toJSONString(payload))
                .setHeader(RocketMQHeaders.KEYS, UUID.randomUUID())
                .build();
        if (enums.equals(MessageDelayLevelEnums.NOTHING)) {
            rocketMQTemplate.syncSend(topic, message);
            return;
        }
        rocketMQTemplate.syncSend(topic, message, 30000, enums.getStatus());
    }


    /**
     * 顺序发送
     *
     * @param topic       主体
     * @param messageList 消息体
     * @param fieldName   标识 标识一样会放到同一个队列里面保证顺序执行
     */
    public static void sendOrderly(String topic, List<Object> messageList, @NotNull String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            throw new RuntimeException("标识字段不能为空");
        }
        // 创建消息，使用MessageBuilder来设置消息的key
        messageList.forEach(msg -> {
            try {
                rocketMQTemplate.syncSendOrderly(topic, msg, msg.getClass().getField(fieldName).toString());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

    }


    /**
     * 发送异步消息
     *
     * @param destination  formats: `topicName:tags`
     * @param payload      消息体
     * @param sendCallback {@link SendCallback}
     * @param delayLevel   level for the delay message
     */
    public static void asyncSend(String destination, Object payload, SendCallback sendCallback,
                                 Integer delayLevel) {
        // 创建消息，使用MessageBuilder来设置消息的key
        Message message = MessageBuilder.withPayload(JSONObject.toJSONString(payload))
                .setHeader(RocketMQHeaders.KEYS, UUID.randomUUID())
                .build();
        if (delayLevel == null) {
            rocketMQTemplate.asyncSend(destination, message, sendCallback);
            return;
        }
        rocketMQTemplate.asyncSend(destination, message, sendCallback, 30000, delayLevel);
    }

    /**
     * 发送异步消息
     *
     * @param destination  formats: `topicName:tags`
     * @param payload      消息体
     * @param sendCallback {@link SendCallback}
     */
    public static void asyncSend(String destination, Object payload, SendCallback sendCallback) {
        // 创建消息，使用MessageBuilder来设置消息的key
        asyncSend(destination, payload, sendCallback, null);
    }


}
