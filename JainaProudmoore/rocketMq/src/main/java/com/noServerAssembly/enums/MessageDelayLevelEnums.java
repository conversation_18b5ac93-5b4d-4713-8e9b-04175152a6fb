package com.noServerAssembly.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2024-03-06 11:23
 * @PackageName:com.bot.cat.assembly.rocketMQ.enums
 * @ClassName: MessageDelayLevelEnums
 * @Description: private String messageDelayLevel = "1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h";
 * @Version 1.0
 */
@Getter
public enum MessageDelayLevelEnums {
    NOTHING(null, "非延时"),
    LEVEL_1(1, "1s"),
    LEVEL_2(2, "5s"),
    LEVEL_3(3, "10s"),
    LEVEL_4(4, "30s"),
    LEVEL_5(5, "1m"),
    LEVEL_6(6, "2m"),
    LEVEL_7(7, "3m"),
    LEVEL_8(8, "4m"),
    LEVEL_9(9, "5m"),
    LEVEL_10(10, "6m"),
    LEVEL_11(11, "7m"),
    LEVEL_12(12, "8m"),
    LEVEL_13(13, "9m"),
    LEVEL_14(14, "10m"),
    LEVEL_15(15, "20m"),
    LEVEL_16(16, "30m"),
    LEVEL_17(17, "1h"),
    LEVEL_18(18, "2h"),
    ;

    private Integer status;
    private String value;

    MessageDelayLevelEnums(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    /**
     * 根据状态查询枚举
     */
    public static MessageDelayLevelEnums getEnumByStatus(Integer status) {
        return Arrays.stream(MessageDelayLevelEnums.values())
                .filter(x -> x.getStatus().equals(status))
                .findFirst()
                .orElse(null);
    }


}
