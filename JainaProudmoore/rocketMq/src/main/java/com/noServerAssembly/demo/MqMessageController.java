package com.noServerAssembly.demo;

import com.noServerAssembly.enums.MessageDelayLevelEnums;
import com.noServerAssembly.utils.RocketSendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024-03-05 11:40
 * @PackageName:com.bot.cat.assembly.rocketMQ.demo
 * @ClassName: MqMessageController
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/mqMessageController")
public class MqMessageController {


    /**
     * 正常发送
     */
    @RequestMapping("/pushMessage.send")
    public void send(@RequestBody UserD userD) {
        RocketSendService.send("bot-topic", userD);
    }

    /**
     * 延迟发送
     */
    @RequestMapping("/pushMessage.send2")
    public void send2(@RequestBody UserD userD) {
        RocketSendService.send("bot-topic", userD, MessageDelayLevelEnums.LEVEL_3);
    }

    @RequestMapping("/pushMessage.send3")
    public void send3(@RequestBody UserD userD) {
        RocketSendService.send("bot-topic", "TAG1", userD, MessageDelayLevelEnums.LEVEL_3);
    }


    /**
     * 异步发送
     */
    @RequestMapping("/pushMessage.send4")
    public void send4(@RequestBody UserD userD) {
        RocketSendService.asyncSend("bot-topic", userD, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                System.out.println("成功了============" + sendResult);
            }
            @Override
            public void onException(Throwable throwable) {
                System.out.println("失败了============" + throwable.getMessage());
            }
        });
        RocketSendService.send("bot-topic", "TAG1", userD, MessageDelayLevelEnums.LEVEL_3);
    }
}
