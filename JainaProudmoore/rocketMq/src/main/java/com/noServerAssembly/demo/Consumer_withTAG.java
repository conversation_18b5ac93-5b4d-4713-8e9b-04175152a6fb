package com.noServerAssembly.demo;

import com.noServerAssembly.listener.RocketConsume;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @Date 2024-03-05 11:42
 * @PackageName:com.bot.cat.assembly.rocketMQ.demo
 * @ClassName: Consumer
 * @Description: TODO
 * @Version 1.0
 * 创建一个简单的标签消息的监听
 * 1.类上添加注解@Component和@RocketMQMessageListener
 * selectorType = SelectorType.TAG,  指定使用tag过滤。(也可以使用sql92 需要在配置文件broker.conf中开启enbalePropertyFilter=true)
 * selectorExpression = "TAG1"     表达式，默认是*,支持"tag1 || tag2 || tag3"
 * 2.实现RocketMQListener接口，注意泛型的使用
 */

//@Component
//@RocketMQMessageListener(topic = "bot-topic",
//        consumerGroup = "bobo_group",
//        selectorType = SelectorType.TAG,
//        selectorExpression = "TAG1")
@Slf4j
public class Consumer_withTAG extends RocketConsume<UserD> {

    @Override
    protected void doHandlerMessage(UserD s) {
        System.out.println("ConsumerConsumerConsumer 收到消息了" + s);
    }

    @Override
    protected void doErrorHandlerMessage(UserD s) {

    }
}
