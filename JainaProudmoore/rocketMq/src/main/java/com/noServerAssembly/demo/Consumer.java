package com.noServerAssembly.demo;


import com.noServerAssembly.listener.RocketConsume;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;



/**
 * <AUTHOR>
 * @Date 2024-03-05 11:42
 * @PackageName:com.bot.cat.assembly.rocketMQ.demo
 * @ClassName: Consumer
 * @Description: TODO
 * @Version 1.0
 */

@Component
@RocketMQMessageListener(topic = "bot-topic", consumerGroup = "bobo_group")
@Slf4j
public class Consumer extends RocketConsume<UserD> {

    @Override
    protected void doHandlerMessage(UserD s) {
        System.out.println("ConsumerConsumerConsumer 收到消息了" + s);
    }

    @Override
    protected void doErrorHandlerMessage(UserD s) {

    }
}
