package com.noServerAssembly.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.utils.RedisUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @Date 2024-03-06 9:06
 * @PackageName:com.bot.cat.assembly.rocketMQ.listener
 * @ClassName: RocketConsume
 * @Description: 消费端抽象层
 * @Version 1.0
 */
@Slf4j
public abstract class RocketConsume<E> implements RocketMQListener<MessageExt> {

    protected MessageExt message;

    private final Class<E> type;

    protected RocketConsume() {
        // 获取当前类的直接父类的类型
        Type genericSuperclass = getClass().getGenericSuperclass();
        // 参数化类型
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            // 获取参数化类型的实际类型参数的数组，[0]是因为我们假设只有一个泛型参数
            Type actualTypeArgument = parameterizedType.getActualTypeArguments()[0];
            // 类型强转并获取类的Class对象
            this.type = (Class<E>) actualTypeArgument;
        } else {
            throw new RuntimeException("Problem during type inference");
        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //接收到消息
     * @Date 9:08 2024-03-06
     * @Param [org.apache.rocketmq.common.message.MessageExt]
     */
    @SneakyThrows
    @Override
    public void onMessage(MessageExt message) {
        log.info("【{}】接收到消息 message:【{}】", this.getClass().getSimpleName(), message.getTopic());
        this.message = message;
        RocketMQMessageListener listener = this.getClass().getAnnotation(RocketMQMessageListener.class);
        //进行消息的幂等性处理 TODO 注意 广播模式下不进行消息幂等性处理
        if (listener.messageModel().equals(MessageModel.CLUSTERING) && !RedisUtils.setIfAbsent(message.getKeys(), message.getTopic(), 10L)) {
            log.info("该消息【{}】已被其他节点消费!", message.getKeys());
        }
        E messageEntry = null;
        try {
            messageEntry = new ObjectMapper().readValue(new String(message.getBody()), this.type);
            //执行主流程
            log.info("【{}】开始执行主业务流程 解析到的数据为:【{}】", message.getKeys(), messageEntry);
            doHandlerMessage(messageEntry);
            log.info("【{}】业务数据处理完成", message.getKeys());
        } catch (Exception e) {
            log.info("业务出现异常:【{}】,已经重试【{}】次", e.getMessage(), message.getReconsumeTimes());
            //判断当前重试是否达到重试上线 如果达到上限 做出补救措施 否则异常抛出继续重试
            if (message.getReconsumeTimes() >= (listener.maxReconsumeTimes() == -1 ? 16 : listener.maxReconsumeTimes())) {
                log.info("重试次数已达到上线:【{}】 交由异常处理业务处理", listener.maxReconsumeTimes());
                doErrorHandlerMessage(messageEntry);
                return;
            }
            throw e;
        }

    }

    /**
     * 业务处理方法
     */
    protected abstract void doHandlerMessage(E e);

    /**
     * 业务报错 重试达到上线 业务处理方法
     */
    protected abstract void doErrorHandlerMessage(E e);

}
