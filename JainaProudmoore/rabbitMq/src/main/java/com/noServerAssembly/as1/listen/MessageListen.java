package com.noServerAssembly.as1.listen;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 项目名称: pay_core
 *
 * @ClassName MessageListen
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/5/5 9:47
 */
@Slf4j
@Component
public class MessageListen {

    @Resource
    private ConnectionFactory connectionFactory;

    /**
     * 在容器中加入消息监听
     *
     * @param queue
     * @param messageHandler
     * @param isAck
     * @throws Exception
     */
    public void addMessageLister(String queue, WalterMessageListener messageHandler, boolean isAck) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(this.connectionFactory);
        container.setQueueNames(queue);
        //不确认，存在消息丢失的风险
        AcknowledgeMode ack = AcknowledgeMode.NONE;
        if (isAck) {
            //手动确认
            ack = AcknowledgeMode.MANUAL;
        }
        messageHandler.setAck(ack);
        container.setAcknowledgeMode(ack);
        container.setMessageListener(new MessageListenerAdapter(messageHandler));
        container.start();
        log.info("已成功监听异步消息触发通知队列：{}", queue);
    }
}
