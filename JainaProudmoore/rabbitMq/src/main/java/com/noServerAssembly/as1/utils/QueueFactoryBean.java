package com.noServerAssembly.as1.utils;

import org.springframework.beans.factory.FactoryBean;

/**
 * 项目名称: pay_core
 *
 * @ClassName QueueFactoryBean
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/5/5 9:44
 */
public class QueueFactoryBean implements FactoryBean {
    @Override
    public Object getObject() throws Exception {
        return null;
    }

    @Override
    public Class<?> getObjectType() {
        return null;
    }
}