package com.noServerAssembly.as1.vo;


import com.exception.AssemblyCatServiceException;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.enums.TargetEnum;
import com.noServerAssembly.as1.utils.ProtostuffUtils;
import com.utils.UUIDUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.CorrelationData;

import java.io.Serializable;

/**
 * 项目名称: pay_core
 *
 * @ClassName WalterMessage
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/28 10:04
 */
@Data
//@Builder
@NoArgsConstructor
public class WalterMessage implements Serializable {

    /**
     * 交换机名称
     */
    private String exchangeName;
    /**
     * 队列名称
     */
    private String queueName;
    /**
     * 消息Id
     */
    private String messageId;
    /**
     * 消息体
     */
    private Object messageBody;
    /**
     * 目标
     */
    private TargetEnum target;
    /**
     * 路由键
     */
    private String routingKey;
    /**
     * 消息优先级
     */
    private int messagePriority = 0;
    /**
    * 消息是否持久化
    */
    private boolean ifPersistent;
    /**
    * 消息过期时间
    */
    private Integer expireTime;
    /**
    * 消息延迟时间
    */
    private Integer delayed;

    public static WalterMessage newInstance(Object mesContext, ExchangeTypeEnum exchangeType, String routingKey, boolean ifPersistent, int expireTime, int delayed){
        WalterMessage message=new WalterMessage();
        message.setMessageBody(mesContext);
        message.setExchangeName(exchangeType.getName());
        message.setRoutingKey(routingKey);
        message.setIfPersistent(ifPersistent);
        message.setExpireTime(expireTime);
        message.setDelayed(delayed);
        message.setTarget(TargetEnum.PROVIDE);
        message.setMessageId(System.currentTimeMillis() + UUIDUtils.getUUID());
        return message;
    }

    public CorrelationData createCorrelationData(){
        CorrelationData correlationData = new CorrelationData(this.getMessageId());
        //设置参数
        correlationData.setReturnedMessage(new Message(ProtostuffUtils.serialize(this)));
        return correlationData;
    }
    /**
     * 进行消息参数校验
     */
    public void validation() {
        if (StringUtils.isEmpty(exchangeName)) {
            throw new AssemblyCatServiceException("交换机不能为空");
        }
        if (TargetEnum.CONSUME.equals(target) && StringUtils.isEmpty(queueName)) {
            throw new AssemblyCatServiceException("消费方队列不能为空");
        }
        if (ExchangeTypeEnum.DIRECT.getName().equals(exchangeName)) {
            //直连交换机
            if (StringUtils.isEmpty(routingKey)) {
                throw new AssemblyCatServiceException("直连交换机路由键不能为空");
            }
        }
        if (ExchangeTypeEnum.TOPIC.getName().equals(exchangeName)) {
            //主题交换机
            if (StringUtils.isEmpty(routingKey)) {
                throw new AssemblyCatServiceException("主题交换机路由键不能为空");
            }
        }
        if (ExchangeTypeEnum.HEADERS.getName().equals(exchangeName)) {
            //头部交换机
            throw new AssemblyCatServiceException("头部交换机已弃用,请选择其他类型交换机");
        }
    }
}