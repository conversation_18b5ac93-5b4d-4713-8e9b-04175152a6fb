package com.noServerAssembly.as1.utils;

import cn.hutool.extra.spring.SpringUtil;

import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.operate.ExchangeOperate;
import com.noServerAssembly.as1.send.AbstractSendService;
import com.noServerAssembly.as1.send.DefaultRabbitSendService;
import com.noServerAssembly.as1.send.RetryByRedisRabbitSendService;
import com.noServerAssembly.as1.vo.WalterExchange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 项目名称: pay_core
 *
 * @ClassName RabbitSendUtil
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/29 15:50
 */
@Slf4j
@Component
public class RabbitSendUtil {

    private static AbstractSendService sendService;

    @PostConstruct
    public void init() {
        try {
            sendService = SpringUtil.getBean(RetryByRedisRabbitSendService.class);
        } catch (Exception e) {
            sendService = SpringUtil.getBean(DefaultRabbitSendService.class);
        }
    }


    /**
     * 扇形交换机 不需要路由键
     */
    public static void sendFanout(Object mesContext) {
        sendService.send(mesContext, ExchangeTypeEnum.TOPIC, null);
    }

    /**
     * 发送主题交换机 需要指定 路由键
     */
    public static void sendDirect(Object mesContext, String routingKey) {
        sendService.send(mesContext, ExchangeTypeEnum.DIRECT, routingKey);
    }

    /**
     * 发送直连交换机 需要指定 路由键
     */
    public static void sendTopic(Object mesContext, String routingKey) {
        sendService.send(mesContext, ExchangeTypeEnum.TOPIC, routingKey);
    }

    public static void send(Object mesContext, ExchangeTypeEnum exchangeType, String routingKey) {
        sendService.send(mesContext, exchangeType, routingKey, false, 0, 0);
    }

    /**
     * @param ifPersistent 是否开启持久化，true开启，false不开启
     * @param expireTime   是否超时  默认不会超时，单位毫秒
     */
    public static void send(Object mesContext, ExchangeTypeEnum exchangeType, String routingKey, boolean ifPersistent, int expireTime, int delayed) {
        sendService.send(mesContext, exchangeType, routingKey, ifPersistent, expireTime, delayed);
    }

    /**
     * 创建新的交换机
     */
    public static void createExchange(String exchangeName, ExchangeTypeEnum exchangeType) {
        ExchangeOperate operate = SpringUtil.getBean(ExchangeOperate.class);
        Exchange exchange = operate.initExchange(WalterExchange.newInstance(exchangeName, exchangeType));
        SpringUtil.getBean(RabbitAdmin.class).declareExchange(exchange);
    }
}