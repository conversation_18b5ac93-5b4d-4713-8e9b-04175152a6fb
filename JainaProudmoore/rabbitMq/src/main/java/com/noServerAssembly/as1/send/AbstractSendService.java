package com.noServerAssembly.as1.send;


import com.noServerAssembly.as1.Constants;
import com.noServerAssembly.as1.enums.ConfirmTypeEnum;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.vo.WalterMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import javax.annotation.Resource;


/**
 * Rabbit发送service
 */
@Slf4j
public abstract class AbstractSendService implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnsCallback, ApplicationRunner {

    @Resource
    protected RabbitTemplate rabbitTemplate;

    /**
     * 扇形交换机 不需要路由键
     */
    public void sendFanout(Object mesContext) {
        this.send(mesContext, ExchangeTypeEnum.TOPIC, null);
    }

    /**
     * 发送主题交换机 需要指定 路由键
     */
    public void sendDirect(Object mesContext, String routingKey) {
        this.send(mesContext, ExchangeTypeEnum.DIRECT, routingKey);
    }

    /**
     * 发送直连交换机 需要指定 路由键
     */
    public void sendTopic(Object mesContext, String routingKey) {
        this.send(mesContext, ExchangeTypeEnum.TOPIC, routingKey);
    }

    public void send(Object mesContext, ExchangeTypeEnum exchangeType, String routingKey) {
        this.send(mesContext, exchangeType, routingKey, false, 0, 0);
    }

    /**
     * @param ifPersistent 是否开启持久化，true开启，false不开启
     * @param expireTime   是否超时  默认不会超时，单位毫秒
     */
    public void send(Object mesContext, ExchangeTypeEnum exchangeType, String routingKey, boolean ifPersistent, int expireTime, int delayed) {
        this.send(WalterMessage.newInstance(mesContext,exchangeType,routingKey,ifPersistent,expireTime,delayed),
                createMessagePostProcessor(expireTime, ifPersistent, delayed));
    }


    /**
     * @param messagePostProcessor 消息属性
     * @return void
     * @<NAME_EMAIL>
     * @date 2022/4/28 9:46
     **/
    private void send(WalterMessage walterMessage, MessagePostProcessor messagePostProcessor) {
        //进行参数校验
        walterMessage.validation();
        //发送前置事项
        beforeSend(walterMessage);
        //开始发送消息
        rabbitTemplate.convertAndSend(walterMessage.getExchangeName(), walterMessage.getRoutingKey(), walterMessage,
                messagePostProcessor, walterMessage.createCorrelationData());
        //发送完后置事项
        afterSend(walterMessage);
    }


    /**
     * 发送消息后逻辑
     */
    public abstract void afterSend(WalterMessage walterMessage);

    /**
     * 发送消息前逻辑
     */
    public abstract void beforeSend(WalterMessage walterMessage);

    /**
     * confirm模式
     * 当消息从生产者发送到交换机时，不论发送是否成功都会返回一个confirmCallback，
     * 当发送成功时这个confirmCallback为true，
     * 当发送失败时这个confirmCallback为false。
     */
    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String reason) {
        if (!ack) {
            log.error("消息ID:{}发送到交换机失败 原因:{} 进入重试阶段", correlationData.getId(), reason);
            //进入重试阶段
            doConfirmHandler(correlationData);
        }

    }

    /**
     * 消息从交换机发送到队列时，若投递失败则会返回一个returnCallback
     * [message=(Body:'[' MessageProperties [headers={spring_returned_message_correlation=16511349491012d1eee3fdf884614acf57a434418e739,
     * __TypeId__=com.walter.mq_core.vo.WalterMessage}, contentType=UTF-8, contentEncoding=UTF-8, contentLength=0,
     * receivedDeliveryMode=PERSISTENT, priority=0, deliveryTag=0]), replyCode=312, replyText=NO_ROUTE, exchange=WALTER_DIRECT,
     * routingKey=demo_key]
     */
    @Override
    public void returnedMessage(ReturnedMessage returned) {
        log.error("消息ID:{}投递失败,开始进行投递失败逻辑", returned.getMessage().getMessageProperties().getMessageId());
        //进入重试阶段
        doReturnedHandler(returned);
    }

    /**
     * 消息发送至交换器失败逻辑
     */
    protected abstract void doConfirmHandler(CorrelationData correlationData);

    /**
     * 消息投递失败
     */
    protected abstract void doReturnedHandler(ReturnedMessage returned);

    /**
     * 初始化发送方法：如果是confirm模式初始化对应的rabbitTemplate
     * 这里 rabbitTemplate不是单例的，如果需要自行定义，那么自行扩展
     */
    @Override
    public void run(ApplicationArguments args) {
        if (ConfirmTypeEnum.CORRELATED.equals(Constants.CONFIRM_TYPE)) {
            this.rabbitTemplate.setConfirmCallback(this);
            this.rabbitTemplate.setReturnsCallback(this);
        }
    }

    /**
     * 默认重试发送消息
     */
    protected void defaultRetrySend(WalterMessage walterMessage) {
        //开始发送消息
        rabbitTemplate.convertAndSend(walterMessage.getExchangeName(), walterMessage.getRoutingKey(), walterMessage,
                createMessagePostProcessor(walterMessage.getExpireTime(), walterMessage.isIfPersistent(), walterMessage.getDelayed()),
                walterMessage.createCorrelationData());
    }

    /**
     * 封装消息属性
     */
    public MessagePostProcessor createMessagePostProcessor(Integer expireTime, boolean ifPersistent, int delayed) {
        return (message) -> {
            // 设置消息的过期时间
            if (expireTime > 0) {
                message.getMessageProperties().setExpiration(expireTime + "");
            }
            //是否开启消息持久化
            if (ifPersistent) {
                message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            }
            // 消息延迟时间 入参为秒 需要转换为毫秒
            if (delayed > 0) {
                message.getMessageProperties().setDelay(delayed * 1000);
            }
            //指定消费者
            if (StringUtils.isNotEmpty(Constants.ORIENT_CONSUME)) {
                message.getMessageProperties().setAppId(Constants.ORIENT_CONSUME);
            }
            // 设置字符集
            message.getMessageProperties().setContentType("utf-8");
            return message;
        };
    }
}
