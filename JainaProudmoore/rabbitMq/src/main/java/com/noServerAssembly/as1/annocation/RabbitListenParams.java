package com.noServerAssembly.as1.annocation;


import com.noServerAssembly.as1.config.RabbitMqConfig;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

import java.lang.annotation.*;


@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Service
@ConditionalOnBean(RabbitMqConfig.class)
public @interface RabbitListenParams {
    /**
    * 队列名称
    */
    String queueName();

    /**
    * 是否需要持久化
    */
    boolean durable() default true;
    /**
    * 是否排他
    */
    boolean exclusive() default false;

    /**
    * 是否自动删除
    */
    boolean autoDelete() default false;
    /**
     * 是否需要手动确认
     */
    boolean isAck() default false;
    /**
    * 是否为延迟消息 如果为延迟消息 在 ttl(毫秒) 时间后 会直接走死信队列
    */
    boolean isDelay() default false;
    /**
     * 路由键
     */
    String routingKey() default "";

    /**
     * 交换机类型 默认直连交换机
     */
    ExchangeTypeEnum exchangeType() default ExchangeTypeEnum.DIRECT;

    /**
     * 交换机名称
     */
    String exchangeName();

    /**
     * 死信队列
     */
    DeadQueue deadQueue() default @DeadQueue();
    /**
    * 消费失败重试次数
    */
    int retry() default 0;

}
