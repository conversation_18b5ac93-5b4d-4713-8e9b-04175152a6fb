package com.noServerAssembly.as1.controller;


import com.annotation.RedisLock;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.utils.RabbitSendUtil;
import com.noServerAssembly.as1.vo.WalterExchange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目名称: pay_core
 *
 * @ClassName DemoController
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/28 16:03
 */
@Slf4j
@RestController
public class DemoController {


    @RequestMapping(value = "/send")
    public void send(String msg){
        log.info("当前时间：{}，发送一条消息给队列：{}",System.currentTimeMillis(),msg);
        RabbitSendUtil.sendDirect(msg,"demo_routing_key");
    }


    @RequestMapping(value = "/createExchange")
    public void createExchange(String exchange){
        log.info("当前时间：{}，createExchange：{}",System.currentTimeMillis(),exchange);
        RabbitSendUtil.createExchange(exchange, ExchangeTypeEnum.TOPIC);
    }

    @RequestMapping(value = "/lock")
    @RedisLock(key = "#walterExchange.exchangeName")
    public void lock(@RequestBody WalterExchange walterExchange) throws InterruptedException {
        log.info("执行逻辑============================={}",walterExchange);
    }


}