package com.noServerAssembly.as1.send;



import com.noServerAssembly.as1.vo.WalterMessage;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.stereotype.Service;

/**
 * 项目名称: pay_core
 *
 * @ClassName DefaultRabbitSendService
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/29 15:54
 */
@Service
public class DefaultRabbitSendService extends AbstractSendService {
    @Override
    public void afterSend(WalterMessage walterMessage) {

    }

    @Override
    public void beforeSend(WalterMessage walterMessage) {

    }

    @Override
    protected void doConfirmHandler(CorrelationData correlationData) {

    }

    @Override
    protected void doReturnedHandler(ReturnedMessage returned) {

    }
}