package com.noServerAssembly.as1.operate;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;

import com.noServerAssembly.as1.annocation.DeadQueue;
import com.noServerAssembly.as1.annocation.RabbitListenParams;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.vo.WalterExchange;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


/**
 * 项目名称: pay_core
 *
 * @ClassName QueueOperate
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/5/5 14:55
 */
@Slf4j
@Service
public class QueueOperate extends BaseOperate<RabbitListenParams, Queue> {

    /**
     * 声明并绑定队列
     */
    @Override
    public Queue declare(RabbitListenParams params) {
        DeadQueue deadQueue = params.deadQueue();
        Map<String, Object> arguments = new HashMap<>();
        if (ObjectUtil.isNotNull(deadQueue) && StringUtils.isNotEmpty(deadQueue.queueName())) {
            arguments.put("x-dead-letter-exchange", ExchangeTypeEnum.DEAD_LETTER.getName());
            arguments.put("x-message-ttl", deadQueue.ttl());
            arguments.put("x-dead-letter-routing-key", deadQueue.routingKey());
            //声明并绑定死信队列
            Queue dq = new Queue(deadQueue.queueName());
            dq.setIgnoreDeclarationExceptions(true);
            Binding dqBind = BindingBuilder.bind(dq)
                    .to(getExchange(ExchangeTypeEnum.DEAD_LETTER.getName(), ExchangeTypeEnum.DIRECT))
                    .with(deadQueue.routingKey()).noargs();
            this.rabbitAdmin.declareQueue(dq);
            this.rabbitAdmin.declareBinding(dqBind);
        }

        Queue declare = new Queue(params.queueName(), params.durable(), params.exclusive(), params.autoDelete(), arguments);
        declare.setIgnoreDeclarationExceptions(true);
        //进行正常队列绑定
        Binding bind = BindingBuilder.bind(declare)
                .to(getExchange(params.exchangeName(), params.exchangeType()))
                .with(params.routingKey()).noargs();
        this.rabbitAdmin.declareQueue(declare);
        this.rabbitAdmin.declareBinding(bind);
        //如果当前队列需要延迟队列 会直接走死信队列
        if(params.isDelay()){
            rabbitAdmin.deleteQueue(params.queueName());
        }
        return declare;
    }


    private Exchange getExchange(String exchangeName, ExchangeTypeEnum exchangeType) {
        return SpringUtil.getBean(ExchangeOperate.class).declare(WalterExchange.newInstance(exchangeName, exchangeType));
    }
}