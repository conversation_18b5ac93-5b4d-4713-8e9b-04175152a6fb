package com.noServerAssembly.as1.operate;

import cn.hutool.extra.spring.SpringUtil;

import com.noServerAssembly.as1.annocation.RabbitListenParams;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.vo.WalterExchange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.stereotype.Component;


/**
 * 项目名称: pay_core
 *
 * @ClassName BingOperate
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/29 17:24
 */
@Slf4j
@Component
public class BingOperate extends BaseOperate {

    public static InheritableThreadLocal<DirectExchange> exchangeThreadLocal = new InheritableThreadLocal<>();

    /**
     * 队列与交换机进行绑定
     *
     * @return
     * @params params 參數
     */
    public boolean queueBind(RabbitListenParams params) {
        try {
            SpringUtil.getBean(QueueOperate.class).declare(params);
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }

    }


    /**
     * 生成交换机
     */
    private Exchange getExchange(String exchangeName, ExchangeTypeEnum exchangeType) {
        return SpringUtil.getBean(ExchangeOperate.class).declare(WalterExchange.newInstance(exchangeName, exchangeType));
    }

}