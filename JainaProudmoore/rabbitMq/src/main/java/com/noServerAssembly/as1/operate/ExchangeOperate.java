package com.noServerAssembly.as1.operate;


import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.vo.WalterExchange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.*;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 项目名称: pay_core
 *
 * @ClassName ExchangeOperate
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/27 17:18
 */
@Slf4j
@Service
public class ExchangeOperate extends BaseOperate<WalterExchange, Exchange> {
    /**
     * 声明交换机
     */
    @Override
    public Exchange declare(WalterExchange walterExchange) {
        //参数校验
        super.validate(walterExchange);
        return Optional.of(walterExchange)
                .map(this::initExchange)
                .map(exchange -> {
                    try {
                        this.rabbitAdmin.declareExchange(exchange);
                    } catch (AmqpException e) {
                        //ignore 最后一个交换机肯定会报错 至今找不到原因
                    }
                    return exchange;
                }).orElse(null);
    }

    public Exchange initExchange(WalterExchange exchange) {
        ExchangeTypeEnum exchangeTypeEnum = exchange.getType();
        switch (exchangeTypeEnum) {
            case DIRECT:
                //声明直连交换机
                return new DirectExchange(exchange.getExchangeName(), exchange.isDurable(), exchange.isAutoDelete(), exchange.getArguments());
            case TOPIC:
                //声明主体交换机
                return new TopicExchange(exchange.getExchangeName(), exchange.isDurable(), exchange.isAutoDelete(), exchange.getArguments());
            case FANOUT:
                //声明扇形交换机
                return new FanoutExchange(exchange.getExchangeName(), exchange.isDurable(), exchange.isAutoDelete(), exchange.getArguments());
            case HEADERS:
                //声明头部交换机
                return new HeadersExchange(exchange.getExchangeName(), exchange.isDurable(), exchange.isAutoDelete(), exchange.getArguments());
//            TODO 声明延迟交换机 延迟交换机需要安装插件 https://blog.csdn.net/weixin_50616848/article/details/123593198
//            case DELAYED:
//                HashMap<String, Object> argument = new HashMap<>();
//                argument.put("x-delayed-message", "direct");
//                return new CustomExchange(exchange.getExchangeName(), "x-delayed-message", true, false, argument);
            case DEAD_LETTER:
                //声明死信交换机
                DirectExchange directExchange = new DirectExchange(exchange.getExchangeName(), exchange.isDurable(), exchange.isAutoDelete(), exchange.getArguments());
                BingOperate.exchangeThreadLocal.set(directExchange);
                return directExchange;
            default:
                return null;
        }
    }

}