package com.noServerAssembly.as1.operate;


import com.exception.AssemblyCatServiceException;
import com.utils.ValidateUtils;
import org.springframework.amqp.rabbit.core.RabbitAdmin;

/**
 * 项目名称: pay_core
 *
 * @ClassName BaseOperate
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/27 17:19
 */

public class BaseOperate<T, U> {
    //TODO  正式投放选用该 bean
   // protected RabbitAdmin rabbitAdmin = SpringUtil.getBean(RabbitAdmin.class);
    protected RabbitAdmin rabbitAdmin = null;


    /**
     * 自定义的校验
     *
     * @param object
     */
    public void DefinedValidate(Object object) {
        //ignore  子类需要子类重写
    }

    /**
     * 容器声明
     */
    public U declare(T t) {
        throw new AssemblyCatServiceException("子类若需要需实现改容器声明方法");
    }

    /**
     * 基础校验
     */
    public void validate(Object object) {
        ValidateUtils.validate(object);
        this.DefinedValidate(object);
    }
}