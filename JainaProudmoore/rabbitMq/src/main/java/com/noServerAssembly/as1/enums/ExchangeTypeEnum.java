package com.noServerAssembly.as1.enums;

import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * 项目名称: pay_core
 *
 * @ClassName ExchangeTypeEnum
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/27 16:05
 */
@Getter
@ToString
public enum ExchangeTypeEnum {
    DIRECT("DIRECT", "WALTER_DIRECT", "直连交换机"),
    TOPIC("TOPIC", "WALTER_TOPIC", "订阅交换机"),
    FANOUT("FANOUT", "WALTER_FANOUT", "扇形交换机-广播模式"),
    DEAD_LETTER("DEAD_LETTER", "WALTER_DEAD_LETTER", "死信交换机"),
    DELAY("delay", "WALTER_DELAY_LETTER", "延时交换机"),
    HEADERS("HEADERS", "WALTER_HEADERS", "头部交换机");

    private String type;
    private String name;
    private String desc;


    ExchangeTypeEnum(String type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }

    public static String getNameByType(String type) {
        return Arrays.stream(ExchangeTypeEnum.values())
                .filter(x -> x.getType().equals(type))
                .map(ExchangeTypeEnum::getName)
                .findFirst()
                .orElse("");

    }


    public static ExchangeTypeEnum getEnumByType(String type) {
        return Arrays.stream(ExchangeTypeEnum.values())
                .filter(x -> x.getType().equals(type))
                .findFirst()
                .orElse(null);
    }

}