package com.noServerAssembly.as1.listen;

import com.noServerAssembly.as1.annocation.DeadQueue;
import com.noServerAssembly.as1.annocation.RabbitListenParams;
import lombok.extern.slf4j.Slf4j;
/**
 * 项目名称: pay_core
 *
 * @ClassName DemoMessageListener
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/28 9:23
 */
@Slf4j
@RabbitListenParams(queueName = "demo_queue", exchangeName = "WALTER_DIRECT", isDelay = true, routingKey = "demo_routing_key", deadQueue = @DeadQueue(
        queueName = "dead_queue", routingKey = "dead_routing_Key", ttl = 1000 * 3
))
public class DemoMessageListener extends WalterMessageListener<String> {


    @Override
    public void doHandleMessage(String str) {
        System.out.println("接受到消息了:" + str);
    }

}