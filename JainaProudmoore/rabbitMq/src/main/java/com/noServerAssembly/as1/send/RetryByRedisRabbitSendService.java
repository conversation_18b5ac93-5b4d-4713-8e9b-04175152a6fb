package com.noServerAssembly.as1.send;


import com.noServerAssembly.as1.utils.ProtostuffUtils;
import com.noServerAssembly.as1.vo.WalterMessage;
import com.utils.RedisUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;


/**
 * 项目名称: pay_core
 *
 * @ClassName DefaultRabbitSendService
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/29 14:11
 */
@Slf4j
@Service
@ComponentScan("com.walter.redis_core.**")
@ConditionalOnProperty(prefix = "walter.redis", name = "use", havingValue = "true", matchIfMissing = false)
public class RetryByRedisRabbitSendService extends AbstractSendService {

    private static final String retrySendFailKey = "walter:fail:";


    @Value("${rabbit.retry.count:5}")
    private Integer maxRetryCount;

    @Override
    protected void doConfirmHandler(CorrelationData correlationData) {
        retrySendMessage(correlationData.getReturnedMessage().getBody());
    }

    @Override
    @ConditionalOnBean(RedisTemplate.class)
    protected void doReturnedHandler(ReturnedMessage returned) {
        retrySendMessage(returned.getMessage().getBody());
    }

    /**
     * 重新发送消息
     */
    private void retrySendMessage(byte[] bytes) {
        Optional.of(bytes)
                .map(bs -> ProtostuffUtils.deserialize(bs, WalterMessage.class))
                .ifPresent(walterMessage -> {
                    String messageId = walterMessage.getMessageId();
                    if (!RedisUtil.hasKey(messageId)) {
                        RedisUtil.set(messageId, 1, 3600);
                    }
                    Integer count = (Integer) RedisUtil.get(messageId);
                    log.info("当前消息{}重试第{}次", messageId, count);
                    if ((Integer) RedisUtil.get(messageId) > maxRetryCount) {
                        //超过限制
                        doCompensate(walterMessage);
                        return;
                    }
                    // 发送对应的消息
                    defaultRetrySend(walterMessage);
                    RedisUtil.incr(messageId, 1);
                });
    }

    /**
     * 超过最大限制限制
     */
    private void doCompensate(WalterMessage walterMessage) {
        String key = retrySendFailKey.concat(walterMessage.getMessageId());
        RedisUtil.set(key, FailWalterMessage.builder().retryCount(maxRetryCount).walterMessage(walterMessage).createTime(LocalDateTime.now()).build());
    }

    @Override
    public void afterSend(WalterMessage walterMessage) {
        //ignore do something after send message

    }

    @Override
    public void beforeSend(WalterMessage walterMessage) {
        //ignore do something before send message
    }


    @Data
    @Builder
    static class FailWalterMessage {
        /**
         * 重试次数
         */
        private Integer retryCount;
        /**
         * 消息体
         */
        private WalterMessage walterMessage;
        /**
         * 时间
         */
        private LocalDateTime createTime;
    }
}