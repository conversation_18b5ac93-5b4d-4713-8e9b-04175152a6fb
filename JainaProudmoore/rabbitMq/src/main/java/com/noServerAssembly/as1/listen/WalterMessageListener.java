package com.noServerAssembly.as1.listen;


import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.exception.AssemblyCatServiceException;
import com.noServerAssembly.as1.Constants;
import com.noServerAssembly.as1.annocation.RabbitListenParams;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.operate.BingOperate;
import com.noServerAssembly.as1.vo.WalterMessage;
import com.rabbitmq.client.Channel;
import com.utils.RedisUtil;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 项目名称: pay_core
 *
 * @ClassName WalterMessageListener
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/28 9:17
 */
@Slf4j
public abstract class WalterMessageListener<T> implements ChannelAwareMessageListener, ApplicationRunner {

    @Setter
    private AcknowledgeMode ack;
    @Resource
    private MessageListen messageListen;

    /**
     * 获取到注解参数
     */
    protected RabbitListenParams params = this.getClass().getAnnotation(RabbitListenParams.class);

    /**
     * 监听到消息
     */
    @SneakyThrows
    @Override
    public void onMessage(Message message, Channel channel) {
        boolean doSuccess = false;
        //获取到消息体
        WalterMessage mqMessage = JSONObject.parseObject(new String(message.getBody()), WalterMessage.class);
        //获取消息唯一标识
        String messageId = mqMessage.getMessageId();
        try {
            //判断当前消息是否存在定向消息(是否可消费) 获取消费标识
            String orientConsume = message.getMessageProperties().getAppId();
            //定向消费（只对手动ACK确认机制生效），如果配置了定向消费，如果发起端指定了消费端消费，其他消费端消费了直接拒绝
            if (StringUtils.isNotBlank(orientConsume)) {
                if (!orientConsume.equals(Constants.ORIENT_CONSUME)) {
                    log.error("不能消费当前消息 跳出");
                    return;
                }
            }
            //幂等性设置
            if (isActive(messageId)) {
                //业务处理
                doHandleMessage((T) mqMessage.getMessageBody());
                //业务处理成功
                doSuccess = true;
            } else {
                log.error("该消息{}已被消费", messageId);
            }
        } catch (Exception e) {
            log.error("队列" + message.getMessageProperties().getConsumerQueue() + "消息接收处理异常：" + e.getMessage());
            if (log.isDebugEnabled()) {
                e.printStackTrace();
            }
        } finally {
            //手动ack
            if (ack.isManual()) {
                if (doSuccess) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    //业务处理失败 删除当前幂等性标志位
                    RedisUtil.del(messageId);
                    //
                    if (RedisUtil.hasKey(Constants.RETRY_KEY.concat(messageId))) {
                        Integer count = (Integer) RedisUtil.get(Constants.RETRY_KEY.concat(messageId));
                        if (count >= params.retry()) {
                            log.error("消息处理失败 重试此处已达到上线:{}", params.retry());
                            RedisUtil.set(Constants.RABBIT_ERROR.concat(messageId), mqMessage);
                            //重试达到上线 放到redis后直接ack
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        RedisUtil.incr(Constants.RETRY_KEY.concat(messageId), 1);
                    } else {
                        RedisUtil.set(Constants.RETRY_KEY.concat(messageId), 1);
                    }
                    //删除mq幂等性 重新投递消息
                    RedisUtil.del(messageId);
                    //requeue为true表示让消息重回队列，放入队列尾部，如果为false则会删除当前消息
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
                }
            }
        }
    }


    /**
     * 处理业务
     */
    public abstract void doHandleMessage(T t);

    /**
     * 消费着幂等性处理
     */
    private boolean isActive(String messageId) {
        if (RedisUtil.hasKey(messageId)) {
            return false;
        }
        RedisUtil.set(messageId, messageId, 300, TimeUnit.SECONDS);
        return true;
    }

    /**
     * bean对象完成初始化
     */
    @Override
    public void run(ApplicationArguments args) {
        //进行参数校验
        checkParams();
        //创建绑定关系
        if (SpringUtil.getBean(BingOperate.class).queueBind(params)) {
            //开启注册监听器 如果延迟队列 直接监听死信队列
            if (params.isDelay()) {
                //监听死信队列 作为当前队列得延迟队列
                this.messageListen.addMessageLister(params.deadQueue().queueName(), this, params.isAck());
            } else {
                //监听正常队列
                this.messageListen.addMessageLister(params.queueName(), this, params.isAck());
            }

        }
    }

    /**
     * 进行注解参数校验
     */
    private void checkParams() {
        String queueName = params.queueName();
        if (StringUtils.isEmpty(queueName)) {
            throw new AssemblyCatServiceException("[@RabbitListenParams]队列名称[queueName()]不能为空");
        }
        ExchangeTypeEnum exchangeTypeEnum = params.exchangeType();
        if (!exchangeTypeEnum.equals(ExchangeTypeEnum.FANOUT) && StringUtils.isEmpty(params.routingKey())) {
            throw new AssemblyCatServiceException("[@RabbitListenParams]非扇形交换机路由键[routingKey]不能为空");
        }
    }

}