package com.noServerAssembly.as1.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 项目名称: pay_core
 *
 * @ClassName WalterQueue
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/29 16:26
 */
@Data
@NoArgsConstructor
public class WalterQueue {

    public WalterQueue(String name){
        this.name=name;
    }

    /**
     * 队列名称
     * 队列命名规范   交换机.模块名称.自定义
     * 死信队列名称 dlx.队列名称
     */
    @NotNull(message = "队列名称不能为空")
    private String name;
    /**
     * 是否持久化
     * 持久化会存盘，服务器重启时不会丢失相关信息
     */
    private boolean durable=true;
    /**
     * 是否排他
     * 如果是排他，则该队列对首次声明他的连接有效，并在连接断开时自动删除
     * 注意：
     * 1. 同一个连接的其他的Channel是可以连接该排他队列的
     * 2. 首次是说其他连接就不同创建同名的排他队列
     * 适用于一个客户端同时发送和读取消息
     */
    private boolean exclusive=false;
    /**
     * 是否自动删除
     * 自动删除的前提是至少有一个队列或者交换机与这个交互机绑定，之后所有与这个交换机绑定的队列或者交换机都与此解绑
     */
    private boolean autoDelete=false;

    /**
     * 是否需要死信队列 默认false
     * 死信队列默认不创建
     */
    private boolean needDeadQueue=false;
    /**
     * 结构化参数
     */
    private Map<String, Object> arguments;
}