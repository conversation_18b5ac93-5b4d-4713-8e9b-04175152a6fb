package com.noServerAssembly.as1.annocation;


import java.lang.annotation.*;

/**
 * 死信队列
 */
@Target({ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DeadQueue {
    /**
     * 队列名称
     */
    String queueName() default "";

    /**
     * 路由键
     */
    String routingKey() default "";

    /**
     * 使用配置x-expires修饰的消息队列会在其指定时间内，未被使用将会被删除！ 单位毫秒 默认三十分钟
     */
    int ttl() default 30 * 60 * 1000;
}
