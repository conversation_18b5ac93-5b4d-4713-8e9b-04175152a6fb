package com.noServerAssembly.as1.config;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.noServerAssembly.as1.Constants;
import com.noServerAssembly.as1.enums.ConfirmTypeEnum;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.operate.ExchangeOperate;
import com.noServerAssembly.as1.vo.WalterExchange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import java.util.Arrays;

/**
 * 项目名称: pay_core
 *
 * @ClassName RabbitMqConfig
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/27 15:05
 */

@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "walter.rabbitmq", name = "use", havingValue = "false", matchIfMissing = false)
public class RabbitMqConfig {

    @Value("${spring.application.name}")
    private String springApplicationName;
    /**
     * rabbitMQ服务器的地址
     */
    @Value("${spring.rabbitmq.host}")
    private String host;

    /**
     * rabbitMQ端口
     */
    @Value("${spring.rabbitmq.port}")
    private int port;

    /**
     * rabbitMQ用户名
     */
    @Value("${spring.rabbitmq.username}")
    private String username;
    /**
     * rabbitMQ密码
     */
    @Value("${spring.rabbitmq.password}")
    private String password;
    /**
     * rabbitMQ虚拟机 这里默认 /
     */
    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;
    /**
     * 发送端确认模式，默认是NONE 可变为 CORRELATED
     */
    @Value("${spring.rabbitmq.confirmType:NONE}")
    private String confirmType;

    /**
     * 发送端找不到队列是否回调 默认不回调
     */
    @Value("${spring.rabbitmq.publisher-returns:false}")
    private boolean publisherReturns;

    /**
     * mandatory 找不到路由规则的消息会直接抛弃 默认false
     */
    @Value("${spring.rabbitmq.mandatory:false}")
    private boolean mandatory;

    /**
     * 定向消费判断，默认是空
     */
    @Value("${spring.rabbitmq.orient.consume:}")
    private String orientConsume;

    /**
     * 普通的connectionFactory 发送端发送消息后，不需要确认，如果发送成功后如果客户端挂掉，那么消息会丢失，用于一般场景
     *
     * @return
     */
    @Bean(name = "connectionFactory")
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setAddresses(this.host);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPort(port);
        //设置virtualHost。
        connectionFactory.setVirtualHost(this.virtualHost);
        if (ConfirmTypeEnum.CORRELATED.getType().equals(confirmType)) {
            connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        }
        if (publisherReturns) {
            connectionFactory.setPublisherReturns(true);
        }
        //setCacheMode：设置缓存模式，共有两种，CHANNEL和CONNECTION模式。
        //1、CONNECTION模式，这个模式下允许创建多个Connection，会缓存一定数量的Connection，每个Connection中同样会缓存一些Channel，
        // 除了可以有多个Connection，其它都跟CHANNEL模式一样。
        //2、CHANNEL模式，程序运行期间ConnectionFactory会维护着一个Connection，
        // 所有的操作都会使用这个Connection，但一个Connection中可以有多个Channel，
        // 操作rabbitmq之前都必须先获取到一个Channel，
        // 否则就会阻塞（可以通过setChannelCheckoutTimeout()设置等待时间），
        // 这些Channel会被缓存（缓存的数量可以通过setChannelCacheSize()设置）；
        connectionFactory.setCacheMode(CachingConnectionFactory.CacheMode.CONNECTION);   //设置CONNECTION模式，可创建多个Connection连接
        //设置每个Connection中缓存Channel的数量，不是最大的。操作rabbitmq之前（send/receive message等）
        // 要先获取到一个Channel.获取Channel时会先从缓存中找闲置的Channel，如果没有则创建新的Channel，
        // 当Channel数量大于缓存数量时，多出来没法放进缓存的会被关闭。
        connectionFactory.setChannelCacheSize(25);
        //单位：毫秒；配合channelCacheSize不仅是缓存数量，而且是最大的数量。
        // 从缓存获取不到可用的Channel时，不会创建新的Channel，会等待这个值设置的毫秒数
        //同时，在CONNECTION模式，这个值也会影响获取Connection的等待时间，
        // 超时获取不到Connection也会抛出AmqpTimeoutException异常。
        connectionFactory.setChannelCheckoutTimeout(600);
        //仅在CONNECTION模式使用，设置Connection的缓存数量。
        connectionFactory.setConnectionCacheSize(3);
        //setConnectionLimit：仅在CONNECTION模式使用，设置Connection的数量上限。
        connectionFactory.setConnectionLimit(10);
        Constants.SPRING_APPLICATION_NAME = this.springApplicationName;
        //设置CONFIRM_TYPE为全局变量
        Constants.CONFIRM_TYPE = ConfirmTypeEnum.valueOf(this.confirmType);
        if (orientConsume != null) {
            Constants.ORIENT_CONSUME = this.orientConsume;
        }
        return connectionFactory;
    }


    /**
     * rabbitTemplate非单例 提高扩展性
     *
     * @return
     */
    @Bean(name = "rabbitTemplate")
    @Scope("prototype")
    public RabbitTemplate rabbitTemplate() {
        RabbitTemplate template = new RabbitTemplate(this.connectionFactory());
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        om.registerModule(new JavaTimeModule());
        Jackson2JsonMessageConverter json = new Jackson2JsonMessageConverter(om);
        template.setMessageConverter(json);
        template.setUsePublisherConnection(true);
        template.setMandatory(mandatory);
        return template;
    }


    /**
     * 注册rabbitAdmin 方便管理
     */
    @Bean
    public RabbitAdmin rabbitAdmin() {
        return new RabbitAdmin(this.connectionFactory());
    }


    @Bean
    public void initExchange() {
        ExchangeOperate operate = SpringUtil.getBean(ExchangeOperate.class);
        //初始化默认交换器 四种
        Arrays.stream(ExchangeTypeEnum.values()).forEach(x -> operate.declare(WalterExchange.newInstance(x)));
    }
}