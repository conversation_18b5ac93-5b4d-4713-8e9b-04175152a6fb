package com.noServerAssembly.as1.vo;



import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 项目名称: pay_core
 *
 * @ClassName WalterExchange
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/27 16:03
 */
@Getter
@Setter
@ToString
public class WalterExchange {


    private WalterExchange() {
    }

    private WalterExchange(String exchangeName, ExchangeTypeEnum type) {
        this.type = type;
        this.exchangeName = exchangeName;
    }

    /**
     * 交换机名称
     */
    @NotNull(message = "交换机名称不能为空")
    protected String exchangeName;

    /**
     * 交换机的类型
     */
    @NotNull(message = "交换机的类型不能为空")
    private ExchangeTypeEnum type;
    /**
     * 是否持久化
     * 持久化可以将交换机存盘，在服务器重启的时候不会丢失相关的信息
     * 默认是开启持久化
     */
    protected boolean durable = Boolean.TRUE;
    /**
     * 是否自动删除
     * 自动删除的前提是至少有一个队列或者交换机与这个交互机绑定，之后所有与这个交换机绑定的队列或者交换机都与此解绑
     */
    protected boolean autoDelete = Boolean.FALSE;

    /**
     * 自定义属性参数
     * 比如：alternate-exchange
     */
    protected Map<String, Object> arguments;


    public static WalterExchange newInstance() {
        return new WalterExchange();
    }

    public static WalterExchange newInstance(String exchangeName, ExchangeTypeEnum type) {
       return new WalterExchange(exchangeName, type);
    }

    public static WalterExchange newInstance(ExchangeTypeEnum type) {
        return new WalterExchange(type.getName(), type);
    }
}