package com.noServerAssembly.as2.testdemo;


import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as2.server.RabbitSendServer;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023-12-14 17:58
 * @PackageName:com.bot.cat.demo
 * @ClassName: mqSend
 * @Description: TODO
 * @Version 1.0
 */
@Service
@RestController
@RequestMapping(value = "/MqSendTest")
@Component
public class MqSendTest {

    @RequestMapping("/send/{delayMilliseconds}")
    public void send(@RequestBody Object message, @PathVariable(value = "delayMilliseconds", required = false) int delayMilliseconds) {
        if (delayMilliseconds > 0) {
            RabbitSendServer.send(ExchangeTypeEnum.DIRECT, message, "cat_ex", "cat_rk", delayMilliseconds);
        } else {
            RabbitSendServer.send(ExchangeTypeEnum.DIRECT, message, "cat_rk", "cat_ex");
        }
    }
}
