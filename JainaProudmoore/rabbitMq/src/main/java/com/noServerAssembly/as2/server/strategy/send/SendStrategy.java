package com.noServerAssembly.as2.server.strategy.send;


import com.noServerAssembly.as1.Constants;
import com.noServerAssembly.as1.enums.ConfirmTypeEnum;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as2.constants.RabbitConstants;
import com.noServerAssembly.as2.vo.MessageVo;
import com.utils.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Properties;


import static com.noServerAssembly.as2.constants.RabbitConstants.X_DELAY;

/**
 * <AUTHOR>
 * @Date 2023-12-18 9:45
 * @PackageName:com.bot.cat.assembly.rabbit231214.server.strategy
 * @ClassName: SendService
 * @Description: 消息发送策略
 * @Version 1.0
 */
@Slf4j
public abstract class SendStrategy implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnsCallback, ApplicationRunner {


    @Resource
    protected RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name:}")
    protected String applicationName;

    /**
     * @param ifPersistent 是否开启持久化，true开启，false不开启
     * @param expireTime   是否超时  默认不会超时，单位毫秒
     */
    public void send(
            //消息体
            Object mesContext,
            //交换机
            ExchangeTypeEnum exchangeType,
            //交换机名称
            String exchangeName,
            //路由键
            String routingKey,
            //消息是否持久化
            boolean ifPersistent,
            //消息ttl
            int expireTime,
            //延时时间
            int delayed,
            //延时时间
            Properties properties) {
        MessageVo messageVo = MessageVo.newInstance(mesContext, exchangeType, exchangeName, routingKey, applicationName,
                ifPersistent, expireTime, delayed, properties);
        this.send(messageVo, createMessagePostProcessor(messageVo, expireTime, ifPersistent, delayed));
    }

    /**
     * @param messagePostProcessor 消息属性
     * @return void
     * @<NAME_EMAIL>
     * @date 2022/4/28 9:46
     **/
    protected void send(MessageVo message, MessagePostProcessor messagePostProcessor) {
        //进行参数校验
        message.validation();
        //发送前置事项
        beforeSend(message);
        //发送消息
        rabbitTemplate.convertAndSend(message.getExchangeName(), message.getRoutingKey(), message,
                messagePostProcessor, message.createCorrelationData());
        //发送完后置事项
        afterSend(message);
    }


    /**
     * 封装消息属性
     */
    public MessagePostProcessor createMessagePostProcessor(MessageVo messageVo, int expireTime, boolean ifPersistent, int delayed) {
        return (message) -> {
            /**
             * 单位是毫秒，如果一条消息设置了TTL属性或者进入了设置TTL属性的队列，那么这条消息如果在TTL设置的时间内没有被消费，则会成为“死信”，如果同时配置了队列的TTL和消息的TTL，那么较小的那个值将会被使用，有两种方法设置TTL
             * 如果设置了队列的TTL属性，那么一旦过期，就会被队列丢弃（如果配置了死信队列被丢到死信队列中），而第二种方式，消息即使过期，也不一定马上被丢弃，
             * 因为消息是否过期是在即将投递到消费者之前判定的，如果当前队列有严重的消息积压情况，则已过期的消息也许还能存活较长时间。如果不设置TTL，表示消息永远不会过期i，
             * 如果将TTL设置为0，则表示除非此时可以直接投递该消息到消费者，否则该消息将会被丢弃。
             */
            if (expireTime > 0) {
                message.getMessageProperties().setExpiration(expireTime + "");
            }
            //是否开启消息持久化
            if (ifPersistent) {
                message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            }
            // 消息延迟时间 入参为秒 需要转换为毫秒
            if (delayed > 0) {
                message.getMessageProperties().setHeader(X_DELAY, delayed);
            }
            //指定消费者
            if (StringUtils.isNotEmpty(Constants.ORIENT_CONSUME)) {
                message.getMessageProperties().setAppId(Constants.ORIENT_CONSUME);
            }
            // 设置字符集
            message.getMessageProperties().setContentType(RabbitConstants.UTF_8);
            message.getMessageProperties().setMessageId(StringUtils.isEmpty(messageVo.getMessageId()) ?
                    System.currentTimeMillis() + UUIDUtils.getUUID() : messageVo.getMessageId());
            if (!Objects.isNull(messageVo.getProperties()) && messageVo.getProperties().containsKey(RabbitConstants.APP_ID)) {
                message.getMessageProperties().setAppId(messageVo.getProperties().getProperty(RabbitConstants.APP_ID));
            }
            return message;
        };
    }

    //TODO ====================================以下为实现框架封装逻辑========================

    /**
     * 初始化发送方法：如果是confirm模式初始化对应的rabbitTemplate
     * 这里 rabbitTemplate不是单例的，如果需要自行定义，那么自行扩展
     */
    @Override
    public void run(ApplicationArguments args) {
        if (ConfirmTypeEnum.CORRELATED.equals(Constants.CONFIRM_TYPE)) {
            //设置confirm模式
            this.rabbitTemplate.setConfirmCallback(this);
            //设置投递失败逻辑
            this.rabbitTemplate.setReturnsCallback(this);
        }
    }


    /**
     * confirm模式
     * 当消息从生产者发送到交换机时，不论发送是否成功都会返回一个confirmCallback，
     * 当发送成功时这个confirmCallback为true，
     * 当发送失败时这个confirmCallback为false。
     */
    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String reason) {
        if (!ack) {
            log.error("消息ID:【{}】发送到交换机失败 原因:【{}】 进入confirm阶段", correlationData.getId(), reason);
            //进入confirm模式逻辑
            doConfirmHandler(correlationData, reason);
        }

    }

    /**
     * 消息从交换机发送到队列时，若投递失败则会返回一个returnCallback
     * [message=(Body:'[' MessageProperties [headers={spring_returned_message_correlation=16511349491012d1eee3fdf884614acf57a434418e739,
     * __TypeId__=com.walter.mq_core.vo.WalterMessage}, contentType=UTF-8, contentEncoding=UTF-8, contentLength=0,
     * receivedDeliveryMode=PERSISTENT, priority=0, deliveryTag=0]), replyCode=312, replyText=NO_ROUTE, exchange=WALTER_DIRECT,
     * routingKey=demo_key]
     * 交换机返回消息的方法-消息未送达队列触发回调
     * （1）常用于交换机无法路由回退消息。
     * （2）如果交换机绑定了备用交换机则是路由到备用交换机，此方法不回调。
     * （3）如果是发送到延迟交换机则回调此方法，所以如果使用延迟交换机则要对延迟交换机回调的消息过滤。
     */
    @Override
    public void returnedMessage(ReturnedMessage returned) {
        log.error("消息ID:【{}】投递失败,开始进行投递失败逻辑", returned.getMessage().getMessageProperties().getMessageId());
        //进入returned阶段
        doReturnedHandler(returned);
    }


    /**
     * 消息发送至交换器失败逻辑
     */
    protected abstract void doConfirmHandler(CorrelationData correlationData, String reason);

    /**
     * 消息投递失败
     */
    protected abstract void doReturnedHandler(ReturnedMessage returned);

    /**
     * 发送消息后逻辑
     */
    protected void afterSend(MessageVo messageVo) {
        //ignore do something after send message  implements for sun
    }

    /**
     * 发送消息前逻辑
     */
    protected void beforeSend(MessageVo messageVo) {
        //ignore do something before send message  implements for sun
    }


}
