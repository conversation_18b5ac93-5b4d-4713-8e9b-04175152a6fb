package com.noServerAssembly.as2.testdemo;


import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import java.io.IOException;



/**
 * <AUTHOR>
 * @Date 2023-12-14 17:58
 * @PackageName:com.bot.cat.demo
 * @ClassName: mqSend
 * @Description: TODO
 * @Version 1.0
 */

@Component
public class AnnotationListenerTest {


    @RabbitMonitor(queues = "delay.queue", exchangeName = "cat_ex", routingKey = "cat_rk", isDelayed = true, isAck = true)
    public void onExpired2(String msg, Message message, Channel channel) {
        System.out.println("msg======================: " + msg);
    }


    @RabbitMonitor(queues = {"cp0", "cp1", "cp2", "cp3"}, exchangeName = "cat_ex", routingKey = "cat_rk_cp", isDelayed = true)
    public void onExpired3(String msg, Message message, Channel channel) throws IOException {
        //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }


}
