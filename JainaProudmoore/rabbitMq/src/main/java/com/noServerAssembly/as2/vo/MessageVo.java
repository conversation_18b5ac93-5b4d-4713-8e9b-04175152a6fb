package com.noServerAssembly.as2.vo;


import com.exception.AssemblyCatServiceException;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as1.enums.TargetEnum;
import com.noServerAssembly.as1.utils.ProtostuffUtils;
import com.utils.UUIDUtils;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.CorrelationData;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2023-12-18 9:51
 * @PackageName:com.bot.cat.assembly.rabbit231214.vo
 * @ClassName: MessageVo
 * @Description: TODO
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageVo {

    /**
     * 交换机类型
     */
    private ExchangeTypeEnum exchangeType;
    /**
     * 交换机名称
     */
    private String exchangeName;
    /**
     * 队列名称
     */
    private String queueName;
    /**
     * 消息Id
     */
    private String messageId;

    /**
     * 发送服务
     */

    private String applicationName;
    /**
     * 消息体
     */
    private Object messageBody;
    /**
     * 目标
     */
    private TargetEnum target;
    /**
     * 路由键
     */
    private String routingKey;
    /**
     * 消息优先级
     */
    private int messagePriority = 0;
    /**
     * 消息是否持久化
     */
    private boolean ifPersistent;
    /**
     * 消息过期时间
     */
    private Integer expireTime;
    /**
     * 消息延迟时间
     */
    private Integer delayed;

    /**
     * 消息延迟时间
     */
    private String errorMsg;
    /**
     * 其余参数
     */
    private Properties properties;

    public static MessageVo newInstance(Object mesContext,
                                        ExchangeTypeEnum exchangeType,
                                        String exchangeName,
                                        String routingKey,
                                        String applicationName,
                                        boolean ifPersistent,
                                        int expireTime,
                                        int delayed,
                                        Properties properties) {
        MessageVo message = new MessageVo();
        //消息体
        message.setMessageBody(mesContext);
        //交换机类型
        message.setExchangeType(exchangeType);
        //交换机名称
        message.setExchangeName(StringUtil.isNotEmpty(exchangeName) ? exchangeName : exchangeType.getName());
        //路由键
        message.setRoutingKey(routingKey);
        //消息是否持久化
        message.setIfPersistent(ifPersistent);
        //消息过期时间
        message.setExpireTime(expireTime);
        //应用
        message.setApplicationName(applicationName);
        //消息延迟时间
        message.setDelayed(delayed);
        //目标 暂时不知道有什么用处
        message.setTarget(TargetEnum.PROVIDE);
        //交换机名称
        message.setMessageId(System.currentTimeMillis() + UUIDUtils.getUUID());
        //设置其余参数
        message.setProperties(properties);
        return message;
    }


    public CorrelationData createCorrelationData() {
        CorrelationData correlationData = new CorrelationData(this.getMessageId());
        //设置参数
        correlationData.setReturnedMessage(new Message(ProtostuffUtils.serialize(this)));
        return correlationData;
    }

    /**
     * 进行消息参数校验
     */
    public void validation() {
        if (TargetEnum.CONSUME.equals(target) && StringUtils.isEmpty(queueName)) {
            throw new AssemblyCatServiceException("消费方队列不能为空");
        }
        if (ExchangeTypeEnum.DIRECT.getName().equals(exchangeName) || ExchangeTypeEnum.TOPIC.getName().equals(exchangeName)) {
            //直连交换机
            if (StringUtils.isEmpty(routingKey)) {
                throw new AssemblyCatServiceException("直连交换机路由键不能为空");
            }
        }
        if (ExchangeTypeEnum.HEADERS.getName().equals(exchangeName)) {
            //头部交换机
            throw new AssemblyCatServiceException("头部交换机已弃用,请选择其他类型交换机");
        }
    }
}
