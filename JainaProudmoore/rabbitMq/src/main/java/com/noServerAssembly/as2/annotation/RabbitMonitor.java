package com.noServerAssembly.as2.annotation;


import com.noServerAssembly.as1.annocation.DeadQueue;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

import static com.noServerAssembly.as1.enums.ExchangeTypeEnum.DIRECT;


/**
 * <AUTHOR>
 * 当前注解配置到方法代表这个方法是一个监听器 需要配置队列等信息才能接收到消息
 * 当前注解配置到类上不需要配置队列 代表当前类为动态创建队列绑定关系类
 * 需要配置类为 com.bot.cat.assembly.rabbit231214.server.strategy.listen.CustomMessageListener 的子类
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@RabbitListener
public @interface RabbitMonitor {


    /**
     * 是否需要持久化
     */
    boolean durable() default true;

    /**
     * 是否自动删除
     */
    boolean autoDelete() default false;

    /**
     * 是否需要手动确认
     */
    boolean isAck() default false;

    boolean isDelayed() default false;

    /**
     * 路由键
     */
    String routingKey() default "";

    /**
     * 交换机类型 默认直连交换机
     */
    ExchangeTypeEnum exchangeType() default DIRECT;

    /**
     * 交换机名称
     */
    String exchangeName() default "";

    /**
     * 死信队列
     */
    DeadQueue deadQueue() default @DeadQueue();

    /**
     * 消费失败重试次数
     */
    int retry() default 0;

    //=====================================原生注解属性 照搬过来的不要动===============================

    /**
     * 队列名称
     */
    @AliasFor(annotation = RabbitListener.class)
    String[] queues() default {};

    @AliasFor(annotation = RabbitListener.class)
    String id() default "";

    @AliasFor(annotation = RabbitListener.class)
    String containerFactory() default "";

    @AliasFor(annotation = RabbitListener.class)
    Queue[] queuesToDeclare() default {};

    @AliasFor(annotation = RabbitListener.class)
    boolean exclusive() default false;

    @AliasFor(annotation = RabbitListener.class)
    String priority() default "";

    @AliasFor(annotation = RabbitListener.class)
    String admin() default "";

    @AliasFor(annotation = RabbitListener.class)
    QueueBinding[] bindings() default {};

    @AliasFor(annotation = RabbitListener.class)
    String group() default "";

    @AliasFor(annotation = RabbitListener.class)
    String returnExceptions() default "";

    @AliasFor(annotation = RabbitListener.class)
    String errorHandler() default "";

    @AliasFor(annotation = RabbitListener.class)
    String concurrency() default "";

    @AliasFor(annotation = RabbitListener.class)
    String autoStartup() default "";

    @AliasFor(annotation = RabbitListener.class)
    String executor() default "";

    @AliasFor(annotation = RabbitListener.class)
    String ackMode() default "";

    @AliasFor(annotation = RabbitListener.class)
    String replyPostProcessor() default "";

    @AliasFor(annotation = RabbitListener.class)
    String messageConverter() default "";

    @AliasFor(annotation = RabbitListener.class)
    String replyContentType() default "";

    @AliasFor(annotation = RabbitListener.class)
    String converterWinsContentType() default "true";

}
