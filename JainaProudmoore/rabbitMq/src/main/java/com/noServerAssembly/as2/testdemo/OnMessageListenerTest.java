package com.noServerAssembly.as2.testdemo;


import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.noServerAssembly.as2.server.BindingQueueServer;
import com.noServerAssembly.as2.server.RabbitSendServer;
import com.noServerAssembly.as2.server.strategy.listen.CustomMessageListener;
import com.noServerAssembly.as2.vo.MessageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023-12-20 10:23
 * @PackageName:com.bot.cat.assembly.rabbit231214.testdemo
 * @ClassName: OnMessageListenerTest
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
@RabbitMonitor(isAck = true, isDelayed = true)
@RestController
@RequestMapping("/onMessageListenerTest")
public class OnMessageListenerTest extends CustomMessageListener {

    @PostMapping("/sendMes")
    public void sendMes(@RequestParam("routingKey") String routingKey) {
        RabbitSendServer.send(ExchangeTypeEnum.DIRECT, "this is message", routingKey);
    }

    @PostMapping("/testDemo")
    public void testDemo(@RequestParam("queue") String queue) {
        BindingQueueServer.doBinding(queue, "test_ck", this);
    }


    @Override
    public void doHandleMessage(MessageVo mqMessage) {
        log.info("接收到消息:{}", mqMessage);
    }
}
