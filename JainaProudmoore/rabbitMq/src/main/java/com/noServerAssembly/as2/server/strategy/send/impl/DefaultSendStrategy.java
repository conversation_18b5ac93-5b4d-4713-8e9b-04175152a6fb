package com.noServerAssembly.as2.server.strategy.send.impl;


import com.noServerAssembly.as2.server.strategy.send.SendStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-12-18 9:47
 * @PackageName:com.bot.cat.assembly.rabbit231214.server.strategy
 * @ClassName: DefaultSendService
 * @Description: 默认的发送逻辑 什么都不干
 * @Version 1.0
 */
@Service
@Slf4j
public class DefaultSendStrategy extends SendStrategy {


    @Override
    protected void doConfirmHandler(CorrelationData correlationData, String reason) {

    }

    @Override
    protected void doReturnedHandler(ReturnedMessage returned) {

    }
}
