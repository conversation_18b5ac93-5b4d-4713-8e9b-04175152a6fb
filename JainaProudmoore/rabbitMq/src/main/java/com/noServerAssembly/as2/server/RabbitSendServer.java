package com.noServerAssembly.as2.server;

import cn.hutool.extra.spring.SpringUtil;

import com.exception.AssemblyCatServiceException;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as2.server.strategy.send.SendStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2023-12-18 9:39
 * @PackageName:com.bot.cat.assembly.rabbit231214.utils
 * @ClassName: RabbitSendServer
 * @Description: rabbit发送消息服务
 * @Version 1.0
 */
@Slf4j
@Service
public final class RabbitSendServer {


    @Value("${rabbit.send.beanName:retrySendStrategy}")
    public String beanName;


    /**
     * @param exchangeType 交换机类型
     * @param mesContext   消息体
     * @param routingKey   路由键
     */
    public static void send(ExchangeTypeEnum exchangeType, Object mesContext, String routingKey) {
        send(exchangeType, mesContext, routingKey, null);
    }

    /**
     * @param exchangeType 交换机类型
     * @param mesContext   消息体
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     */
    public static void send(ExchangeTypeEnum exchangeType, Object mesContext, String routingKey, String exchangeName) {
        send(exchangeType, mesContext, exchangeName, routingKey, 0);
    }


    /**
     * @param exchangeType 交换机类型
     * @param mesContext   消息体
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     * @param delayed      延时时间
     */
    public static void send(ExchangeTypeEnum exchangeType, Object mesContext, String exchangeName, String routingKey, int delayed) {
        send(exchangeType, mesContext, routingKey, exchangeName, 0, delayed);
    }

    /**
     * @param exchangeType 交换机类型
     * @param mesContext   消息体
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     * @param delayed      延时时间
     * @param expireTime   ttl
     */
    public static void send(ExchangeTypeEnum exchangeType, Object mesContext, String exchangeName, String routingKey, int delayed, int expireTime) {
        send(exchangeType, mesContext, routingKey, exchangeName, expireTime, delayed, true);
    }

    /**
     * @param exchangeType 交换机类型
     * @param mesContext   消息体
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     * @param delayed      延时时间
     * @param expireTime   ttl
     * @param ifPersistent 消息是否持久化
     */
    public static void send(
            //交换机
            ExchangeTypeEnum exchangeType,
            //消息体
            Object mesContext,
            //交换机名称
            String exchangeName,
            //路由键
            String routingKey,
            //延时时间
            int delayed,
            //ttl
            int expireTime,
            //消息是否持久化
            boolean ifPersistent) {
        send(exchangeType, mesContext, exchangeName, routingKey, expireTime, delayed, ifPersistent, new Properties());
    }

    /**
     * @param exchangeType 交换机类型
     * @param mesContext   消息体
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     * @param delayed      延时时间
     * @param expireTime   ttl
     * @param ifPersistent 消息是否持久化
     * @param properties   其余属性
     */
    public static void send(
            //交换机
            ExchangeTypeEnum exchangeType,
            //消息体
            Object mesContext,
            //交换机名称
            String exchangeName,
            //路由键
            String routingKey,
            //延时时间
            int delayed,
            //ttl
            int expireTime,
            //消息是否持久化
            boolean ifPersistent,
            //其余属性
            Properties properties) {
        SpringUtil.getBean(RabbitSendServer.class).getRabbitTemplate()
                .send(mesContext, exchangeType, exchangeName, routingKey, ifPersistent, expireTime, delayed, properties);
    }

    /**
     * 获取
     */
    private SendStrategy getRabbitTemplate() {
        try {
            return SpringUtil.getBean(beanName, SendStrategy.class);
        } catch (Exception e) {
            log.error("通过配置beanName:【{}】未找到Bean对象", beanName);
            throw new AssemblyCatServiceException("通过配置beanName:[" + beanName + "]未找到Bean对象");
        }
    }

}
