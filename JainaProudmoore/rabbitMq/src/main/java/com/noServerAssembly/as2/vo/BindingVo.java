package com.noServerAssembly.as2.vo;


import com.noServerAssembly.as1.annocation.DeadQueue;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.noServerAssembly.as2.server.strategy.listen.CustomMessageListener;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.util.StringUtils;



/**
 * <AUTHOR>
 * @Date 2023-12-15 9:34
 * @PackageName:com.bot.cat.assembly.rabbit231214.vo
 * @ClassName: BindingVo
 * @Description: TODO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BindingVo {

    /**
     * 构造器
     */
    public BindingVo(String queueName, String routingKey) {
        this.routingKey = routingKey;
        this.exchangeType = ExchangeTypeEnum.DIRECT;
        this.exchangeName = ExchangeTypeEnum.DIRECT.getName();
        this.queues = new String[1];
        queues[0] = queueName;
        this.durable = true;
        this.autoDelete = false;
        this.isAck = false;
        this.isDelayed = false;
    }

    /**
     * 构造器
     */
    public BindingVo(String queueName, String routingKey, CustomMessageListener messageListener) {
        RabbitMonitor rabbitMonitor = messageListener.getClass().getAnnotation(RabbitMonitor.class);
        String[] queues = new String[1];
        queues[0] = queueName;
        this.queues = queues;
        this.durable = rabbitMonitor.durable();
        this.autoDelete = rabbitMonitor.autoDelete();
        this.isAck = rabbitMonitor.isAck();
        this.isDelayed = rabbitMonitor.isDelayed();
        this.routingKey = StringUtils.isEmpty(routingKey) ? rabbitMonitor.routingKey() : routingKey;
        this.exchangeType = rabbitMonitor.exchangeType();
        this.exchangeName = StringUtils.isEmpty(rabbitMonitor.exchangeName()) ? exchangeType.getName() : rabbitMonitor.exchangeName();
        this.deadQueue = rabbitMonitor.deadQueue();
        this.retry = rabbitMonitor.retry();
        this.id = rabbitMonitor.id();
        this.containerFactory = rabbitMonitor.containerFactory();
        this.exclusive = rabbitMonitor.exclusive();
        this.priority = rabbitMonitor.priority();
        this.admin = rabbitMonitor.admin();
        this.bindings = rabbitMonitor.bindings();
        this.group = rabbitMonitor.group();
        this.returnExceptions = rabbitMonitor.returnExceptions();
        this.errorHandler = rabbitMonitor.errorHandler();
        this.concurrency = rabbitMonitor.concurrency();
        this.autoStartup = rabbitMonitor.autoStartup();
        this.executor = rabbitMonitor.executor();
        this.ackMode = rabbitMonitor.ackMode();
        this.replyPostProcessor = rabbitMonitor.replyPostProcessor();
        this.messageConverter = rabbitMonitor.messageConverter();
        this.replyContentType = rabbitMonitor.replyContentType();
        this.converterWinsContentType = rabbitMonitor.converterWinsContentType();
    }

    /**
     * 构造器
     */
    public BindingVo(RabbitMonitor rabbitMonitor) {
        this.durable = rabbitMonitor.durable();
        this.autoDelete = rabbitMonitor.autoDelete();
        this.isAck = rabbitMonitor.isAck();
        this.isDelayed = rabbitMonitor.isDelayed();
        this.routingKey = rabbitMonitor.routingKey();
        this.exchangeType = rabbitMonitor.exchangeType();
        this.exchangeName = StringUtils.isEmpty(rabbitMonitor.exchangeName()) ? exchangeType.getName() : rabbitMonitor.exchangeName();
        this.deadQueue = rabbitMonitor.deadQueue();
        this.retry = rabbitMonitor.retry();
        this.queues = rabbitMonitor.queues();
        this.id = rabbitMonitor.id();
        this.containerFactory = rabbitMonitor.containerFactory();
        this.exclusive = rabbitMonitor.exclusive();
        this.priority = rabbitMonitor.priority();
        this.admin = rabbitMonitor.admin();
        this.bindings = rabbitMonitor.bindings();
        this.group = rabbitMonitor.group();
        this.returnExceptions = rabbitMonitor.returnExceptions();
        this.errorHandler = rabbitMonitor.errorHandler();
        this.concurrency = rabbitMonitor.concurrency();
        this.autoStartup = rabbitMonitor.autoStartup();
        this.executor = rabbitMonitor.executor();
        this.ackMode = rabbitMonitor.ackMode();
        this.replyPostProcessor = rabbitMonitor.replyPostProcessor();
        this.messageConverter = rabbitMonitor.messageConverter();
        this.replyContentType = rabbitMonitor.replyContentType();
        this.converterWinsContentType = rabbitMonitor.converterWinsContentType();
    }

    /**
     * 是否需要持久化
     */
    private boolean durable;

    /**
     * 是否自动删除
     */
    private boolean autoDelete;

    /**
     * 是否需要手动确认
     */
    private boolean isAck;
    /**
     * 是否延迟
     */
    boolean isDelayed;

    /**
     * 路由键
     */
    private String routingKey;

    /**
     * 交换机类型 默认直连交换机
     */
    private ExchangeTypeEnum exchangeType;

    /**
     * 交换机名称
     */
    private String exchangeName;

    /**
     * 死信队列
     */
    private DeadQueue deadQueue;

    /**
     * 消费失败重试次数
     */
    private int retry;

    //=====================================原生注解属性 照搬过来的不要动===============================

    /**
     * 队列名称
     */
    private String[] queues;


    private String id;


    private String containerFactory;


    private Queue[] queuesToDeclare;


    private boolean exclusive;


    private String priority;


    private String admin;


    private QueueBinding[] bindings;


    private String group;


    private String returnExceptions;


    private String errorHandler;


    private String concurrency;


    private String autoStartup;


    private String executor;


    private String ackMode;


    private String replyPostProcessor;


    private String messageConverter;


    private String replyContentType;


    private String converterWinsContentType;


}
