package com.noServerAssembly.as2.utils;

import com.alibaba.fastjson.JSONObject;
import com.noServerAssembly.as1.Constants;
import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.noServerAssembly.as2.vo.MessageVo;
import com.rabbitmq.client.Channel;
import com.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023-12-20 9:49
 * @PackageName:com.bot.cat.assembly.rabbit231214.utils
 * @ClassName: BaseHandler
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
public class BaseHandler {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory;

    /**
     * ack处理
     */
    protected void doHandlerAck(boolean doSuccess, Message message, Channel channel, RabbitMonitor rabbitMonitor) throws IOException {
        String messageId = message.getMessageProperties().getMessageId();
        //设置监听器为手动确认机制 注解默认为自动ack
        rabbitListenerContainerFactory.createListenerContainer().setAcknowledgeMode(AcknowledgeMode.MANUAL);
        // 手动确认消息
        if (doSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            //业务处理失败 删除当前幂等性标志位
            RedisUtil.del(messageId);
            //
            if (RedisUtil.hasKey(Constants.RETRY_KEY.concat(messageId))) {
                if (Integer.valueOf(RedisUtil.get(Constants.RETRY_KEY.concat(messageId)).toString()) >= rabbitMonitor.retry()) {
                    log.error("消息处理失败 重试此处已达到上线:【{}】", rabbitMonitor.retry());
                    //重试达到上线 放到redis后直接ack
                    RedisUtil.set(Constants.RABBIT_ERROR.concat(messageId), JSONObject.parseObject(new String(message.getBody()), MessageVo.class));
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                    return;
                }
                RedisUtil.incr(Constants.RETRY_KEY.concat(messageId), 1);
            } else {
                RedisUtil.set(Constants.RETRY_KEY.concat(messageId), 1);
            }
            //删除mq幂等性 重新投递消息
            RedisUtil.del(messageId);
            //requeue为true表示让消息重回队列，放入队列尾部，如果为false则会删除当前消息
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        }

    }

    /**
     * 消费着幂等性处理
     */
    protected boolean isActive(String messageId) {
        if (RedisUtil.hasKey(messageId)) {
            return false;
        }
        RedisUtil.set(messageId, messageId, 300, TimeUnit.SECONDS);
        return true;
    }

    /**
     * 校验是否存在定向消费
     */
    protected boolean checkHaveCustom(Message message) {
        return StringUtils.isNotBlank(message.getMessageProperties().getAppId()) &&
                !message.getMessageProperties().getAppId().equals(Constants.ORIENT_CONSUME);
    }

}
