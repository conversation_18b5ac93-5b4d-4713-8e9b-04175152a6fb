package com.noServerAssembly.as2.server.strategy.listen;


import com.alibaba.fastjson.JSONObject;

import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.noServerAssembly.as2.utils.BaseHandler;
import com.noServerAssembly.as2.vo.MessageVo;
import com.rabbitmq.client.Channel;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

/**
 * 项目名称: pay_core
 *
 * @ClassName WalterMessageListener
 * @Description
 * @<NAME_EMAIL>
 * @Date 2022/4/28 9:17
 */
@Slf4j
public abstract class CustomMessageListener<T> extends BaseHandler implements ChannelAwareMessageListener {

    @Setter
    private AcknowledgeMode ack;
    /**
     * 获取到注解参数
     */
    protected RabbitMonitor rabbitMonitor = this.getClass().getAnnotation(RabbitMonitor.class);

    /**
     * 监听到消息
     */
    @SneakyThrows
    @Override
    public void onMessage(Message message, Channel channel) {
        boolean doSuccess = false;
        //获取到消息体
        MessageVo mqMessage = JSONObject.parseObject(new String(message.getBody()), MessageVo.class);
        String consumerQueue = message.getMessageProperties().getConsumerQueue();
        mqMessage.setQueueName(consumerQueue);
        try {
            //判断当前消息是否存在定向消息(是否可消费) 获取消费标识
            if (checkHaveCustom(message)) {
                log.error("不能消费当前消息！跳出");
                return;
            }
            //幂等性设置
            if (isActive(mqMessage.getMessageId())) {
                //业务处理
                doHandleMessage(mqMessage);
                //业务处理成功
                doSuccess = true;
            } else {
                log.error("该消息{}已被消费", mqMessage.getMessageId());
            }
        } catch (Exception e) {
            log.error("队列" + message.getMessageProperties().getConsumerQueue() + "消息接收处理异常：" + e.getMessage());
            if (log.isDebugEnabled()) {
                e.printStackTrace();
            }
        } finally {
            //手动ack
            if (ack.isManual()) {
                doHandlerAck(doSuccess, message, channel, rabbitMonitor);
            }
        }
    }

    /**
     * 处理业务
     */
    public abstract void doHandleMessage(MessageVo mqMessage);

}