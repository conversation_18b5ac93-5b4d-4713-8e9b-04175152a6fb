package com.noServerAssembly.as2.server;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;

import com.exception.AssemblyCatServiceException;
import com.noServerAssembly.as1.annocation.DeadQueue;
import com.noServerAssembly.as1.enums.ExchangeTypeEnum;
import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.noServerAssembly.as2.server.strategy.listen.CustomMessageListener;
import com.noServerAssembly.as2.vo.BindingVo;
import io.micrometer.core.instrument.util.StringUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RMapCache;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.noServerAssembly.as1.enums.ExchangeTypeEnum.DEAD_LETTER;
import static com.noServerAssembly.as2.constants.RabbitConstants.*;

/**
 * <AUTHOR>
 * @Date 2023-12-14 17:54
 * @PackageName:com.bot.cat.assembly.rabbit231214.utils
 * @ClassName: BingQueueServie
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
public final class BindingQueueServer {


    private static Redisson redisson = SpringUtil.getBean(Redisson.class);

    private static RabbitAdmin rabbitAdmin = SpringUtil.getBean(RabbitAdmin.class);

    private static ConnectionFactory connectionFactory = SpringUtil.getBean(ConnectionFactory.class);

    /**
     * 进行关系绑定 -- 最简单的绑定关系 交换机为直连交换机
     */
    public static void doBinding(String queueName, String routingKey, CustomMessageListener messageListener) {
        if (!messageListener.getClass().isAnnotationPresent(RabbitMonitor.class)) {
            log.info("监听类请添加注解[com.bot.cat.assembly.rabbit231214.annotation.RabbitMonitor]才允许动态绑定");
            throw new AssemblyCatServiceException("监听类请添加注解[com.bot.cat.assembly.rabbit231214.annotation.RabbitMonitor]才允许动态绑定");
        }
        doBinding(new BindingVo(queueName, routingKey, messageListener));
        //后进行监听器队列绑定
        addMessageLister(queueName, messageListener);
    }

    /**
     * 进行关系绑定 -- 最简单的绑定关系 交换机为直连交换机
     */
    public static void doBinding(String queueName, String routingKey) {
        doBinding(new BindingVo(queueName, routingKey));
    }

    /**
     * 进行关系绑定
     */
    public static void doBinding(RabbitMonitor rabbitMonitor) {
        doBinding(new BindingVo(rabbitMonitor));
    }


    /**
     * 进行关系绑定
     */
    public static void doBinding(BindingVo bindingVo) {
        Exchange exchange = createExchange(bindingVo);
        String[] queues = bindingVo.getQueues();
        if (queues.length == 0) {
            declareQueue(bindingVo, exchange, DEFAULT_QUEUE);
            return;
        }
        Arrays.stream(queues).forEach(queueName -> declareQueue(bindingVo, exchange, queueName));
    }


    /**
     * 关系绑定
     */
    public static void declareQueue(BindingVo bindingVo, Exchange exchange, String queueName) {
        log.info("进行队列关系绑定--BindingVo:【{}】,exchange:【{}】,queueName:【{}】", bindingVo, exchange, queueName);
        //查询是否存在死信队列
        Map<String, Object> arguments = new HashMap<>();
        DeadQueue deadQueue = bindingVo.getDeadQueue();
        if (ObjectUtil.isNotNull(deadQueue) && StringUtils.isNotEmpty(deadQueue.queueName())) {
            //存在死信队列 创建死信队列
            //使用 x-dead-letter-exchange 参数指定死信交换机的名称
            arguments.put(X_DEAD_LETTER_EXCHANGE, DEAD_LETTER.getName());
            //时间 超过时间主路由器无法处理到死信交换机处理
            arguments.put(X_MESSAGE_TTL, deadQueue.ttl());
            //使用 x-dead-letter-routing-key 参数指定死信消息被发送到死信交换机的路由键
            arguments.put(X_DEAD_LETTER_ROUTING_KEY, deadQueue.routingKey());
            //声明并绑定死信队列
            Queue deadLetterQueue = new Queue(deadQueue.queueName());
            deadLetterQueue.setIgnoreDeclarationExceptions(true);
            //创建死信交换机
            Exchange deadExchange = createExchange(DEAD_LETTER.getName());
            if (!checkExchangeExists(DEAD_LETTER.getName())) {
                rabbitAdmin.declareExchange(deadExchange);
            }
            rabbitAdmin.declareQueue(deadLetterQueue);
            rabbitAdmin.declareBinding(BindingBuilder.bind(deadLetterQueue)
                    .to(deadExchange)
                    .with(deadQueue.routingKey())
                    .noargs());
        }
        //创建正常的交换机业务
        Queue queue = QueueBuilder.durable(queueName).withArguments(arguments).build();
        if (!checkExchangeExists(exchange.getName())) {
            rabbitAdmin.declareExchange(exchange);
        }
        rabbitAdmin.declareQueue(queue);
        rabbitAdmin.declareBinding(BindingBuilder
                .bind(queue)
                .to(exchange)
                .with(bindingVo.getRoutingKey())
                .noargs());
    }

    /**
     * 创建交换机
     */
    private static Exchange createExchange(String exchangeName) {
        BindingVo bindingVo = new BindingVo();
        bindingVo.setExchangeName(exchangeName);
        return createExchange(bindingVo);
    }


    /**
     * 创建交换机
     */
    private static Exchange createExchange(BindingVo bindingVo) {
        log.info("开始创建交换机--bindingVo:【{}】", bindingVo);
        Exchange exchange;
        //查看交换机是否已经创建
        if (bindingVo.isDelayed()) {
            Map<String, Object> args = new HashMap<>();
            //TODO 设置直连默认
            args.put(X_DELAYED_TYPE, "direct");
            //延迟队列
            exchange = new CustomExchange(bindingVo.getExchangeName(), X_DELAYED_MESSAGE, bindingVo.isDurable(), bindingVo.isAutoDelete(), args);
        } else {
            ExchangeTypeEnum exchangeType = bindingVo.getExchangeType();
            switch (exchangeType) {
                case TOPIC:
                    exchange = new TopicExchange(StringUtil.isNotEmpty(bindingVo.getExchangeName()) ? bindingVo.getExchangeName() : exchangeType.getName(), bindingVo.isDurable(), bindingVo.isAutoDelete());
                    break;
                case FANOUT:
                    exchange = new FanoutExchange(StringUtil.isNotEmpty(bindingVo.getExchangeName()) ? bindingVo.getExchangeName() : exchangeType.getName(), bindingVo.isDurable(), bindingVo.isAutoDelete());
                    break;
                case HEADERS:
                    exchange = new HeadersExchange(StringUtil.isNotEmpty(bindingVo.getExchangeName()) ? bindingVo.getExchangeName() : exchangeType.getName(), bindingVo.isDurable(), bindingVo.isAutoDelete());
                    break;
                //创建死信交换机
                case DEAD_LETTER:
                    exchange = new DirectExchange(DEAD_LETTER.getName(), true, false);
                    break;
                default:
                    exchange = new DirectExchange(StringUtil.isNotEmpty(bindingVo.getExchangeName()) ? bindingVo.getExchangeName() : exchangeType.getName(), bindingVo.isDurable(), bindingVo.isAutoDelete());
            }
        }
        //按断是否
        return exchange;
    }


    /**
     * 判断交换机是否已经创建
     */
    public static boolean checkExchangeExists(String exchangeName) {
        try {
            rabbitAdmin.getRabbitTemplate().execute(channel -> {
                channel.exchangeDeclarePassive(exchangeName);
                return null;
            });
            return true; // 如果没有抛出异常，表示交换机存在
        } catch (Exception e) {
            return false; // 捕获到异常，表示交换机不存在
        }
    }


    /**
     * 在容器中加入消息监听
     *
     * @param queueName
     * @param messageListener
     * @throws Exception
     */
    public static void addMessageLister(String queueName, CustomMessageListener messageListener) {
        RabbitMonitor annotation = messageListener.getClass().getAnnotation(RabbitMonitor.class);
        log.info("开始在容器中加入消息监听--queueName:【{}】,messageListener:【{}】", queueName, messageListener.getClass().getSimpleName());
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addQueueNames(queueName);
        //不确认，存在消息丢失的风险
        AcknowledgeMode ack = annotation.isAck() ? AcknowledgeMode.MANUAL : AcknowledgeMode.NONE;
        messageListener.setAck(ack);
        container.setAcknowledgeMode(ack);
        container.setMessageListener(new MessageListenerAdapter(messageListener));
        container.start();
        //将绑定关系放入缓存
        RMapCache<String, String> rMapCache = redisson.getMapCache("rabbit:listener");
        String qs = rMapCache.get(messageListener.getClass().getSimpleName());
        qs = qs == null ? queueName : qs.concat(",").concat(queueName);
        rMapCache.put(messageListener.getClass().getSimpleName(), qs, 100, TimeUnit.DAYS);
        log.info("已成功监听异步消息触发通知队列:【{}】", queueName);
    }

}
