package com.noServerAssembly.as2.server.strategy.send.impl;


import com.noServerAssembly.as1.utils.ProtostuffUtils;
import com.noServerAssembly.as2.server.strategy.send.SendStrategy;
import com.noServerAssembly.as2.vo.MessageVo;
import com.utils.RedisUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023-12-18 15:44
 * @PackageName:com.bot.cat.assembly.rabbit231214.server.strategy
 * @ClassName: RetrySendStrategy
 * @Description: 重试机制
 * @Version 1.0
 */
@Slf4j
@Service
public class RetrySendStrategy extends SendStrategy {

    private static final String retrySendFailKey = "CAT:RABBIT:SEND_FAIL:";
    private static final String retrySendFailKeyCount = "CAT:RABBIT:COUNT:";


    @Value("${rabbit.retry.count:5}")
    private Integer maxRetryCount;

    @Override
    protected void doConfirmHandler(CorrelationData correlationData, String reason) {
        retrySendMessage(correlationData.getReturnedMessage().getBody(), reason);
    }


    @Override
    protected void doReturnedHandler(ReturnedMessage returned) {
        retrySendMessage(returned.getMessage().getBody(), returned.getReplyText());
    }

    /**
     * 重新发送消息
     */
    private void retrySendMessage(byte[] bytes, String errorMessage) {
        Optional.of(bytes)
                .map(bs -> ProtostuffUtils.deserialize(bs, MessageVo.class))
                .ifPresent(messageVo -> {
                    String key = retrySendFailKeyCount.concat(messageVo.getMessageId());
                    if (!RedisUtil.hasKey(key)) {
                        RedisUtil.set(key, 1, 3600);
                    }
                    Integer count = Integer.valueOf(RedisUtil.get(key).toString());
                    log.info("当前消息【{}】重试第【{}】次", key, count);
                    if (count > maxRetryCount) {
                        log.info("当前消息【{}】重试【{}】次超过限制 进入备用方案", key, count);
                        //超过限制
                        messageVo.setErrorMsg(errorMessage);
                        doCompensate(messageVo);
                        return;
                    }
                    RedisUtil.incr(key, 1);
                    // 重新发送发送对应的消息
                    send(messageVo, createMessagePostProcessor(messageVo, messageVo.getExpireTime(), messageVo.isIfPersistent(), messageVo.getDelayed()));
                });
    }


    /**
     * 超过最大限制限制
     */
    private void doCompensate(MessageVo walterMessage) {
        String key = retrySendFailKey.concat(walterMessage.getMessageId());
        RedisUtil.set(key, FailWalterMessage.builder()
                .retryCount(maxRetryCount)
                .walterMessage(walterMessage)
                .createTime(LocalDateTime.now())
                .build());
    }


    @Data
    @Builder
    static class FailWalterMessage {
        /**
         * 重试次数
         */
        private Integer retryCount;
        /**
         * 消息体
         */
        private MessageVo walterMessage;
        /**
         * 消息体
         */
        private String errorMessage;
        /**
         * 时间
         */
        private LocalDateTime createTime;
    }
}
