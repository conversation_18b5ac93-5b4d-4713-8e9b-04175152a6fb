package com.noServerAssembly.as2.aspect;


import com.alibaba.fastjson.JSONObject;

import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.noServerAssembly.as2.utils.BaseHandler;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-09-22 11:38
 * @PackageName:com.xmd.xiaomuding.project.server.interceptor.aop
 * @ClassName: AuthAspect
 * @Description: TODO
 * @Version 1.0
 */
@Aspect
@Component
@Slf4j
public class RabbitMonitorAspect extends BaseHandler {
    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description //rabbit监听器处理
     * @Date 9:17 2023-12-19
     * @Param [org.aspectj.lang.ProceedingJoinPoint]
     */
    @Around("@annotation(rabbitMonitor)")
    public Object before(ProceedingJoinPoint joinPoint, RabbitMonitor rabbitMonitor) throws Throwable {
        boolean doSuccess = true;
        Channel channel = null;
        Message message = null;
        try {
            for (Object arg : joinPoint.getArgs()) {
                if (arg instanceof Channel) {
                    channel = (Channel) arg;
                } else if (arg instanceof Message) {
                    message = (Message) arg;
                }
            }
            if (Objects.isNull(channel) || Objects.isNull(message)) {
                //非插件发送生成或非mq消息 直接执行原逻辑
                return joinPoint.proceed();
            }
            //开始执行切面逻辑
            log.info("rabbit接受到消息体messageVo:【{}】", JSONObject.parseObject(new String(message.getBody())));
            //判断当前消息是否存在定向消息(是否可消费) 获取消费标识
            if (checkHaveCustom(message)) {
                log.error("不能消费当前消息！跳出");
                return null;
            }
            //幂等性设置
            if (isActive(message.getMessageProperties().getMessageId())) {
                //业务处理
                return joinPoint.proceed();
                //业务处理成功
            }
            log.error("该消息【{}】已被消费", message.getMessageProperties().getMessageId());
            return null;
        } catch (Exception e) {
            doSuccess = false;
            log.error("消息接收处理异常：" + e.getMessage());
            throw e;
        } finally {
            //处理手动ack
            if (rabbitMonitor.isAck()) {
                doHandlerAck(doSuccess, message, channel, rabbitMonitor);
            }
        }
    }
}
