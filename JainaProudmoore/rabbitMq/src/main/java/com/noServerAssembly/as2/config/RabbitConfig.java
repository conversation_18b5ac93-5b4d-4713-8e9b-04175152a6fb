package com.noServerAssembly.as2.config;


import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.noServerAssembly.as1.Constants;
import com.noServerAssembly.as1.enums.ConfirmTypeEnum;
import com.noServerAssembly.as2.annotation.RabbitMonitor;
import com.noServerAssembly.as2.server.BindingQueueServer;
import com.noServerAssembly.as2.server.strategy.listen.CustomMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RMapCache;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-12-14 11:12
 * @PackageName:com.bot.cat.assembly.rabbit231214.config
 * @ClassName: RabbitConfig
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "rabbit", name = "use", havingValue = "true", matchIfMissing = true)
public class RabbitConfig {

    @Value("${spring.application.name}")
    private String springApplicationName;
    /**
     * rabbitMQ服务器的地址
     */
    @Value("${spring.rabbitmq.host}")
    private String host;

    /**
     * rabbitMQ端口
     */
    @Value("${spring.rabbitmq.port}")
    private int port;

    /**
     * rabbitMQ用户名
     */
    @Value("${spring.rabbitmq.username}")
    private String username;
    /**
     * rabbitMQ密码
     */
    @Value("${spring.rabbitmq.password}")
    private String password;
    /**
     * rabbitMQ虚拟机 这里默认 /
     */
    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;
    /**
     * 发送端确认模式，默认是NONE 可变为 CORRELATED
     * none（默认）：关闭发布确认模式。
     * correlated：消息从生产者发送到交换机后触发回调方法。
     * simple：会触发回调方法，相当于单个确认（发一条确认一条）。在发布消息成功后使用rabbitTemplate调用waitForConfirms或waitForConfirmsOrDie方法等待broker节点返回发送结果，根据返回结果来判定下一步的逻辑，要注意的点是 waitForConfirmsOrDie方法如果返回false则会关闭channel，则接下来无法发送消息到broker。
     */
    @Value("${spring.rabbitmq.confirmType:NONE}")
    private String confirmType;

    /**
     * 发送端找不到队列是否回调 默认不回调
     */
    @Value("${spring.rabbitmq.publisher-returns:true}")
    private boolean publisherReturns;

    /**
     * mandatory 找不到路由规则的消息会直接抛弃 默认false (false 会直接丢弃消息)
     * 当mandatory标志位设置为true时，如果exchange根据自身类型和消息routeKey无法找到一个符合条件的queue，那么会调用basic.return方法将消息返回给生产者（Basic.Return + Content-Header + Content-Body）；
     * 当mandatory设置为false时，出现上述情形broker会直接将消息扔掉
     */
    @Value("${spring.rabbitmq.mandatory:false}")
    private boolean mandatory;

    /**
     * 定向消费判断，默认为空
     */
    @Value("${spring.rabbitmq.orient.consume:}")
    private String orientConsume;


    @Bean(name = "connectionFactory")
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setAddresses(this.host);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPort(port);
        //设置virtualHost。
        connectionFactory.setVirtualHost(this.virtualHost);
        if (ConfirmTypeEnum.CORRELATED.getType().equals(confirmType)) {
            connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        }
        if (publisherReturns) {
            connectionFactory.setPublisherReturns(true);
        }
        //setCacheMode：设置缓存模式，共有两种，CHANNEL和CONNECTION模式。
        //1、CONNECTION模式，这个模式下允许创建多个Connection，会缓存一定数量的Connection，每个Connection中同样会缓存一些Channel，
        // 除了可以有多个Connection，其它都跟CHANNEL模式一样。
        //2、CHANNEL模式，程序运行期间ConnectionFactory会维护着一个Connection，
        // 所有的操作都会使用这个Connection，但一个Connection中可以有多个Channel，
        // 操作rabbitmq之前都必须先获取到一个Channel，
        // 否则就会阻塞（可以通过setChannelCheckoutTimeout()设置等待时间），
        // 这些Channel会被缓存（缓存的数量可以通过setChannelCacheSize()设置）；
        connectionFactory.setCacheMode(CachingConnectionFactory.CacheMode.CONNECTION);   //设置CONNECTION模式，可创建多个Connection连接
        //设置每个Connection中缓存Channel的数量，不是最大的。操作rabbitmq之前（send/receive message等）
        // 要先获取到一个Channel.获取Channel时会先从缓存中找闲置的Channel，如果没有则创建新的Channel，
        // 当Channel数量大于缓存数量时，多出来没法放进缓存的会被关闭。
        connectionFactory.setChannelCacheSize(25);
        //单位：毫秒；配合channelCacheSize不仅是缓存数量，而且是最大的数量。
        // 从缓存获取不到可用的Channel时，不会创建新的Channel，会等待这个值设置的毫秒数
        //同时，在CONNECTION模式，这个值也会影响获取Connection的等待时间，
        // 超时获取不到Connection也会抛出AmqpTimeoutException异常。
        connectionFactory.setChannelCheckoutTimeout(600);
        //仅在CONNECTION模式使用，设置Connection的缓存数量。
        connectionFactory.setConnectionCacheSize(3);
        //setConnectionLimit：仅在CONNECTION模式使用，设置Connection的数量上限。
        connectionFactory.setConnectionLimit(10);
        Constants.SPRING_APPLICATION_NAME = this.springApplicationName;
        //设置CONFIRM_TYPE为全局变量
        Constants.CONFIRM_TYPE = ConfirmTypeEnum.valueOf(this.confirmType);
        if (orientConsume != null) {
            Constants.ORIENT_CONSUME = this.orientConsume;
        }
        return connectionFactory;
    }


    @Bean(name = "rabbitTemplate")
    @Scope("prototype")
    public RabbitTemplate rabbitTemplate() {
        RabbitTemplate template = new RabbitTemplate(this.connectionFactory());
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        om.registerModule(new JavaTimeModule());
        Jackson2JsonMessageConverter json = new Jackson2JsonMessageConverter(om);
        template.setMessageConverter(json);
        template.setUsePublisherConnection(true);
        template.setMandatory(mandatory);
        return template;
    }


    /**
     * 注册rabbitAdmin 方便管理
     */
    @Bean
    public RabbitAdmin rabbitAdmin() {
        return new RabbitAdmin(this.connectionFactory());
    }

    /**
     * 实现业务初始化
     */
    @PostConstruct
    public void initRabbitServer() {
        //獲取所有的需要初始化的rabbit绑定关系
        findAllListenerMethod()
                .stream()
                .filter(Objects::nonNull)
                //进行关系绑定
                .forEach(method -> BindingQueueServer.doBinding(method.getAnnotation(RabbitMonitor.class)));
        //将历史动态绑定的数据重新绑定 否则需要手动重新绑定
        RMapCache<String, String> rMapCache = SpringUtil.getBean(Redisson.class).getMapCache("rabbit:listener");
        rMapCache.entrySet().stream().forEach(data -> {
            String key = data.getKey();
            String value = data.getValue();
            String[] qs = value.split(",");
            for (String queue : qs) {
                try {
                    BindingQueueServer.addMessageLister(queue, SpringUtil.getBean(toLowerCaseFirstOne(key), CustomMessageListener.class));
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        });
    }


    @Resource
    private ApplicationContext applicationContext;

    /**
     * 获取所有携带监听器的方法  BaiduMapGeocoding
     */
    public List<Method> findAllListenerMethod() {
        List<Method> methodList = new ArrayList<>();
        BeanDefinitionRegistry beanDefinitionRegistry = ((BeanDefinitionRegistry) applicationContext.getAutowireCapableBeanFactory());
        Arrays.stream(applicationContext.getBeanDefinitionNames())
                .map(definitionName -> beanDefinitionRegistry.getBeanDefinition(definitionName).getBeanClassName())
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .forEach(beanClassName -> {
                    Class<?> aClass;
                    try {
                        aClass = Class.forName(beanClassName);
                    } catch (ClassNotFoundException e) {
                        throw new RuntimeException(e);
                    }
                    methodList.addAll(Arrays.stream(aClass.getMethods())
                            .filter(method -> method.isAnnotationPresent(RabbitMonitor.class)
                                    //过滤掉需要动态配置的监听器 TODO 目前好像只是初始化方法 动态监听器需要写到类上
                                    //&& !method.getAnnotation(RabbitMonitor.class).isTrends()
                            )
                            .collect(Collectors.toList()));
                });
        return methodList;
    }


    //首字母转小写
    public static String toLowerCaseFirstOne(String s) {
        if (Character.isLowerCase(s.charAt(0)))
            return s;
        else
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
    }
}
