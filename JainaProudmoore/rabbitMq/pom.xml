<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rb</groupId>
		<artifactId>JainaProudmoore</artifactId>
		<version>1.0.0.RELEASE</version>
	</parent>

	<groupId>com.rb</groupId>
	<artifactId>rabbitMq</artifactId>
	<packaging>jar</packaging>

	<description>rabbitMq</description>

	<properties>
		<redisson.version>3.17.3</redisson.version>
		<rocketmq.version>2.3.0</rocketmq.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.rb</groupId>
			<artifactId>j<PERSON><PERSON><PERSON><PERSON><PERSON></artifactId>
			<version>1.0.0.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-integration</artifactId>
		</dependency>
		<dependency>
			<groupId>io.protostuff</groupId>
			<artifactId>protostuff-core</artifactId>
			<version>1.6.0</version>
		</dependency>

		<dependency>
			<groupId>io.protostuff</groupId>
			<artifactId>protostuff-runtime</artifactId>
			<version>1.6.0</version>
		</dependency>
	</dependencies>
</project>
