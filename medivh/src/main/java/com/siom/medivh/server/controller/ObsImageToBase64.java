package com.siom.medivh.server.controller;

import com.obs.services.ObsClient;
import com.obs.services.model.ObsObject;
import com.siom.medivh.server.cert.sdk.utils.Base64;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;


/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-05-21 16:00
 **/
public class ObsImageToBase64 {
    public static String downloadAndEncodeImage(String bucketName, String objectKey) throws Exception {
        // 1. 创建 OBS 客户端
        ObsClient obsClient = new ObsClient("KGMV1GGCLWU5PZUYSONH", "NAfxlIBzuMNrQZXuR1m6ec4itolLwHJZ19jcV3gL", "https://obs.ap-southeast-1.myhuaweicloud.com");

        // 2. 下载对象
        ObsObject obsObject = obsClient.getObject(bucketName, objectKey);
        InputStream input = obsObject.getObjectContent();

        // 3. 转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int len;
        while ((len = input.read(buffer)) != -1) {
            baos.write(buffer, 0, len);
        }
        byte[] imageBytes = baos.toByteArray();
        // 4. Base64 编码（标准 Java）
        String encode = Base64.encode(imageBytes);
        // 5. 关闭资源
        input.close();
        obsClient.close();
        return encode;
    }
}
