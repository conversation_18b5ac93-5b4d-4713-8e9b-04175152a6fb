package com.siom.medivh.server.service.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siom.medivh.server.entity.strategy.StrategyExecuteLog;
import com.siom.medivh.server.mapper.strategy.StrategyExecuteLogMapper;
import com.siom.medivh.server.model.dto.strategy.StrategyExecuteLogQueryDTO;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 策略执行日志服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class StrategyExecuteLogService extends ServiceImpl<StrategyExecuteLogMapper, StrategyExecuteLog> {

    /**
     * 分页查询策略执行日志
     */
    public IPage<StrategyExecuteLog> pageQuery(StrategyExecuteLogQueryDTO queryDTO) {
        return this.page(new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize()),
                new LambdaQueryWrapper<StrategyExecuteLog>()
                        .eq(queryDTO.getStrategyId() != null, StrategyExecuteLog::getStrategyId, queryDTO.getStrategyId())
                        .eq(StrUtil.isNotEmpty(queryDTO.getStrategyType()), StrategyExecuteLog::getStrategyType, queryDTO.getStrategyType())
                        .eq(queryDTO.getOfferId() != null, StrategyExecuteLog::getOfferId, queryDTO.getOfferId())
                        .eq(queryDTO.getAdvertiserOfferId() != null, StrategyExecuteLog::getAdvertiserOfferId, queryDTO.getAdvertiserOfferId())
                        .eq(StrUtil.isNotEmpty(queryDTO.getGroupTraceId()), StrategyExecuteLog::getGroupTraceId, queryDTO.getGroupTraceId())
                        .eq(queryDTO.getSuccess() != null, StrategyExecuteLog::getSuccess, queryDTO.getSuccess())
                        .eq(queryDTO.getNotifySent() != null, StrategyExecuteLog::getNotifySent, queryDTO.getNotifySent())
                        .ge(queryDTO.getExecuteTimeStart() != null, StrategyExecuteLog::getExecuteTime, queryDTO.getExecuteTimeStart())
                        .le(queryDTO.getExecuteTimeEnd() != null, StrategyExecuteLog::getExecuteTime, queryDTO.getExecuteTimeEnd())
                        .ge(queryDTO.getCreatedTimeStart() != null, StrategyExecuteLog::getCreatedTime, queryDTO.getCreatedTimeStart())
                        .le(queryDTO.getCreatedTimeEnd() != null, StrategyExecuteLog::getCreatedTime, queryDTO.getCreatedTimeEnd())
                        .orderByDesc(StrategyExecuteLog::getExecuteTime));
    }

    /**
     * 记录执行日志
     */
    public boolean recordLog(Long strategyId, String strategyType, Long offerId, Long advertiserOfferId,
                           String groupTraceId, boolean success, String message,
                           String requestPayload, String responsePayload) {
        return Optional.of(new StrategyExecuteLog())
                .map(log -> {
                    log.setStrategyId(strategyId);
                    log.setStrategyType(strategyType);
                    log.setOfferId(offerId);
                    log.setAdvertiserOfferId(advertiserOfferId);
                    log.setGroupTraceId(groupTraceId);
                    log.setExecuteTime(LocalDateTime.now());
                    log.setSuccess(success);
                    log.setMessage(message);
                    log.setRequestPayload(requestPayload);
                    log.setResponsePayload(responsePayload);
                    log.setNotifySent(false);
                    return this.save(log);
                })
                .orElse(false);
    }

    /**
     * 查询未发送通知的失败日志
     */
    public List<StrategyExecuteLog> getFailedLogsNotNotified() {
        return this.list(new LambdaQueryWrapper<StrategyExecuteLog>()
                .eq(StrategyExecuteLog::getSuccess, false)
                .eq(StrategyExecuteLog::getNotifySent, false));
    }

    /**
     * 统计策略的执行成功率
     */
    public Map<String, Object> getSuccessRateByStrategyId(Long strategyId) {
        return baseMapper.selectSuccessRateByStrategyId(strategyId);
    }

    /**
     * 获取策略最近的执行日志
     */
    public List<StrategyExecuteLog> getLatestByStrategyId(Long strategyId, int limit) {
        return this.list(new LambdaQueryWrapper<StrategyExecuteLog>()
                .eq(StrategyExecuteLog::getStrategyId, strategyId)
                .orderByDesc(StrategyExecuteLog::getExecuteTime)
                .last("LIMIT " + limit));
    }

    /**
     * 标记通知已发送
     */
    public boolean markNotifySent(Long id) {
        return Optional.ofNullable(this.getById(id))
                .map(log -> {
                    log.setNotifySent(true);
                    return this.updateById(log);
                })
                .orElse(false);
    }

    /**
     * 批量标记通知已发送
     */
    public boolean batchMarkNotifySent(List<Long> ids) {
        return ids.stream()
                .map(this::markNotifySent)
                .allMatch(result -> result);
    }

    /**
     * 根据组追踪ID查询日志
     */
    public List<StrategyExecuteLog> getByGroupTraceId(String groupTraceId) {
        return this.list(new LambdaQueryWrapper<StrategyExecuteLog>()
                .eq(StrategyExecuteLog::getGroupTraceId, groupTraceId)
                .orderByDesc(StrategyExecuteLog::getExecuteTime));
    }
}
