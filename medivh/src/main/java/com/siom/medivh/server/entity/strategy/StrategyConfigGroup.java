package com.siom.medivh.server.entity.strategy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 参数配置表实体
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_strategy_config_group")
public class StrategyConfigGroup {

    @TableId("id")
    private Long id;

    /**
     * 是否启用
     */
    @TableField("status")
    private String status;

    /**
     * 说明
     */
    @TableField("description")
    private String description;

    /**
     * 规则 key-value
     */
    @TableField("rule_list")
    private String ruleList;

    /**
     * cron表达式
     */
    @TableField(exist = false)
    private String corn;

    /**
     * 关联的策略任务列表
     */
    @TableField(exist = false)
    private List<StrategyTask> strategyTasks;
}
