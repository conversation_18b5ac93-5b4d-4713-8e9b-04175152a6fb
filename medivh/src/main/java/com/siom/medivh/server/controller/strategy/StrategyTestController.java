package com.siom.medivh.server.controller.strategy;

import com.siom.medivh.server.entity.strategy.StrategyTask;
import com.siom.medivh.server.entity.strategy.TrackerClip;
import com.siom.medivh.server.service.strategy.StrategySchedulerService;
import com.siom.medivh.server.service.strategy.StrategyTaskService;
import com.siom.medivh.server.service.strategy.TrackerClipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 策略测试控制器
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping("/api/strategy/test")
public class StrategyTestController {

    @Autowired
    private StrategyTaskService strategyTaskService;

    @Autowired
    private TrackerClipService trackerClipService;

    @Autowired
    private StrategySchedulerService strategySchedulerService;

    /**
     * 创建测试数据
     */
    @PostMapping("/create-test-data")
    public String createTestData() {
        try {
            // 创建测试策略任务
            StrategyTask task = new StrategyTask();
            task.setStrategyType("APPMETRICA_CLIP");
            task.setStrategyName("测试策略-" + System.currentTimeMillis());
            task.setOfferId(1001L);
            task.setAdvertiserOfferId(2001L);
            task.setCron("0 */5 * * * ?"); // 每5分钟执行一次
            task.setGroupId("test_group_1");
            task.setNotifyUser("https://open.feishu.cn/open-apis/bot/v2/hook/test");
            task.setStatus("ENABLED");
            task.setNextExecuteTime(LocalDateTime.now().plusMinutes(1));
            
            strategyTaskService.save(task);
            
            // 创建测试弹夹
            List<TrackerClip> clips = new ArrayList<>();
            for (int i = 1; i <= 5; i++) {
                TrackerClip clip = new TrackerClip();
                clip.setStrategyId(task.getId());
                clip.setOfferId(task.getOfferId());
                clip.setTrackerUrl("https://test.appmetrica.com/click?id=test" + i);
                clip.setStatus("unused");
                clips.add(clip);
            }
            
            trackerClipService.batchInsert(clips);
            
            return "测试数据创建成功！策略ID: " + task.getId();
            
        } catch (Exception e) {
            return "创建测试数据失败: " + e.getMessage();
        }
    }

    /**
     * 手动触发任务扫描
     */
    @PostMapping("/trigger-scan")
    public String triggerScan() {
        try {
            strategySchedulerService.scanAndExecuteTasks();
            return "任务扫描触发成功！";
        } catch (Exception e) {
            return "任务扫描失败: " + e.getMessage();
        }
    }

    /**
     * 查看系统状态
     */
    @GetMapping("/status")
    public Object getSystemStatus() {
        try {
            long totalTasks = strategyTaskService.count();
            long enabledTasks = strategyTaskService.count(
                    strategyTaskService.lambdaQuery().eq(StrategyTask::getStatus, "ENABLED")
            );
            long totalClips = trackerClipService.count();
            long unusedClips = trackerClipService.count(
                    trackerClipService.lambdaQuery().eq(TrackerClip::getStatus, "unused")
            );

            return String.format("系统状态:\n" +
                    "- 总策略数: %d\n" +
                    "- 启用策略数: %d\n" +
                    "- 总弹夹数: %d\n" +
                    "- 未使用弹夹数: %d\n", totalTasks, enabledTasks, totalClips, unusedClips);
                    
        } catch (Exception e) {
            return "获取系统状态失败: " + e.getMessage();
        }
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/clean-test-data")
    public String cleanTestData() {
        try {
            // 删除测试策略任务
            strategyTaskService.remove(
                    strategyTaskService.lambdaQuery().like(StrategyTask::getStrategyName, "测试策略-")
            );
            
            // 删除测试弹夹
            trackerClipService.remove(
                    trackerClipService.lambdaQuery().like(TrackerClip::getTrackerUrl, "https://test.appmetrica.com")
            );
            
            return "测试数据清理成功！";
            
        } catch (Exception e) {
            return "清理测试数据失败: " + e.getMessage();
        }
    }
}
