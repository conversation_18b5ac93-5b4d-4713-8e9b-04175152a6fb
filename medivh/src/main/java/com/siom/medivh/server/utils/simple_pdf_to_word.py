#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的PDF转Word Demo
使用最简单的方式实现PDF到Word的转换
"""

def simple_pdf_to_word_demo():
    """
    最简单的PDF转Word示例
    """
    print("📄 简单PDF转Word Demo")
    print("=" * 30)
    
    # 方法1: 使用pdf2docx (推荐)
    def method1_pdf2docx():
        try:
            from pdf2docx import Converter
            
            # 输入PDF文件路径
            pdf_file = "D:\\abc.pdf"  # 替换为你的PDF文件路径
            word_file = "D:\\output.docx"  # 输出的Word文件路径
            
            print(f"🔄 转换 {pdf_file} 到 {word_file}")
            
            # 创建转换器并转换
            cv = Converter(pdf_file)
            cv.convert(word_file)
            cv.close()
            
            print("✅ 转换成功!")
            return True
            
        except ImportError:
            print("❌ 需要安装: pip install pdf2docx")
            return False
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return False
    
    # 方法2: 使用PyMuPDF提取文本
    def method2_pymupdf():
        try:
            import fitz  # PyMuPDF
            from docx import Document
            
            pdf_file = "example.pdf"
            word_file = "output_method2.docx"
            
            print(f"🔄 使用PyMuPDF转换 {pdf_file}")
            
            # 打开PDF
            doc = fitz.open(pdf_file)
            
            # 创建Word文档
            word_doc = Document()
            
            # 提取每页文本
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text = page.get_text()
                
                # 添加到Word文档
                word_doc.add_heading(f'第{page_num + 1}页', level=2)
                word_doc.add_paragraph(text)
            
            # 保存Word文档
            word_doc.save(word_file)
            doc.close()
            
            print("✅ 转换成功!")
            return True
            
        except ImportError:
            print("❌ 需要安装: pip install PyMuPDF python-docx")
            return False
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return False
    
    # 交互式选择
    print("选择转换方法:")
    print("1. pdf2docx (推荐，保持格式)")
    print("2. PyMuPDF (提取纯文本)")
    
    choice = input("请选择 (1 或 2): ").strip()
    
    if choice == "1":
        return method1_pdf2docx()
    elif choice == "2":
        return method2_pymupdf()
    else:
        print("❌ 无效选择")
        return False

def install_dependencies():
    """
    安装必要的依赖包
    """
    import subprocess
    import sys
    
    packages = [
        "pdf2docx",
        "PyMuPDF", 
        "python-docx"
    ]
    
    print("📦 安装依赖包...")
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except:
            print(f"❌ {package} 安装失败")

def quick_convert(pdf_path, word_path=None):
    """
    快速转换函数
    """
    try:
        from pdf2docx import Converter
        
        if word_path is None:
            word_path = pdf_path.replace('.pdf', '.docx')
        
        print(f"🔄 转换: {pdf_path} → {word_path}")
        
        cv = Converter(pdf_path)
        cv.convert(word_path)
        cv.close()
        
        print("✅ 转换完成!")
        return True
        
    except ImportError:
        print("❌ 请先运行: pip install pdf2docx")
        return False
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == "--install":
            install_dependencies()
        else:
            pdf_file = sys.argv[1]
            word_file = sys.argv[2] if len(sys.argv) > 2 else None
            quick_convert(pdf_file, word_file)
    else:
        # 交互模式
        print("🚀 PDF转Word工具")
        print("\n选择操作:")
        print("1. 转换PDF文件")
        print("2. 安装依赖包")
        
        choice = input("\n请选择 (1 或 2): ").strip()
        
        if choice == "1":
            pdf_path = input("请输入PDF文件路径: ").strip().strip('"')
            if pdf_path:
                quick_convert(pdf_path)
            else:
                print("❌ 请提供PDF文件路径")
        elif choice == "2":
            install_dependencies()
        else:
            print("❌ 无效选择")
