package com.siom.medivh.server.model.dto.strategy;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 策略任务查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class StrategyTaskQueryDTO {

    /**
     * 策略类型
     */
    private String strategyType;

    /**
     * 策略名称（模糊查询）
     */
    private String strategyName;

    /**
     * 本地 Offer ID
     */
    private Long offerId;

    /**
     * 第三方平台上的 Offer ID
     */
    private Long advertiserOfferId;

    /**
     * 组id
     */
    private String groupId;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间开始
     */
    private LocalDateTime createdTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createdTimeEnd;

    /**
     * 最后执行时间开始
     */
    private LocalDateTime lastExecuteTimeStart;

    /**
     * 最后执行时间结束
     */
    private LocalDateTime lastExecuteTimeEnd;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
