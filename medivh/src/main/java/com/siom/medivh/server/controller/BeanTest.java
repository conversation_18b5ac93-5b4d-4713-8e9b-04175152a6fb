package com.siom.medivh.server.controller;

import org.apache.commons.codec.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import javax.validation.constraints.Min;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * <AUTHOR>
 * @Date 2025-01-15 10:12
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Service
public class BeanTest {

    public static void main(String[] args) {
        LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(3000));
        System.out.println(1);
    }
}
