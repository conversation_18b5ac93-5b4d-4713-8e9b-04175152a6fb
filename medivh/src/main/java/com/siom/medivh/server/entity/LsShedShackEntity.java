/*
 *    Copyright (c) 2018-2025, xiaomuding All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: xiaomuding
 */

package com.siom.medivh.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 圈舍棚
 *
 * <AUTHOR>
 * @date 2023-06-15 19:41:04
 */
@Data
@TableName("ls_shed_shack")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "圈舍棚")
public class LsShedShackEntity extends Model<LsShedShackEntity> {

    private static final long serialVersionUID = 1L;


	/**
	* 圈舍棚ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="圈舍棚ID")
    private Long id;

	/**
	* 棚上级ID(分组ID或养殖场ID)
	*/
    @Schema(description="棚上级ID(分组ID或养殖场ID)")
    private Long shackPid;

	/**
	* 棚名称
	*/
    @Schema(description="棚名称")
    private String shedShackName;

	/**
	* 棚描述
	*/
    @Schema(description="棚描述")
    private String shedShackDes;

	/**
	* 是否为默认栏(0:否;1:是)
	*/
    @Schema(description="是否为默认栏(0:否;1:是)")
    private String shedType;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除 0：未删除 1：已删除
	*/
    @TableLogic
    @Schema(description="是否删除 0：未删除 1：已删除")
    private Integer delFlag;

}