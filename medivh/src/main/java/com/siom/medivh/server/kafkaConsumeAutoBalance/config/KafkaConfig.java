package com.siom.medivh.server.kafkaConsumeAutoBalance.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-18 17:17
 **/
@Configuration
@Data
public class KafkaConfig {

    @Value("${spring.kafka.bootstrap-servers:***************:9092}")
    private String server;

}
