package com.siom.medivh.server.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Scanner;

/**
 * PDF转Word主程序
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
public class PdfToWordMain {

    public static void main(String[] args) {
        System.out.println("📄 PDF转Word工具");
        System.out.println("================");
        
        Scanner scanner = new Scanner(System.in);
        
        try {
            // 获取PDF文件路径
            System.out.print("请输入PDF文件路径: ");
            String pdfPath = scanner.nextLine().trim().replace("\"", "");
            
            // 检查文件是否存在
            File pdfFile = new File(pdfPath);
            if (!pdfFile.exists()) {
                System.out.println("❌ 文件不存在: " + pdfPath);
                return;
            }
            
            if (!pdfPath.toLowerCase().endsWith(".pdf")) {
                System.out.println("❌ 请选择PDF文件");
                return;
            }
            
            // 生成输出文件路径
            String wordPath = pdfPath.replaceAll("\\.pdf$", ".docx");
            System.out.println("📝 输出文件: " + wordPath);
            
            // 开始转换
            System.out.println("🔄 开始转换...");
            boolean success = convertPdfToWord(pdfPath, wordPath);
            
            if (success) {
                System.out.println("✅ 转换成功!");
                System.out.println("📁 输出文件: " + wordPath);
                
                // 显示文件大小
                File wordFile = new File(wordPath);
                if (wordFile.exists()) {
                    System.out.println("📏 文件大小: " + wordFile.length() + " bytes");
                }
            } else {
                System.out.println("❌ 转换失败!");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 程序出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
        
        System.out.println("\n按回车键退出...");
        try {
            System.in.read();
        } catch (IOException e) {
            // 忽略
        }
    }

    /**
     * PDF转Word核心方法
     */
    public static boolean convertPdfToWord(String pdfPath, String wordPath) {
        try {
            // 第一步：从PDF提取文本
            System.out.println("📖 正在提取PDF文本...");
            String text = extractTextFromPdf(pdfPath);
            
            if (text == null || text.trim().isEmpty()) {
                System.out.println("⚠️ PDF中没有找到文本内容");
                return false;
            }
            
            System.out.println("📝 提取到 " + text.length() + " 个字符");
            
            // 第二步：创建Word文档
            System.out.println("📄 正在创建Word文档...");
            createWordDocument(text, wordPath, pdfPath);
            
            return true;
            
        } catch (Exception e) {
            System.out.println("❌ 转换过程中出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从PDF提取文本
     */
    private static String extractTextFromPdf(String pdfPath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(pdfPath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            
            // 设置提取选项
            stripper.setSortByPosition(true);
            
            int totalPages = document.getNumberOfPages();
            System.out.println("📚 PDF总页数: " + totalPages);
            
            StringBuilder allText = new StringBuilder();
            
            // 逐页提取文本
            for (int i = 1; i <= totalPages; i++) {
                stripper.setStartPage(i);
                stripper.setEndPage(i);
                
                String pageText = stripper.getText(document);
                
                if (pageText != null && !pageText.trim().isEmpty()) {
                    allText.append("=== 第 ").append(i).append(" 页 ===\n\n");
                    allText.append(pageText.trim()).append("\n\n");
                }
                
                System.out.print("✓ 第" + i + "页 ");
                if (i % 10 == 0) System.out.println(); // 每10页换行
            }
            
            System.out.println("\n📝 文本提取完成");
            return allText.toString();
        }
    }

    /**
     * 创建Word文档
     */
    private static void createWordDocument(String text, String wordPath, String originalPdfPath) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(wordPath)) {

            // 添加标题
            XWPFParagraph titleParagraph = document.createParagraph();
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("PDF转换文档");
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            titleRun.setFontFamily("微软雅黑");

            // 添加原文件信息
            XWPFParagraph infoParagraph = document.createParagraph();
            XWPFRun infoRun = infoParagraph.createRun();
            infoRun.setText("原文件: " + new File(originalPdfPath).getName());
            infoRun.setFontSize(10);
            infoRun.setColor("666666");
            infoRun.setFontFamily("微软雅黑");

            // 添加空行
            document.createParagraph();

            // 按段落分割文本并添加到Word
            String[] paragraphs = text.split("\n\n");
            
            for (String paragraphText : paragraphs) {
                if (paragraphText.trim().isEmpty()) {
                    continue;
                }
                
                XWPFParagraph paragraph = document.createParagraph();
                XWPFRun run = paragraph.createRun();
                
                // 检查是否是页面标题
                if (paragraphText.startsWith("=== 第") && paragraphText.contains("页 ===")) {
                    run.setText(paragraphText.trim());
                    run.setBold(true);
                    run.setFontSize(14);
                    run.setColor("0066CC");
                } else {
                    run.setText(paragraphText.trim());
                    run.setFontSize(12);
                }
                
                run.setFontFamily("微软雅黑");
            }

            // 保存文档
            document.write(out);
            System.out.println("💾 Word文档保存完成");
        }
    }
}
