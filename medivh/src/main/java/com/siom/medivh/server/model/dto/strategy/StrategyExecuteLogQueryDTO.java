package com.siom.medivh.server.model.dto.strategy;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 策略执行日志查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class StrategyExecuteLogQueryDTO {

    /**
     * 策略主键
     */
    private Long strategyId;

    /**
     * 策略类型
     */
    private String strategyType;

    /**
     * 本地 offer ID
     */
    private Long offerId;

    /**
     * Affise offer ID
     */
    private Long advertiserOfferId;

    /**
     * 同组策略统一执行标识
     */
    private String groupTraceId;

    /**
     * 执行是否成功
     */
    private Boolean success;

    /**
     * 是否已发送通知
     */
    private Boolean notifySent;

    /**
     * 执行时间开始
     */
    private LocalDateTime executeTimeStart;

    /**
     * 执行时间结束
     */
    private LocalDateTime executeTimeEnd;

    /**
     * 创建时间开始
     */
    private LocalDateTime createdTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createdTimeEnd;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
