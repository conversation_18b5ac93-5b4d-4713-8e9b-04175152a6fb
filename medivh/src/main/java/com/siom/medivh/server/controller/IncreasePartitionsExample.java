package com.siom.medivh.server.controller;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-03 18:47
 **/
import org.apache.kafka.clients.admin.*;
import java.util.*;
import java.util.concurrent.ExecutionException;

public class IncreasePartitionsExample {
    public static void main(String[] args) throws ExecutionException, InterruptedException {
        String bootstrapServers = "************:9094,***************:9094,*************:9094";
        String topicName = "DC_RU_CLICK_STREAM";
        int newPartitionCount = 64; // 要扩展到的新分区数量（必须大于当前分区数）

        Properties config = new Properties();
        config.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);

        try (AdminClient adminClient = AdminClient.create(config)) {
            Map<String, NewPartitions> newPartitions = new HashMap<>();
            newPartitions.put(topicName, NewPartitions.increaseTo(newPartitionCount));

            CreatePartitionsResult result = adminClient.createPartitions(newPartitions);
            result.all().get(); // 阻塞直到完成
            System.out.println("Successfully increased partition count for topic: " + topicName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

