package com.siom.medivh.server;

import com.siom.medivh.server.controller.PDFController;
import com.siom.medivh.server.kafkaConsumeAutoBalance.util.ConsumeTaskCreateUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.context.support.XmlWebApplicationContext;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> admin
 * @date : 2023/5/24
 * @description :
 */
@Slf4j
@EnableAsync
@SpringBootApplication
@MapperScan(value = {"com.siom.**.mapper"})
@ComponentScan(
        //基本包
        basePackages = {"com.siom.medivh", "com.aspect", "com.exception", "com.interceptor", "com.websocket"}
)
@EnableAspectJAutoProxy
public class MedivhServerApplication {

    public static List switchList = new ArrayList();
    int count = 0;
    /**
     * test-app 启动
     *
     * @param args
     */
    public static void main(String[] args) {
        XmlWebApplicationContext xmlWebApplicationContext = new XmlWebApplicationContext();
        xmlWebApplicationContext.setConfigLocation("classpath:application.xml");
        log.info("麦迪文 - server 服务启动 ---- start ");
        SpringApplication springApplication = new SpringApplication(MedivhServerApplication.class);
        springApplication.setAllowCircularReferences(Boolean.TRUE);
        springApplication.run(args);
        log.info("麦迪文 - server 服务启动 ---- end ");
        ConsumeTaskCreateUtils.createAndRunTask("TW");
    }


    @PostConstruct
    public void registerOOMHandler() {
        new Thread(() -> {
            doOOM();
        }).start();

        Thread.setDefaultUncaughtExceptionHandler((t, e) -> {
            if (e instanceof OutOfMemoryError) {
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);
                log.error("⚠️ 系统发生 OOM 异常，线程：" + t.getName(), e);

            }
        });
    }

    private void doOOM() {
//        while (true) {
//            switchList.add(new byte[1 * 1024 * 1024]); // 每次申请10MB
//            count++;
//            System.out.println("Allocated " + count * 10 + "MB");
//            try {
//                Thread.sleep(100);
//            } catch (InterruptedException ignored) {}
//        }

    }

}
