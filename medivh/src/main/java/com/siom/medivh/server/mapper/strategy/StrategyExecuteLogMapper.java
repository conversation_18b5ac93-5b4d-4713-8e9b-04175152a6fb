package com.siom.medivh.server.mapper.strategy;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siom.medivh.server.entity.strategy.StrategyExecuteLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 策略执行日志Mapper
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface StrategyExecuteLogMapper extends BaseMapper<StrategyExecuteLog> {

    /**
     * 根据策略ID分页查询执行日志
     */
    @Select("SELECT * FROM t_strategy_execute_log WHERE strategy_id = #{strategyId} ORDER BY execute_time DESC")
    IPage<StrategyExecuteLog> selectByStrategyIdPage(Page<StrategyExecuteLog> page, @Param("strategyId") Long strategyId);

    /**
     * 查询指定时间范围内的执行日志
     */
    @Select("SELECT * FROM t_strategy_execute_log WHERE execute_time BETWEEN #{startTime} AND #{endTime} ORDER BY execute_time DESC")
    List<StrategyExecuteLog> selectByExecuteTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询未发送通知的失败日志
     */
    @Select("SELECT * FROM t_strategy_execute_log WHERE success = false AND notify_sent = false")
    List<StrategyExecuteLog> selectFailedLogsNotNotified();

    /**
     * 统计策略的执行成功率
     */
    @Select("SELECT " +
            "SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as successCount, " +
            "COUNT(*) as totalCount " +
            "FROM t_strategy_execute_log WHERE strategy_id = #{strategyId}")
    Map<String, Object> selectSuccessRateByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 获取策略最近的执行日志
     */
    @Select("SELECT * FROM t_strategy_execute_log WHERE strategy_id = #{strategyId} ORDER BY execute_time DESC LIMIT #{limit}")
    List<StrategyExecuteLog> selectLatestByStrategyId(@Param("strategyId") Long strategyId, @Param("limit") int limit);

    /**
     * 根据组追踪ID查询日志
     */
    @Select("SELECT * FROM t_strategy_execute_log WHERE group_trace_id = #{groupTraceId}")
    List<StrategyExecuteLog> selectByGroupTraceId(@Param("groupTraceId") String groupTraceId);

    /**
     * 根据执行结果查询日志
     */
    @Select("SELECT * FROM t_strategy_execute_log WHERE success = #{success}")
    List<StrategyExecuteLog> selectBySuccess(@Param("success") Boolean success);
}
