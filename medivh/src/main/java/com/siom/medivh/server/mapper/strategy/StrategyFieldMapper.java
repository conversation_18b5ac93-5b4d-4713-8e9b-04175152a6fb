package com.siom.medivh.server.mapper.strategy;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siom.medivh.server.entity.strategy.StrategyField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 策略字段Mapper
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface StrategyFieldMapper extends BaseMapper<StrategyField> {

    /**
     * 根据算法查询字段
     */
    @Select("SELECT * FROM t_strategy_filed WHERE algorithm = #{algorithm}")
    List<StrategyField> selectByAlgorithm(@Param("algorithm") String algorithm);

    /**
     * 根据平台查询字段
     */
    @Select("SELECT * FROM t_strategy_filed WHERE mmp = #{mmp}")
    List<StrategyField> selectByMmp(@Param("mmp") String mmp);

    /**
     * 根据字段名称查询
     */
    @Select("SELECT * FROM t_strategy_filed WHERE filed_nama = #{fieldName}")
    List<StrategyField> selectByFieldName(@Param("fieldName") String fieldName);
}
