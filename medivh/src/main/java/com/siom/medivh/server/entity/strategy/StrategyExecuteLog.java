package com.siom.medivh.server.entity.strategy;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 调度任务执行记录表实体
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_strategy_execute_log")
public class StrategyExecuteLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略主键
     */
    @TableField("strategy_id")
    private Long strategyId;

    /**
     * 策略类型，例如 APPSFLYER_PARAM_ROTATE
     */
    @TableField("strategy_type")
    private String strategyType;

    /**
     * 本地 offer ID
     */
    @TableField("offer_id")
    private Long offerId;

    /**
     * Affise offer ID
     */
    @TableField("advertiser_offer_id")
    private Long advertiserOfferId;

    /**
     * 同组策略统一执行标识
     */
    @TableField("group_trace_id")
    private String groupTraceId;

    /**
     * 实际执行时间
     */
    @TableField("execute_time")
    private LocalDateTime executeTime;

    /**
     * 执行是否成功
     */
    @TableField("success")
    private Boolean success;

    /**
     * 错误信息或状态说明
     */
    @TableField("message")
    private String message;

    /**
     * 替换请求结构体，JSON 格式
     */
    @TableField("request_payload")
    private String requestPayload;

    /**
     * API 响应体，JSON 格式
     */
    @TableField("response_payload")
    private String responsePayload;

    /**
     * 是否已发送通知
     */
    @TableField("notify_sent")
    private Boolean notifySent;

    /**
     * 记录创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}
