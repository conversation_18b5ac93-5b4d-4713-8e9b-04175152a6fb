package com.siom.medivh.server.controller;

import cn.hutool.core.util.PageUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: roy.ren
 * Time: 2024/8/5
 * Modifier:
 * Fix Description:
 * Version: 1.0.0
 */
@Slf4j
@Component
public class DemoJob {

    private static final ConcurrentHashMap<Thread, AtomicInteger> threadBorrowMap = new ConcurrentHashMap<>();

    public static void main(String[] args) throws Exception {
        demo5();
        //demo2();
    }
    public static void demo3() throws Exception {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, "************:9094"); // 改成你的 Kafka 地址
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        //props.put("group.id", "prod-group-us"); // 任意group
        props.put("group.id", "prod-group-pk"); // 任意group

        try (AdminClient adminClient = AdminClient.create(props)) {
            try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {

                // 查询所有 topic（你也可以只写你关心的 topic）
                Set<String> topics = adminClient.listTopics().names().get();
                for (String topic : topics) {
                    // 获取 topic 的所有分区
                    Map<String, TopicDescription> topicDescriptionMap = adminClient.describeTopics(Collections.singletonList(topic)).all().get();
                    List<TopicPartition> partitions = new ArrayList<>();
                    topicDescriptionMap.get(topic).partitions().forEach(p -> {
                        partitions.add(new TopicPartition(topic, p.partition()));
                    });

                    // 获取每个分区的最新位置
                    Map<TopicPartition, Long> endOffsets = consumer.endOffsets(partitions);

                    // 这里假设 consumerGroupId 是固定的
                    String consumerGroupId = "your-consumer-group"; // 这里填你的消费组！！！

                    ListConsumerGroupOffsetsResult offsetsResult = adminClient.listConsumerGroupOffsets(consumerGroupId);
                    Map<TopicPartition, OffsetAndMetadata> consumerOffsets = offsetsResult.partitionsToOffsetAndMetadata().get();

                    long totalLag = 0;
                    for (TopicPartition tp : partitions) {
                        long logEndOffset = endOffsets.getOrDefault(tp, 0L);
                        long consumerOffset = Optional.ofNullable(consumerOffsets.get(tp))
                                .map(OffsetAndMetadata::offset)
                                .orElse(0L);
                        totalLag += (logEndOffset - consumerOffset);
                    }

                    System.out.println(String.format("Topic：%s 总堆积量：%d", topic, totalLag));
                }
            }
        }
    }


    @SneakyThrows
    public static void demo4(){
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, "************:9094"); // 改成你的Kafka地址
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        //props.put("group.id", "monitor-group"); // 任意 group
        props.put("group.id", "prod-group-pk");
        try (AdminClient adminClient = AdminClient.create(props)) {
            try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
                // 1. 获取所有 consumer group
                ListConsumerGroupsResult consumerGroupsResult = adminClient.listConsumerGroups();
                Set<String> groupIds = new HashSet<>();
                consumerGroupsResult.all().get().forEach(group -> groupIds.add(group.groupId()));

                for (String groupId : groupIds) {
                    long totalLag = 0;

                    // 2. 获取 consumer group 的 offset 信息
                    ListConsumerGroupOffsetsResult offsetsResult = adminClient.listConsumerGroupOffsets(groupId);
                    Map<TopicPartition, OffsetAndMetadata> consumerOffsets = offsetsResult.partitionsToOffsetAndMetadata().get();

                    if (consumerOffsets == null || consumerOffsets.isEmpty()) {
                        continue; // 没有offset就跳过
                    }
                    Set<TopicPartition> partitions = consumerOffsets.keySet();
                    // 3. 查询每个分区的 logEndOffset
                    Map<TopicPartition, Long> endOffsets = consumer.endOffsets(partitions);
                    for (TopicPartition tp : partitions) {
                        long logEndOffset = endOffsets.getOrDefault(tp, 0L);
                        long consumerOffset = Optional.ofNullable(consumerOffsets.get(tp))
                                .map(OffsetAndMetadata::offset)
                                .orElse(0L);
                        totalLag += (logEndOffset - consumerOffset);
                    }
                    if(totalLag>0){
                        System.out.println(String.format("ConsumerGroup：%s 总堆积量：%d", groupId, totalLag));
                    }
                }
            }
        }
    }


    private static void demo5() throws Exception {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, "************:9094"); // 改成你的Kafka地址
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        //props.put("group.id", "monitor-group"); // 任意group
        props.put("group.id", "c_conversion_postback"); // 任意group
        try (AdminClient adminClient = AdminClient.create(props)) {
            try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
                // 1. 获取所有 consumer group
                ListConsumerGroupsResult consumerGroupsResult = adminClient.listConsumerGroups();
                Set<String> groupIds = new HashSet<>();
                Collection<ConsumerGroupListing> groupListings = consumerGroupsResult.all().get();
                if(!props.get("group.id").equals("monitor-group")){
                    groupListings =groupListings.stream().filter(group -> group.groupId().equals(props.get("group.id"))).collect(Collectors.toList());
                }
                groupListings .forEach(group -> groupIds.add(group.groupId()));
                // 2. 遍历每个 consumer group
                for (String groupId : groupIds) {
                    long totalLag = 0;
                    // 获取该 consumer group 所有订阅的分区的 offset 信息
                    ListConsumerGroupOffsetsResult offsetsResult = adminClient.listConsumerGroupOffsets(groupId);
                    Map<TopicPartition, OffsetAndMetadata> consumerOffsets = offsetsResult.partitionsToOffsetAndMetadata().get();
                    if (consumerOffsets == null || consumerOffsets.isEmpty()) {
                        continue; // 没有offset就跳过
                    }
                    Set<TopicPartition> partitions = consumerOffsets.keySet();
                    // 3. 查询每个分区的 logEndOffset
                    Map<TopicPartition, Long> endOffsets = consumer.endOffsets(partitions);
                    Map<String, Long> topicLagMap = new HashMap<>();
                    // 4. 计算每个分区的 lag，并按 topic 分类统计
                    for (TopicPartition tp : partitions) {
                        long logEndOffset = endOffsets.getOrDefault(tp, 0L);
                        long consumerOffset = Optional.ofNullable(consumerOffsets.get(tp))
                                .map(OffsetAndMetadata::offset)
                                .orElse(0L);
                        long lag = logEndOffset - consumerOffset;

                        // 将 lag 加入到对应 topic 的总堆积量中
                        topicLagMap.merge(tp.topic(), lag, Long::sum);
                    }
                    Long l = topicLagMap.values().stream().reduce(Long::sum).orElse(0L);
                    if(l>0){
                        // 5. 打印该 consumer group 和它订阅的每个 topic 的堆积情况
                        System.out.println(String.format("ConsumerGroup：%s", groupId));
                        topicLagMap.forEach((topic, lag) -> {
                            if(lag>0){
                                System.out.println(String.format("  Topic：%s, 堆积量：%d", topic, lag));
                            }
                        });
                    }
                }
            }
        }
    }


}
