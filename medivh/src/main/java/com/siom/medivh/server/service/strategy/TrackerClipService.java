package com.siom.medivh.server.service.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siom.medivh.server.entity.strategy.TrackerClip;
import com.siom.medivh.server.mapper.strategy.TrackerClipMapper;
import com.siom.medivh.server.model.dto.strategy.TrackerClipQueryDTO;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 追踪链接弹夹服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class TrackerClipService extends ServiceImpl<TrackerClipMapper, TrackerClip> {

    /**
     * 分页查询追踪链接弹夹
     */
    public IPage<TrackerClip> pageQuery(TrackerClipQueryDTO queryDTO) {
        return this.page(new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize()),
                new LambdaQueryWrapper<TrackerClip>()
                        .eq(queryDTO.getStrategyId() != null, TrackerClip::getStrategyId, queryDTO.getStrategyId())
                        .eq(queryDTO.getOfferId() != null, TrackerClip::getOfferId, queryDTO.getOfferId())
                        .like(StrUtil.isNotEmpty(queryDTO.getTrackerUrl()), TrackerClip::getTrackerUrl, queryDTO.getTrackerUrl())
                        .eq(StrUtil.isNotEmpty(queryDTO.getStatus()), TrackerClip::getStatus, queryDTO.getStatus())
                        .ge(queryDTO.getCreatedTimeStart() != null, TrackerClip::getCreatedTime, queryDTO.getCreatedTimeStart())
                        .le(queryDTO.getCreatedTimeEnd() != null, TrackerClip::getCreatedTime, queryDTO.getCreatedTimeEnd())
                        .ge(queryDTO.getUsedTimeStart() != null, TrackerClip::getUsedTime, queryDTO.getUsedTimeStart())
                        .le(queryDTO.getUsedTimeEnd() != null, TrackerClip::getUsedTime, queryDTO.getUsedTimeEnd())
                        .orderByDesc(TrackerClip::getCreatedTime));
    }

    /**
     * 获取策略的第一个未使用的弹夹
     */
    public TrackerClip getFirstUnusedByStrategyId(Long strategyId) {
        return baseMapper.selectFirstUnusedByStrategyId(strategyId);
    }

    /**
     * 标记弹夹为已使用
     */
    public boolean markAsUsed(Long id) {
        return Optional.ofNullable(this.getById(id))
                .map(clip -> {
                    clip.setStatus("used");
                    clip.setUsedTime(LocalDateTime.now());
                    return this.updateById(clip);
                })
                .orElse(false);
    }

    /**
     * 统计策略的未使用弹夹数量
     */
    public long countUnusedByStrategyId(Long strategyId) {
        return this.count(new LambdaQueryWrapper<TrackerClip>()
                .eq(TrackerClip::getStrategyId, strategyId)
                .eq(TrackerClip::getStatus, "unused"));
    }

    /**
     * 根据策略ID查询弹夹
     */
    public List<TrackerClip> getByStrategyId(Long strategyId) {
        return this.list(new LambdaQueryWrapper<TrackerClip>()
                .eq(TrackerClip::getStrategyId, strategyId));
    }

    /**
     * 批量添加弹夹
     */
    public boolean batchInsert(List<TrackerClip> clips) {
        clips.forEach(clip -> {
            if (StrUtil.isEmpty(clip.getStatus())) {
                clip.setStatus("unused");
            }
        });
        return this.saveBatch(clips);
    }

    /**
     * 消费弹夹（获取并标记为已使用）
     */
    public Optional<TrackerClip> consumeClip(Long strategyId) {
        return Optional.ofNullable(this.getFirstUnusedByStrategyId(strategyId))
                .filter(clip -> this.markAsUsed(clip.getId()))
                .map(clip -> {
                    clip.setStatus("used");
                    clip.setUsedTime(LocalDateTime.now());
                    return clip;
                });
    }
}
