package com.siom.medivh.server.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @Date 2025-01-13 10:01
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Controller()
@RequestMapping("/rd")
public class RedirectController {


    @GetMapping("/go-to-baidu")
    public String redirectToBaidu() {
        return "redirect:https://www.baidu.com?name=zhangsan&age=18s&wd=zs";
    }
    @GetMapping("/go-to-baidu2")
    public String redirectToBaidu2() {
        return "ceshi_yemian";
    }

}
