package com.siom.medivh.server.service.ws;

import com.websocket.annocation.WsPort;
import com.websocket.service.SimonWebSocketService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025-01-13 15:22
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Service
@WsPort("wb2")
public class Test2Service extends SimonWebSocketService<String> {
    @Override
    protected Object doHandlerMessage(String s) {
        System.out.println("TestService===================8988");
        return "Test2Service8988";
    }
}
