package com.siom.medivh.server.controller;

import com.annotation.SysPrintlnLog;
import com.annotation.pattern.PatternForEnum;
import com.core.util.Result;
import com.enums.FactoryObjectEnum;
import com.siom.medivh.server.entity.BsLivestockBreedEntity;
import com.siom.medivh.server.mapper.BsLivestockBreedMapper;
import com.siom.medivh.server.model.ExampleModel;
import com.siom.medivh.server.model.User;
import com.utils.CatHttpUtils;
import org.apache.ibatis.reflection.Reflector;
import org.springframework.context.annotation.Bean;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-03-19 16:09
 * @PackageName:com.siom.deathwing.server.controller
 * @ClassName: DemoController
 * @Description: TODO
 * @Version 1.0
 */
@RestController
@RequestMapping("/demos")

public class DemoController {

    @SysPrintlnLog
    public Result test(@RequestBody @Valid ExampleModel obj) {
        return Result.ok("susccess");
    }


    @GetMapping("/test2")
    @SysPrintlnLog
    public Result test2(@RequestBody @Valid ExampleModel obj) {
        //String a = "1", b = "c";
        return Result.ok("susccess");
    }


    @GetMapping("/test3")
    @SysPrintlnLog
    public Result test3(@RequestParam(value = "name") @Valid @PatternForEnum(enumClass = FactoryObjectEnum.class, equalsValue = "status") String name) {

        return Result.ok("susccess");
    }

    @GetMapping("/test4")
    public Result test4() {
        User user = new User();
        user.setUsername("abd");
        user.setTypeCode(1);
        //sensitiveEnumValueProcessor.process(user); // 确保处理器被调用
        return Result.ok(user);
    }

    @Resource
    private BsLivestockBreedMapper bsLivestockBreedMapper;

    @GetMapping("/test5")
    public Result test5() {
        BsLivestockBreedEntity bsLivestockBreedEntity = bsLivestockBreedMapper.selectById(1855858904626413569L);
        return Result.ok(bsLivestockBreedEntity);
    }

    @GetMapping("/test6")
    public Result test6() {

        String url = "https://portal.ubianet.com/api/user/get_devices_dynamic_info";
        Map<String, Object> params = new HashMap<>();
        String[] uids = {"P4LTQYDR6ZJI2URLGA7Q"};
        params.put("uids", uids);
        params.put("want_app_config", true);
        params.put("want_battery_and_signal", false);
        params.put("want_flow_info", true);
        params.put("want_ticket_new_reply", true);
        params.put("token", "gzgzvufszphwfUayxyDAXwXozMeA4bdFRf");
        return Result.ok( CatHttpUtils.doPostExecute(url, params, Object.class));

    }

    public static void main(String[] args) {
        // 当前日期
        LocalDate currentDate = LocalDate.now();

        // 减去指定天数，例如减去 10 天
        int daysToSubtract = 10;
        LocalDate resultDate = LocalDate.now().minusMonths(daysToSubtract);
        // 转换为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = resultDate.format(formatter);
        // 输出结果
        System.out.println("减去 " + daysToSubtract + " 天后的日期是: " + formattedDate);
    }
}