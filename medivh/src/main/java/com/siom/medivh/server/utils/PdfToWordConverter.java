package com.siom.medivh.server.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PDF转Word工具类
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
public class PdfToWordConverter {

    /**
     * 将PDF文件转换为Word文档
     * 
     * @param pdfPath PDF文件路径
     * @param wordPath Word文件输出路径（可选，为null时自动生成）
     * @return 转换是否成功
     */
    public static boolean convertPdfToWord(String pdfPath, String wordPath) {
        try {
            // 验证输入文件
            Path pdfFile = Paths.get(pdfPath);
            if (!Files.exists(pdfFile)) {
                System.err.println("❌ PDF文件不存在: " + pdfPath);
                return false;
            }

            // 生成输出文件路径
            if (wordPath == null || wordPath.trim().isEmpty()) {
                wordPath = pdfPath.replaceAll("\\.pdf$", ".docx");
            }

            System.out.println("🔄 开始转换: " + pdfPath + " → " + wordPath);

            // 使用PDFBox提取PDF文本
            String extractedText = extractTextFromPdf(pdfPath);
            
            if (extractedText == null || extractedText.trim().isEmpty()) {
                System.err.println("❌ 无法从PDF中提取文本内容");
                return false;
            }

            // 创建Word文档
            boolean success = createWordDocument(extractedText, wordPath, pdfPath);
            
            if (success) {
                System.out.println("✅ 转换成功: " + wordPath);
                System.out.println("📄 文件大小: " + Files.size(Paths.get(wordPath)) + " bytes");
            }
            
            return success;

        } catch (Exception e) {
            System.err.println("❌ 转换失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 从PDF文件中提取文本
     */
    private static String extractTextFromPdf(String pdfPath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(pdfPath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            
            // 设置提取选项
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");
            
            StringBuilder allText = new StringBuilder();
            int totalPages = document.getNumberOfPages();
            
            System.out.println("📖 PDF总页数: " + totalPages);
            
            // 逐页提取文本
            for (int i = 1; i <= totalPages; i++) {
                stripper.setStartPage(i);
                stripper.setEndPage(i);
                
                String pageText = stripper.getText(document);
                
                if (pageText != null && !pageText.trim().isEmpty()) {
                    allText.append("=== 第 ").append(i).append(" 页 ===\n\n");
                    allText.append(pageText.trim()).append("\n\n");
                } else {
                    allText.append("=== 第 ").append(i).append(" 页 ===\n\n");
                    allText.append("(此页无文本内容)\n\n");
                }
                
                System.out.println("✓ 已处理第 " + i + " 页");
            }
            
            return allText.toString();
        }
    }

    /**
     * 创建Word文档
     */
    private static boolean createWordDocument(String text, String wordPath, String originalPdfPath) {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(wordPath)) {

            // 添加标题
            XWPFParagraph titleParagraph = document.createParagraph();
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("PDF转换文档");
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            titleRun.setFontFamily("微软雅黑");

            // 添加原文件信息
            XWPFParagraph infoParagraph = document.createParagraph();
            XWPFRun infoRun = infoParagraph.createRun();
            infoRun.setText("原文件: " + Paths.get(originalPdfPath).getFileName().toString());
            infoRun.setFontSize(10);
            infoRun.setColor("666666");
            infoRun.setFontFamily("微软雅黑");

            // 添加分隔线
            XWPFParagraph separatorParagraph = document.createParagraph();
            XWPFRun separatorRun = separatorParagraph.createRun();
            separatorRun.setText("─".repeat(50));
            separatorRun.setColor("CCCCCC");

            // 按段落分割文本
            String[] paragraphs = text.split("\n\n");
            
            for (String paragraphText : paragraphs) {
                if (paragraphText.trim().isEmpty()) {
                    continue;
                }
                
                XWPFParagraph paragraph = document.createParagraph();
                XWPFRun run = paragraph.createRun();
                
                // 检查是否是页面标题
                if (paragraphText.startsWith("=== 第") && paragraphText.contains("页 ===")) {
                    run.setText(paragraphText.trim());
                    run.setBold(true);
                    run.setFontSize(14);
                    run.setColor("0066CC");
                } else {
                    run.setText(paragraphText.trim());
                    run.setFontSize(12);
                }
                
                run.setFontFamily("微软雅黑");
            }

            // 保存文档
            document.write(out);
            return true;

        } catch (IOException e) {
            System.err.println("❌ 创建Word文档失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 批量转换PDF文件
     */
    public static void convertBatch(String inputFolder, String outputFolder) {
        try {
            Path inputPath = Paths.get(inputFolder);
            Path outputPath = Paths.get(outputFolder != null ? outputFolder : inputFolder + "/converted");
            
            if (!Files.exists(inputPath)) {
                System.err.println("❌ 输入文件夹不存在: " + inputFolder);
                return;
            }
            
            // 创建输出文件夹
            Files.createDirectories(outputPath);
            
            // 查找所有PDF文件
            List<Path> pdfFiles = Files.walk(inputPath)
                    .filter(path -> path.toString().toLowerCase().endsWith(".pdf"))
                    .collect(Collectors.toList());
            
            if (pdfFiles.isEmpty()) {
                System.out.println("❌ 在文件夹中没有找到PDF文件: " + inputFolder);
                return;
            }
            
            System.out.println("📁 找到 " + pdfFiles.size() + " 个PDF文件");
            
            int successCount = 0;
            for (Path pdfFile : pdfFiles) {
                String outputFile = outputPath.resolve(
                    pdfFile.getFileName().toString().replaceAll("\\.pdf$", ".docx")
                ).toString();
                
                System.out.println("\n🔄 转换: " + pdfFile.getFileName());
                
                if (convertPdfToWord(pdfFile.toString(), outputFile)) {
                    successCount++;
                } else {
                    System.err.println("❌ 转换失败: " + pdfFile.getFileName());
                }
            }
            
            System.out.println("\n📊 批量转换完成: " + successCount + "/" + pdfFiles.size() + " 成功");
            
        } catch (IOException e) {
            System.err.println("❌ 批量转换失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     */
    public static void getFileInfo(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                System.err.println("❌ 文件不存在: " + filePath);
                return;
            }
            
            System.out.println("📄 文件信息:");
            System.out.println("  文件名: " + path.getFileName());
            System.out.println("  文件大小: " + Files.size(path) + " bytes");
            System.out.println("  文件路径: " + path.toAbsolutePath());
            
            if (filePath.toLowerCase().endsWith(".pdf")) {
                try (PDDocument document = PDDocument.load(path.toFile())) {
                    System.out.println("  PDF页数: " + document.getNumberOfPages());
                }
            }
            
        } catch (IOException e) {
            System.err.println("❌ 获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        System.out.println("📄 PDF转Word工具 (Java版)");
        //System.out.println("=" * 40);
        
        if (args.length == 0) {
            // 交互式模式
            System.out.println("使用方法:");
            System.out.println("1. 单文件转换: java PdfToWordConverter <pdf_file> [word_file]");
            System.out.println("2. 批量转换: java PdfToWordConverter --batch <input_folder> [output_folder]");
            System.out.println("3. 文件信息: java PdfToWordConverter --info <file_path>");
            
            // 示例转换
            String testPdf = "D:\\abc.pdf";  // 你选中的文件路径
            if (Files.exists(Paths.get(testPdf))) {
                System.out.println("\n🔍 发现测试文件: " + testPdf);
                System.out.println("开始转换...");
                convertPdfToWord(testPdf, null);
            } else {
                System.out.println("\n💡 请提供PDF文件路径进行转换");
            }
            
        } else if (args[0].equals("--batch")) {
            // 批量转换模式
            if (args.length < 2) {
                System.err.println("❌ 请提供输入文件夹路径");
                return;
            }
            String outputFolder = args.length > 2 ? args[2] : null;
            convertBatch(args[1], outputFolder);
            
        } else if (args[0].equals("--info")) {
            // 文件信息模式
            if (args.length < 2) {
                System.err.println("❌ 请提供文件路径");
                return;
            }
            getFileInfo(args[1]);
            
        } else {
            // 单文件转换模式
            String pdfPath = args[0];
            String wordPath = args.length > 1 ? args[1] : null;
            convertPdfToWord(pdfPath, wordPath);
        }
    }
}
