package com.siom.medivh.server.service.ws;

import com.websocket.annocation.WsPort;
import com.websocket.service.SimonWebSocketService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025-01-13 15:22
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Service
@WsPort("wb1")
public class Test1Service extends SimonWebSocketService<String> {
    @Override
    protected Object doHandlerMessage(String s) {
        System.out.println("Test1Service===================8989");
        return "Test1Service8989";
    }
}
