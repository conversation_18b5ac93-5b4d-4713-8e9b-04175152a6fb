package com.siom.medivh.server.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import com.bo.Result;
import com.siom.medivh.server.entity.LsShedGroupEntity;
import com.siom.medivh.server.entity.LsShedHurdleEntity;
import com.siom.medivh.server.entity.LsShedShackEntity;
import com.siom.medivh.server.mapper.LsShedGroupMapper;
import com.siom.medivh.server.mapper.LsShedHurdleMapper;
import com.siom.medivh.server.mapper.LsShedShackMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/api/excel")
public class ExcelController {

    @Resource
    LsShedGroupMapper lsShedGroupMapper;
    @Resource
    LsShedShackMapper lsShedShackMapper;
    @Resource
    LsShedHurdleMapper lsShedHurdleMapper;


    @PostMapping("/upload")
    public Result uploadExcel(@RequestParam("file") MultipartFile file) throws IOException {
        Set<String> penlanhaoData = new LinkedHashSet<>();
        // 存储区棚信息
        Map<Integer, Integer> gs = new HashMap<>();
        // 存储区棚和；栏信息
        Map<String, Integer> gs_h = new HashMap<>();
        // 解析 Excel 文件
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            // 遍历所有 sheet 页
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                if (sheet == null) continue;
                // 获取表头所在行
                Row headerRow = sheet.getRow(0);
                if (headerRow == null) continue;
                // 找到 "棚栏号" 的列索引
                int penlanhaoColumnIndex = -1;
                for (Cell cell : headerRow) {
                    if ("棚栏号".equals(cell.getStringCellValue())) {
                        penlanhaoColumnIndex = cell.getColumnIndex();
                        break;
                    }
                }
                // 如果找不到“棚栏号”列，跳过该 sheet
                if (penlanhaoColumnIndex == -1) continue;
                // 遍历该列的所有数据，从第 1 行开始
                for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                    Row row = sheet.getRow(rowIndex);
                    if (row != null) {
                        Cell cell = row.getCell(penlanhaoColumnIndex);
                        if (cell != null) {
                            String replaced = cell.toString().replace("中", "");
                            if (StringUtils.isNotBlank(replaced)) {
                                penlanhaoData.add(replaced);
                            }
                        }
                    }
                }
            }
        }
        penlanhaoData.stream().forEach(x -> {
            String[] split = x.split("-");
            Integer group = Integer.valueOf(split[0]);
            Integer shack = Integer.valueOf(split[1]);
            Integer hurdle = Integer.valueOf(split[2]);
            //如果区相同 存储最大的棚信息
            if (gs.containsKey(group)) {
                if (gs.get(group) < shack) {
                    gs.put(group, shack);
                }
            } else {
                gs.put(group, shack);
            }
            String gs_hKey = group + "-" + shack;
            if (gs_h.containsKey(gs_hKey)) {
                if (gs_h.get(gs_hKey) < hurdle) {
                    gs_h.put(gs_hKey, hurdle);
                }
            } else {
                gs_h.put(gs_hKey, hurdle);
            }
        });
        log.info("gs:【{}】", gs);
        log.info("gs_h:【{}】", gs_h);
        //创建区信息
        for (int i = 1; i < 4; i++) {
            LsShedGroupEntity group = lsShedGroupMapper.selectOne(new LambdaQueryWrapper<LsShedGroupEntity>()
                    .eq(LsShedGroupEntity::getGroupPid, 1711547552064667649L)  //1,智慧养殖 2,智慧畜牧 3,专家服务台
                    .eq(LsShedGroupEntity::getShedGroupName, i + "区")
                    .eq(LsShedGroupEntity::getDelFlag, 0));
            if (Objects.isNull(group)) {
                group = new LsShedGroupEntity();
                group.setId(IdWorker.getId());
                group.setShedGroupName(i + "区");
                group.setShedGroupDes("create by system");
                group.setGroupPid(1711547552064667649L);
                group.setShedType("0");
                group.setDelFlag(0);
                group.setCreateTime(LocalDateTime.now());
                lsShedGroupMapper.insert(group);
            }
            //创建区下面的棚信息
            if (gs.containsKey(i)) {
                for (int j = 1; j < gs.get(i) + 1; j++) {
                    LsShedShackEntity shack = lsShedShackMapper.selectOne(new LambdaQueryWrapper<LsShedShackEntity>()
                            .eq(LsShedShackEntity::getShackPid, group.getId())  //1,智慧养殖 2,智慧畜牧 3,专家服务台
                            .eq(LsShedShackEntity::getShedShackName, j + "棚")
                            .eq(LsShedShackEntity::getDelFlag, 0));
                    if (Objects.isNull(shack)) {
                        shack = new LsShedShackEntity();
                        shack.setId(IdWorker.getId());
                        shack.setShedShackName(j + "棚");
                        shack.setShedShackDes("create by system");
                        shack.setShackPid(group.getId());
                        shack.setShedType("0");
                        shack.setDelFlag(0);
                        shack.setCreateTime(LocalDateTime.now());
                        lsShedShackMapper.insert(shack);
                    }
                    //创建栏信息
                    String gs_hKey = i + "-" + j;
                    if (gs_h.containsKey(gs_hKey)) {
                        for (int k = 1; k < gs_h.get(gs_hKey) + 1; k++) {
                            LsShedHurdleEntity hurdle = lsShedHurdleMapper.selectOne(new LambdaQueryWrapper<LsShedHurdleEntity>()
                                    .eq(LsShedHurdleEntity::getHurdlePid, shack.getId())  //1,智慧养殖 2,智慧畜牧 3,专家服务台
                                    .eq(LsShedHurdleEntity::getShedHurdleName, k + "栏")
                                    .eq(LsShedHurdleEntity::getDelFlag, 0));
                            if (Objects.isNull(hurdle)) {
                                hurdle = new LsShedHurdleEntity();
                                hurdle.setId(IdWorker.getId());
                                hurdle.setShedHurdleName(k + "栏");
                                hurdle.setShedHurdleDes("create by system");
                                hurdle.setHurdlePid(shack.getId());
                                hurdle.setShedType("0");
                                hurdle.setDelFlag(0);
                                hurdle.setCreateTime(LocalDateTime.now());
                                lsShedHurdleMapper.insert(hurdle);
                            }
                        }
                    }
                }
            }

        }
        return Result.ok(penlanhaoData);
    }

    public static void main(String[] args) {
        Integer totalPartitions = 64;
        Integer taskCount = 30;
        for (Integer task = 0; task < taskCount; task++) {
            Integer index = (task + 16) % taskCount;
            ArrayList<Object> objects = new ArrayList<>();
            for (int i = 0; i < totalPartitions; i++) {
                if (i % taskCount == index) {
                    objects.add(i);
                }
            }
            System.out.println("任务 " + task + "分到的分区为:" + objects.toString());
        }
    }
}
