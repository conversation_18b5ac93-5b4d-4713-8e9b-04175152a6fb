package com.siom.medivh.server.controller;

import com.bo.R;
import com.websocket.service.SimonWebSocketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.socket.TextMessage;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2025-01-13 14:40
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/ws")
public class WS {


    @GetMapping("/sendWs")
    public void sendWs(@RequestParam("id") String id,
                       @RequestParam("message") String message){
        SimonWebSocketService.sendMessageToClient(id, message);
    }
}
