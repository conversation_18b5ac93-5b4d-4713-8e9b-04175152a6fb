package com.siom.medivh.server.mapper.strategy;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siom.medivh.server.entity.strategy.StrategyTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 策略任务Mapper
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface StrategyTaskMapper extends BaseMapper<StrategyTask> {

    /**
     * 查询需要执行的任务（状态为启用且下次执行时间小于等于当前时间）
     */
    @Select("SELECT * FROM t_strategy_task WHERE status = 'ENABLED' AND (next_execute_time IS NULL OR next_execute_time <= #{currentTime})")
    List<StrategyTask> selectTasksToExecute(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 更新任务执行时间
     */
    @Update("UPDATE t_strategy_task SET last_execute_time = #{lastExecuteTime}, next_execute_time = #{nextExecuteTime} WHERE id = #{id}")
    int updateExecuteTime(@Param("id") Long id, 
                         @Param("lastExecuteTime") LocalDateTime lastExecuteTime, 
                         @Param("nextExecuteTime") LocalDateTime nextExecuteTime);

    /**
     * 根据组ID查询任务
     */
    @Select("SELECT * FROM t_strategy_task WHERE group_id = #{groupId}")
    List<StrategyTask> selectByGroupId(@Param("groupId") String groupId);

    /**
     * 根据策略类型查询任务
     */
    @Select("SELECT * FROM t_strategy_task WHERE strategy_type = #{strategyType}")
    List<StrategyTask> selectByStrategyType(@Param("strategyType") String strategyType);

    /**
     * 根据状态查询任务
     */
    @Select("SELECT * FROM t_strategy_task WHERE status = #{status}")
    List<StrategyTask> selectByStatus(@Param("status") String status);
}
