package com.siom.medivh.server.mapper.strategy;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siom.medivh.server.entity.strategy.TrackerClip;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 追踪链接弹夹Mapper
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface TrackerClipMapper extends BaseMapper<TrackerClip> {

    /**
     * 获取策略的第一个未使用的弹夹
     */
    @Select("SELECT * FROM t_tracker_clip WHERE strategy_id = #{strategyId} AND status = 'unused' ORDER BY created_time ASC LIMIT 1")
    TrackerClip selectFirstUnusedByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 标记弹夹为已使用
     */
    @Update("UPDATE t_tracker_clip SET status = 'used', used_time = #{usedTime} WHERE id = #{id}")
    int markAsUsed(@Param("id") Long id, @Param("usedTime") LocalDateTime usedTime);

    /**
     * 统计策略的未使用弹夹数量
     */
    @Select("SELECT COUNT(*) FROM t_tracker_clip WHERE strategy_id = #{strategyId} AND status = 'unused'")
    long countUnusedByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据策略ID查询弹夹
     */
    @Select("SELECT * FROM t_tracker_clip WHERE strategy_id = #{strategyId}")
    List<TrackerClip> selectByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据状态查询弹夹
     */
    @Select("SELECT * FROM t_tracker_clip WHERE status = #{status}")
    List<TrackerClip> selectByStatus(@Param("status") String status);

    /**
     * 根据策略ID和状态查询弹夹
     */
    @Select("SELECT * FROM t_tracker_clip WHERE strategy_id = #{strategyId} AND status = #{status}")
    List<TrackerClip> selectByStrategyIdAndStatus(@Param("strategyId") Long strategyId, @Param("status") String status);
}
