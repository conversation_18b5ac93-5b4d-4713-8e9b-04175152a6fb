#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK信息提取工具 - 提取Bundle ID、版本信息等
"""

import os
import subprocess
import zipfile
import xml.etree.ElementTree as ET
import sys
from pathlib import Path

class ApkAnalyzer:
    def __init__(self, apk_path):
        self.apk_path = Path(apk_path)
        if not self.apk_path.exists():
            raise FileNotFoundError(f"APK文件不存在: {apk_path}")
    
    def get_bundle_id(self):
        """
        提取APK的Bundle ID（包名）
        """
        try:
            # 方法1: 使用aapt工具（如果可用）
            bundle_id = self._get_bundle_id_with_aapt()
            if bundle_id:
                return bundle_id
            
            # 方法2: 直接解析APK中的AndroidManifest.xml
            bundle_id = self._get_bundle_id_from_manifest()
            if bundle_id:
                return bundle_id
            
            # 方法3: 使用jadx命令行工具
            bundle_id = self._get_bundle_id_with_jadx()
            if bundle_id:
                return bundle_id
                
            return None
            
        except Exception as e:
            print(f"❌ 提取Bundle ID失败: {e}")
            return None
    
    def _get_bundle_id_with_aapt(self):
        """使用aapt工具提取Bundle ID"""
        try:
            cmd = ['aapt', 'dump', 'badging', str(self.apk_path)]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if line.startswith('package:'):
                        # 解析: package: name='com.example.app' versionCode='1'
                        parts = line.split("'")
                        if len(parts) >= 2:
                            return parts[1]
            return None
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return None
    
    def _get_bundle_id_from_manifest(self):
        """直接从APK中的AndroidManifest.xml提取Bundle ID"""
        try:
            with zipfile.ZipFile(self.apk_path, 'r') as apk_zip:
                # 读取AndroidManifest.xml（二进制格式）
                manifest_data = apk_zip.read('AndroidManifest.xml')
                
                # 注意：APK中的AndroidManifest.xml是二进制格式
                # 需要使用专门的工具解析，这里只是示例
                # 实际使用中建议用aapt或jadx
                
                return None  # 二进制格式需要特殊解析
        except Exception:
            return None
    
    def _get_bundle_id_with_jadx(self):
        """使用jadx提取Bundle ID"""
        try:
            # 创建临时目录
            temp_dir = self.apk_path.parent / f"{self.apk_path.stem}_temp"
            
            # 使用jadx反编译
            cmd = ['jadx', '-d', str(temp_dir), str(self.apk_path)]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                # 查找AndroidManifest.xml
                manifest_path = temp_dir / 'resources' / 'AndroidManifest.xml'
                if manifest_path.exists():
                    bundle_id = self._parse_manifest_xml(manifest_path)
                    
                    # 清理临时文件
                    self._cleanup_temp_dir(temp_dir)
                    return bundle_id
            
            return None
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return None
    
    def _parse_manifest_xml(self, manifest_path):
        """解析AndroidManifest.xml文件"""
        try:
            tree = ET.parse(manifest_path)
            root = tree.getroot()
            
            # 查找package属性
            package = root.get('package')
            return package
        except Exception:
            return None
    
    def _cleanup_temp_dir(self, temp_dir):
        """清理临时目录"""
        try:
            import shutil
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
        except Exception:
            pass
    
    def get_app_info(self):
        """
        获取APK的完整信息
        """
        info = {
            'file_path': str(self.apk_path),
            'file_size': self.apk_path.stat().st_size,
            'bundle_id': self.get_bundle_id(),
        }
        
        # 尝试获取更多信息
        try:
            cmd = ['aapt', 'dump', 'badging', str(self.apk_path)]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if line.startswith('package:'):
                        # package: name='com.example.app' versionCode='1' versionName='1.0'
                        parts = line.split("'")
                        if len(parts) >= 6:
                            info['version_code'] = parts[3]
                            info['version_name'] = parts[5]
                    elif line.startswith('application-label:'):
                        # application-label:'App Name'
                        parts = line.split("'")
                        if len(parts) >= 2:
                            info['app_name'] = parts[1]
                    elif line.startswith('sdkVersion:'):
                        # sdkVersion:'21'
                        info['min_sdk'] = line.split("'")[1]
                    elif line.startswith('targetSdkVersion:'):
                        # targetSdkVersion:'30'
                        info['target_sdk'] = line.split("'")[1]
        except Exception:
            pass
        
        return info
    
    def print_info(self):
        """打印APK信息"""
        info = self.get_app_info()
        
        print("📱 APK信息分析结果:")
        print("=" * 50)
        print(f"📁 文件路径: {info['file_path']}")
        print(f"📏 文件大小: {info['file_size']:,} bytes")
        print(f"📦 Bundle ID: {info.get('bundle_id', '未知')}")
        print(f"🏷️  应用名称: {info.get('app_name', '未知')}")
        print(f"🔢 版本号: {info.get('version_name', '未知')} ({info.get('version_code', '未知')})")
        print(f"📱 最低SDK: {info.get('min_sdk', '未知')}")
        print(f"🎯 目标SDK: {info.get('target_sdk', '未知')}")
        print("=" * 50)

def main():
    if len(sys.argv) < 2:
        print("使用方法: python ApkAnalyzer.py <apk_file>")
        print("示例: python ApkAnalyzer.py app.apk")
        sys.exit(1)
    
    apk_file = sys.argv[1]
    
    try:
        analyzer = ApkAnalyzer(apk_file)
        analyzer.print_info()
        
        # 只输出Bundle ID
        bundle_id = analyzer.get_bundle_id()
        if bundle_id:
            print(f"\n🎯 Bundle ID: {bundle_id}")
        else:
            print("\n❌ 无法提取Bundle ID")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
