package com.siom.medivh.server.service.strategy;

import com.siom.medivh.server.entity.strategy.StrategyTask;
import com.siom.medivh.server.entity.strategy.TrackerClip;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.IdUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 策略调度服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Service
public class StrategySchedulerService {

    @Autowired
    private StrategyTaskService strategyTaskService;

    @Autowired
    private TrackerClipService trackerClipService;

    @Autowired
    private StrategyExecuteLogService strategyExecuteLogService;

    @Autowired
    private StrategyExecutorService strategyExecutorService;

    /**
     * 定时扫描并执行策略任务
     * 每分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    public void scanAndExecuteTasks() {
        log.info("开始扫描需要执行的策略任务...");
        
        strategyTaskService.getTasksToExecute()
                .stream()
                .peek(task -> log.info("准备执行策略任务: {}, ID: {}", task.getStrategyName(), task.getId()))
                .forEach(this::executeTask);
        
        log.info("策略任务扫描完成");
    }

    /**
     * 执行单个策略任务
     */
    private void executeTask(StrategyTask task) {
        String groupTraceId = generateGroupTraceId(task);
        
        try {
            // 根据策略类型执行不同的逻辑
            boolean success = executeByStrategyType(task, groupTraceId);
            
            // 更新任务执行时间
            updateTaskExecuteTime(task);
            
            // 记录执行日志
            strategyExecuteLogService.recordLog(
                    task.getId(),
                    task.getStrategyType(),
                    task.getOfferId(),
                    task.getAdvertiserOfferId(),
                    groupTraceId,
                    success,
                    success ? "执行成功" : "执行失败",
                    null,
                    null
            );
            
            log.info("策略任务执行完成: {}, 结果: {}", task.getStrategyName(), success ? "成功" : "失败");
            
        } catch (Exception e) {
            log.error("策略任务执行异常: {}, 错误: {}", task.getStrategyName(), e.getMessage(), e);
            
            // 记录异常日志
            strategyExecuteLogService.recordLog(
                    task.getId(),
                    task.getStrategyType(),
                    task.getOfferId(),
                    task.getAdvertiserOfferId(),
                    groupTraceId,
                    false,
                    "执行异常: " + e.getMessage(),
                    null,
                    null
            );
        }
    }

    /**
     * 根据策略类型执行不同的逻辑
     */
    private boolean executeByStrategyType(StrategyTask task, String groupTraceId) {
        switch (task.getStrategyType()) {
            case "APPMETRICA_CLIP":
                return executeAppmetricaClip(task, groupTraceId);
            case "APPSFLYER_PARAM_ROTATE":
                return executeAppsflyerParamRotate(task, groupTraceId);
            default:
                log.warn("未知的策略类型: {}", task.getStrategyType());
                return false;
        }
    }

    /**
     * 执行 APPMETRICA_CLIP 策略
     */
    private boolean executeAppmetricaClip(StrategyTask task, String groupTraceId) {
        return trackerClipService.consumeClip(task.getId())
                .map(clip -> {
                    log.info("消费弹夹成功: {}", clip.getTrackerUrl());
                    return strategyExecutorService.executeAppmetricaClip(task, clip, groupTraceId);
                })
                .orElseGet(() -> {
                    log.warn("策略 {} 没有可用的弹夹", task.getStrategyName());
                    return false;
                });
    }

    /**
     * 执行 APPSFLYER_PARAM_ROTATE 策略
     */
    private boolean executeAppsflyerParamRotate(StrategyTask task, String groupTraceId) {
        return strategyExecutorService.executeAppsflyerParamRotate(task, groupTraceId);
    }

    /**
     * 更新任务执行时间
     */
    private void updateTaskExecuteTime(StrategyTask task) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextExecuteTime = calculateNextExecuteTime(task.getCron(), now);
        
        strategyTaskService.updateExecuteTime(task.getId(), now, nextExecuteTime);
    }

    /**
     * 计算下次执行时间
     */
    private LocalDateTime calculateNextExecuteTime(String cron, LocalDateTime currentTime) {
        // 这里可以使用 Quartz 的 CronExpression 来计算下次执行时间
        // 简单实现：默认每小时执行一次
        return currentTime.plusHours(1);
    }

    /**
     * 生成组追踪ID
     */
    private String generateGroupTraceId(StrategyTask task) {
        return StrUtil.isNotEmpty(task.getGroupId()) 
                ? task.getGroupId() + "_" + IdUtil.fastSimpleUUID()
                : "single_" + IdUtil.fastSimpleUUID();
    }
}
