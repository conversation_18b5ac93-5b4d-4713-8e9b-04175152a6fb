package com.siom.medivh.server.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 简单的PDF转Word示例
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
public class SimplePdfToWordDemo {

    /**
     * 最简单的PDF转Word方法
     */
    public static boolean convertPdfToWord(String pdfFilePath, String wordFilePath) {
        try {
            System.out.println("🔄 开始转换: " + pdfFilePath);
            
            // 第一步：从PDF提取文本
            String text = extractTextFromPdf(pdfFilePath);
            
            // 第二步：创建Word文档
            createWordDocument(text, wordFilePath);
            
            System.out.println("✅ 转换完成: " + wordFilePath);
            return true;
            
        } catch (Exception e) {
            System.err.println("❌ 转换失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 从PDF提取文本
     */
    private static String extractTextFromPdf(String pdfFilePath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(pdfFilePath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    /**
     * 创建Word文档
     */
    private static void createWordDocument(String text, String wordFilePath) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(wordFilePath)) {
            
            // 创建段落并添加文本
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(text);
            run.setFontFamily("微软雅黑");
            run.setFontSize(12);
            
            // 保存文档
            document.write(out);
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 示例用法
        String pdfFile = "D:\\test.pdf";      // 输入PDF文件路径
        String wordFile = "D:\\output.docx";  // 输出Word文件路径
        
        // 转换
        boolean success = convertPdfToWord(pdfFile, wordFile);
        
        if (success) {
            System.out.println("🎉 转换成功！");
        } else {
            System.out.println("💥 转换失败！");
        }
    }
}
