package com.siom.medivh.server.controller;

import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;

import java.time.Duration;
import java.util.Collections;
import java.util.Properties;

public class KafkaPartitionConsumer {

    public static void main(String[] args) {
        String topic = "TEST_DC_TW_CLICK_STREAM";
        int partitionNumber = 5; // 要读取的分区编号

        // 配置消费者属性
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "159.138.129.186:9092"); // 替换为你的 Kafka 地址
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "prod-group-tw");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest"); // 没有已提交 offset 时从头开始
        props.put("max.poll.interval.ms", 600000); // 设置为 10 分钟
        props.put("max.poll.records", 100); // 默认是 500

        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
            // 指定只消费某个分区
            TopicPartition partition = new TopicPartition(topic, partitionNumber);
            consumer.assign(Collections.singletonList(partition));

            // 可选：从分区起始位置读取（可改为 seek() 设置特定 offset）
            consumer.seekToBeginning(Collections.singletonList(partition));

            System.out.printf("开始拉取 topic [%s] 的分区 [%d] 数据...\n", topic, partitionNumber);

            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                consumer.commitAsync();
            }
        }
    }
}
