package com.siom.medivh.server.controller;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-03 12:13
 **/
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;

public class KafkaGroupLagChecker {

    public static void main(String[] args) throws ExecutionException, InterruptedException {
        String bootstrapServers = "************:9094,***************:9094,*************:9094";

        // AdminClient 用于获取 group 列表 和 offset 信息
        Properties adminProps = new Properties();
        adminProps.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        AdminClient adminClient = AdminClient.create(adminProps);

        // 获取所有消费者组
        Collection<ConsumerGroupListing> groupListings = adminClient.listConsumerGroups().all().get();

        // 创建一个临时 KafkaConsumer 获取最新 offset
        Properties consumerProps = new Properties();
        consumerProps.put("bootstrap.servers", bootstrapServers);
        consumerProps.put("key.deserializer", StringDeserializer.class.getName());
        consumerProps.put("value.deserializer", StringDeserializer.class.getName());
        consumerProps.put("group.id", "temp-group-" + UUID.randomUUID());
        consumerProps.put("auto.offset.reset", "latest");

        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(consumerProps);

        Map<String, Long> groupLagMap = new HashMap<>();

        for (ConsumerGroupListing groupListing : groupListings) {
            String groupId = groupListing.groupId();

            try {
                Map<TopicPartition, OffsetAndMetadata> consumedOffsets =
                        adminClient.listConsumerGroupOffsets(groupId).partitionsToOffsetAndMetadata().get();

                if (consumedOffsets == null || consumedOffsets.isEmpty()) {
                    continue;
                }

                Map<TopicPartition, Long> endOffsets = consumer.endOffsets(consumedOffsets.keySet());

                long totalLag = 0;
                for (TopicPartition tp : consumedOffsets.keySet()) {
                    long current = consumedOffsets.get(tp).offset();
                    long end = endOffsets.getOrDefault(tp, current);
                    totalLag += Math.max(end - current, 0);
                }

                groupLagMap.put(groupId, totalLag);

            } catch (Exception e) {
                System.err.println("Failed to fetch lag for group " + groupId + ": " + e.getMessage());
            }
        }

        // 排序并输出
        groupLagMap.entrySet().stream()
                .filter(e -> e.getValue() > 0)
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .forEach(entry -> System.out.printf("Group: %-40s Lag: %d%n", entry.getKey(), entry.getValue()));

        consumer.close();
        adminClient.close();
    }
}

