package com.siom.medivh.server.controller;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Random;
import java.util.concurrent.ThreadPoolExecutor;

public class PostbackTest {

    public static void main(String[] args) {


        for (int i = 0; i < 10; i++) {
            createTextMessage();
        }
    }


    public static void createTextMessage() {
        Random random = new Random(100);
        // 生成随机的subClickId
        int subClickId = random.nextInt() * random.nextInt();
        // 生成固定的subSiteId
        String subSiteId = "1609,2";
        // 生成随机的eventName
        String[] eventNames = {"install", "open", "click"};
        String eventName = eventNames[random.nextInt(eventNames.length)];
        // 构造 URL
        System.out.println(subClickId);
        System.out.println(subSiteId);
        System.out.println(eventName);
        //String url = "http://api.dockads.com/app-api/transform/ascribe/to/postback?subClickId=11&subSiteId=11&eventName=11";
        String url = "http:localhost:48888/ascribe/to/postback?subClickId=11&subSiteId=11&eventName=11";
        try {
            URL parsedUrl = new URL(url);
            String query = parsedUrl.getQuery();

            query = query.replace("subClickId=11", "subClickId=" + subClickId)
                    .replace("subSiteId=11", "subSiteId=" + subSiteId)
                    .replace("eventName=11", "eventName=" + eventName);

            String finalUrl = parsedUrl.getProtocol() + "://" + parsedUrl.getHost() + parsedUrl.getPath() + "?" + query;
            System.out.println(finalUrl);

            // 发送请求
            HttpURLConnection connection = (HttpURLConnection) new URL(finalUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);  // 设置连接超时时间
            connection.setReadTimeout(5000);  // 设置读取超时时间
            String responseMessage = connection.getResponseMessage();
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                System.out.println("Successfully sent request to " + finalUrl + ": " + responseCode + " " + responseMessage);
            } else {
                System.out.println("Failed to send request to " + finalUrl + ": " + responseCode + " " + responseMessage);
            }
            // 读取响应体
            BufferedReader in;
            if (responseCode >= 200 && responseCode < 300) {
                // 正常响应，读取正常响应体x
                in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                // 错误响应，读取错误响应体
                in = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            // 输出完整的响应内容
            System.out.println("Response Body: " + response.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
