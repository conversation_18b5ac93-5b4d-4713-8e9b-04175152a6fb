package com.siom.medivh.server.mapper.strategy;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siom.medivh.server.entity.strategy.StrategyConfigGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 策略配置组Mapper
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface StrategyConfigGroupMapper extends BaseMapper<StrategyConfigGroup> {

    /**
     * 根据状态查询配置组
     */
    @Select("SELECT * FROM t_strategy_config_group WHERE status = #{status}")
    List<StrategyConfigGroup> selectByStatus(@Param("status") String status);
}
