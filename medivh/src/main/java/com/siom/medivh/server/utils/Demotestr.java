package com.siom.medivh.server.utils;

import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeParseException;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-08-11 12:11
 **/
public class Demotestr {

    public static void main(String[] args) {
        main2("执行器1","04:00:23","04:03:29",523200);
        main2("执行器2","04:00:25","04:03:34",515260);
        main2("执行器3","04:00:28","04:04:11",631680);

    }



    public static void main2(String name,String startTimeStr,String endTimeStr,int totalNumber) {
        try {
            // 解析时间字符串
            LocalTime startTime = LocalTime.parse(startTimeStr);
            LocalTime endTime = LocalTime.parse(endTimeStr);

            // 计算时间差（秒）
            long seconds = Duration.between(startTime, endTime).getSeconds();

            if (seconds <= 0) {
                System.out.println("结束时间必须大于开始时间");
                return;
            }

            // 计算平均值
            double average = (double) totalNumber / seconds;

            // 输出结果
            System.out.println(name+" -> 时间差(秒): " + seconds);
            System.out.println(name+ " -> 平均值(" + totalNumber + " / " + seconds + "): " +
                    String.format("%.4f", average) + "/s");
        } catch (DateTimeParseException e) {
            System.out.println("时间格式错误，请使用 HH:mm:ss 格式");
        }
    }
}
