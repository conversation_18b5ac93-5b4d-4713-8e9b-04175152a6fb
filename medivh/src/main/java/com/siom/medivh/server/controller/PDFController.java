package com.siom.medivh.server.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siom.medivh.server.cert.sdk.vo.request.SignRequest;
import com.siom.medivh.server.utils.PdfEncryptUtil;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-05-21 15:18
 **/
@RestController
@RequestMapping("/pdf")
public class PDFController {
    @Resource
    private OpenSignController openSignController;

    @PostMapping("/base64Encode")
    public String base64Encode() throws Exception {
        String andEncodeImage = ObsImageToBase64.downloadAndEncodeImage("atlas-data", "public/wechat_2025-05-21_160832_378.png");
        System.out.println(andEncodeImage);
        return andEncodeImage;
    }
    //SignRequest
    @PostMapping(value ="/sign", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<byte[]> sign(@RequestParam("request") String requestJson, @RequestPart("file") MultipartFile file) throws Exception {
        // 1. 把 JSON 字符串反序列化成 SignRequest
        SignRequest request = new ObjectMapper().readValue(requestJson, SignRequest.class);
        return openSignController.doSign(request,file);
    }


    @PostMapping("/encrypt")
    public ResponseEntity<byte[]> encryptPdf(@RequestParam("file") MultipartFile file) throws Exception {
        byte[] encrypted = PdfEncryptUtil.encryptPdf(
                file.getBytes(),
                "123"     // 拥有者密码（可用于解除密码）
        );
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=encrypted.pdf")
                .body(encrypted);
    }


    public byte[] getResourceFiles(String path) {
        try {
            InputStream inputStream = ResourceUtils.class.getClassLoader()
                    .getResourceAsStream(path);
            return read(inputStream);
        } catch (Exception e) {
            System.err.println(path);
            e.printStackTrace();
        }
        return null;
    }


    public byte[] read(InputStream inputStream) throws IOException {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int num = inputStream.read(buffer);
            while (num != -1) {
                baos.write(buffer, 0, num);
                num = inputStream.read(buffer);
            }
            baos.flush();
            return baos.toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
}
