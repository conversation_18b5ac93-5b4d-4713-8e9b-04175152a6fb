package com.siom.medivh.server.utils;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-08-25 14:22
 **/
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.DeleteTopicsResult;

import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.ExecutionException;

public class KafkaTopicDeleter {

    public static void main(String[] args) {
        // Kafka 服务器地址
        String kafkaServer = "***************:9092"; // 修改为你的 Kafka 服务器地址
        String topicName = "TEST_DC_RU_CLICK_STREAM"; // 修改为要删除的 Topic 名称

        // 如果通过命令行参数传递
        if (args.length >= 1) {
            topicName = args[0];
        }
        if (args.length >= 2) {
            kafkaServer = args[1];
        }

        deleteTopic(kafkaServer, topicName);
    }

    public static void deleteTopic(String kafkaServer, String topicName) {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServer);

        try (AdminClient adminClient = AdminClient.create(props)) {
            // 检查 Topic 是否存在
            if (!adminClient.listTopics().names().get().contains(topicName)) {
                System.out.println("❌ Topic 不存在: " + topicName);
                return;
            }

            // 删除 Topic
            DeleteTopicsResult result = adminClient.deleteTopics(Collections.singletonList(topicName));
            result.all().get(); // 等待删除完成

            System.out.println("✅ Topic 删除成功: " + topicName);

        } catch (InterruptedException | ExecutionException e) {
            System.err.println("❌ 删除 Topic 失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("❌ 连接 Kafka 失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}