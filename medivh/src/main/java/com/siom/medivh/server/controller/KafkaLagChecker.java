package com.siom.medivh.server.controller;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-03 18:51
 **/

import org.apache.kafka.clients.admin.*;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.*;
import org.apache.kafka.common.errors.TopicExistsException;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.lang.reflect.Field;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;

public class KafkaLagChecker {

    public static void main(String[] args) throws Exception {
       // demo1();
        //demo4();
        //demo3();
        //demo1("TEST_DC_TW_CLICK_STREAM","prod-group-tw");
        //createTopic("TEST_DC_TW_CLICK_STREAM",10, (short) 1);
        //createPartitions("TEST_DC_TW_CLICK_STREAM",10, (short) 1);
        demo5();
    }

    public static void createPartitions(String topicName, int partitions, short replication) throws ExecutionException, InterruptedException {
        // 1. 配置 AdminClient
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, "159.138.129.186:9092");
        try (AdminClient adminClient = AdminClient.create(props)) {
            // 2. 构造 NewPartitions 请求
            Map<String, NewPartitions> newPartitions = Collections.singletonMap(
                    topicName,
                    NewPartitions.increaseTo(partitions)
            );
            // 3. 执行创建分区操作
            adminClient.createPartitions(newPartitions).all().get();
            System.out.printf("成功将 topic [%s] 的分区数扩展为 %d\n", topicName, partitions);
        }
    }

    public static void createTopic(String topicName, int partitions, short replication) {
        //TODO 删除无用topic 必要时候删除
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, "159.138.129.186:9092");
        try (AdminClient adminClient = AdminClient.create(props)) {
            NewTopic newTopic = new NewTopic(topicName, partitions, replication);
            adminClient.createTopics(Collections.singletonList(newTopic)).all().get();
            System.out.println("✅ 自动创建 Topic: " + topicName);
        } catch (Exception e) {
            System.err.println("❌ 创建 Kafka Topic 失败: " + e.getMessage());
        }
    }

    private static void demo1(String topic,String groupId) throws Exception {
        String BOOTSTRAP_SERVERS = "159.138.129.186:9092";
        // 1. 创建 AdminClient
        Properties adminProps = new Properties();
        adminProps.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        AdminClient adminClient = AdminClient.create(adminProps);

        // 4. 创建一个临时消费者，获取每个分区的 end offset
        Properties consumerProps = new Properties();
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, "temp-checker-" + UUID.randomUUID());
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        while (true) {
            // 2. 获取 Topic 分区信息
            List<TopicPartition> partitions = new ArrayList<>();
            DescribeTopicsResult describeTopics = adminClient.describeTopics(Collections.singleton(topic));
            Map<String, TopicDescription> topicDescriptionMap = describeTopics.all().get();
            for (TopicPartitionInfo info : topicDescriptionMap.get(topic).partitions()) {
                partitions.add(new TopicPartition(topic, info.partition()));
            }
            // 3. 获取该消费者组对这些分区的已提交 offset
            Map<TopicPartition, OffsetAndMetadata> committedOffsets =
                    adminClient.listConsumerGroupOffsets(groupId)
                            .partitionsToOffsetAndMetadata().get();
            try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(consumerProps)) {
                Map<TopicPartition, Long> endOffsets = consumer.endOffsets(partitions, Duration.ofSeconds(5));

                System.out.println("===== Topic Lag Info =====");
                long totalLag = 0;
                for (TopicPartition tp : partitions) {
                    long end = endOffsets.getOrDefault(tp, 0L);
                    long committed = committedOffsets.getOrDefault(tp, new OffsetAndMetadata(0L)).offset();
                    long lag = Math.max(end - committed, 0);
                    totalLag += lag;

                    System.out.printf("Partition: %-5d | End: %-10d | Committed: %-10d | Lag: %-10d\n",
                            tp.partition(), end, committed, lag);
                }

                System.out.printf("===> Total Lag for group [%s]: %d\n", groupId, totalLag);
            }
            Thread.sleep(10000);
        }
    }

    private static void demo2(String topic, String groupId) throws ExecutionException, InterruptedException {
        Properties consumerProps = new Properties();
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "159.138.129.186:9092");
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, "temp-checker-" + UUID.randomUUID());
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        consumerProps.put("group.id", groupId);
        StringBuffer sb = new StringBuffer();
        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(consumerProps)) {
            consumer.subscribe(Collections.singletonList(topic));
            for (int i = 0; i < 10; i++) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(10));
                for (ConsumerRecord<String, String> record : records) {
                    sb.append("Topic: " + record.topic() + ", Partition: " + record.partition() + ", Offset: " + record.offset() + ", Timestamp: " + new java.util.Date(record.timestamp()) + ", Value: " + record.value() + "\n");
                }
            }
            System.out.println(sb.toString());
        }

    }


    public static void demo3() throws ExecutionException, InterruptedException {
        String bootstrapServers = "************:9094,***************:9094,*************:9094";
        String topic = "DC_US_CLICK_STREAM";
        // 创建消费者配置
        Properties properties = new Properties();
        properties.put("bootstrap.servers", bootstrapServers);
        properties.put("group.id", "your_group_id");
        properties.put("enable.auto.commit", "false");
        properties.put("key.deserializer", StringDeserializer.class.getName());
        properties.put("value.deserializer", StringDeserializer.class.getName());

        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(properties);

        // 订阅 topic
        consumer.subscribe(Collections.singletonList(topic), new ConsumerRebalanceListener() {
            @Override
            public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
                System.out.println("Partitions assigned: " + partitions);
            }

            @Override
            public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
                System.out.println("Partitions revoked: " + partitions);
            }
        });

        // 确保分区分配完成后开始消费
        while (true) {
            ConsumerRecords<String, String> records = consumer.poll(1000);
            if (!records.isEmpty()) {
                for (ConsumerRecord<String, String> record : records) {
                    System.out.println("Consumed record: " + record);
                }
            } else {
                System.out.println("No records available. Waiting...");
            }
        }
    }


    public static void demo4() {
        // 设置 Kafka 配置
        String bootstrapServers = "************:9094,***************:9094,*************:9094";
        String topic = "DC_RO_CLICK_STREAM";
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, "your_group_id222");
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");

        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(properties);

        // 设置要消费的分区 0、1、2
        TopicPartition partition0 = new TopicPartition(topic, 0);
        TopicPartition partition1 = new TopicPartition(topic, 1);
        TopicPartition partition2 = new TopicPartition(topic, 2);

        System.out.println("Starting consumer...");
        consumer.assign(Arrays.asList(partition0, partition1, partition2));
        System.out.println("Assigned partitions: 0, 1, 2");

        consumer.seekToBeginning(Arrays.asList(partition0, partition1, partition2));
        System.out.println("Moved to the beginning of the partitions");

        while (true) {
            System.out.println("Polling for records...");
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(5));
            if (records.isEmpty()) {
                System.out.println("No records fetched");
            } else {
                for (ConsumerRecord<String, String> record : records) {
                    System.out.printf("Partition=%d, Offset=%d, Value=%s%n", record.partition(), record.offset(), record.value());
                }
            }
        }

    }

    public static void demo5() throws ExecutionException, InterruptedException {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, "************:9094,***************:9094,*************:9094");
        AdminClient adminClient = AdminClient.create(props);
        String groupId = "prod-group-ru";
        // 获取消费者组的描述信息
        DescribeConsumerGroupsResult result = adminClient.describeConsumerGroups(Collections.singletonList(groupId));
        Map<String, ConsumerGroupDescription> descMap = result.all().get();

        // host -> 分区列表
        Map<String, List<TopicPartition>> hostPartitionMap = new HashMap<>();
        for (ConsumerGroupDescription desc : descMap.values()) {
            for (MemberDescription member : desc.members()) {
                String host = member.host();
                Set<TopicPartition> assignedPartitions = member.assignment().topicPartitions();

                if (!hostPartitionMap.containsKey(host)) {
                    hostPartitionMap.put(host, new ArrayList<TopicPartition>());
                }
                hostPartitionMap.get(host).addAll(assignedPartitions);
            }
        }

        // 打印分组结果
        for (Map.Entry<String, List<TopicPartition>> entry : hostPartitionMap.entrySet()) {
            System.out.println("Host: " + entry.getKey());
            for (TopicPartition tp : entry.getValue()) {
                System.out.println("  -> " + tp);
            }
        }

        adminClient.close();
    }
}

