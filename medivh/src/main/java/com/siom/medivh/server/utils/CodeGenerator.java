package com.siom.medivh.server.utils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 代码生成器 - 根据建表语句生成完整的CRUD代码
 */
public class CodeGenerator {

    private static final String BASE_PATH = "D:/generated-code/";

    public static void main(String[] args) {
        // 示例建表语句
        String createTableSql = "CREATE TABLE `t_offer_mmp_grab_config` (\n" +
                "  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',\n" +
                "  `offer_name` varchar(255) DEFAULT NULL COMMENT 'offer name',\n" +
                "  `offer_id` bigint NOT NULL COMMENT 'Offer ID',\n" +
                "  `country` varchar(255) DEFAULT NULL COMMENT '国家',\n" +
                "  `affise_offer_id` varchar(100) NOT NULL COMMENT 'Affise 平台的 Offer ID',\n" +
                "  `bundle_id` varchar(100) NOT NULL COMMENT 'Bundle ID',\n" +
                "  `pid` varchar(100) DEFAULT NULL COMMENT '归因 Tracker 中的 pid',\n" +
                "  `af_prt` varchar(100) DEFAULT NULL COMMENT '归因 Tracker 中的 af_prt 参数',\n" +
                "  `mmp_type` varchar(50) NOT NULL COMMENT '平台类型：APPSFLYER / ADJUST / APPMETRICA',\n" +
                "  `status` varchar(50) DEFAULT 'ACTIVE' COMMENT '策略状态',\n" +
                "  `campaign` varchar(255) DEFAULT NULL COMMENT 'campaign',\n" +
                "  `origin_url` varchar(1024) DEFAULT NULL COMMENT '上次抓取的url信息',\n" +
                "  `create_time` datetime DEFAULT NULL COMMENT '创建时间',\n" +
                "  `create_user_id` bigint DEFAULT NULL COMMENT '创建人id',\n" +
                "  `create_user_name` varchar(64) DEFAULT NULL COMMENT '创建人名称',\n" +
                "  `update_time` datetime DEFAULT NULL COMMENT '更新时间',\n" +
                "  `update_user_id` bigint DEFAULT NULL COMMENT '更新人id',\n" +
                "  `update_user_name` varchar(64) DEFAULT NULL COMMENT '更新人名称',\n" +
                "  `del_status` tinyint(1) DEFAULT '0' COMMENT '删除状态',\n" +
                "  PRIMARY KEY (`id`)\n" +
                ") ENGINE=InnoDB AUTO_INCREMENT=1948340027983564803 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='MMP数据抓取策略配置';";
        generateCode(createTableSql);
    }

    public static void generateCode(String createTableSql) {
        TableInfo tableInfo = parseCreateTableSql(createTableSql);
        String baseDir = BASE_PATH + tableInfo.getClassName() + "/";

        try {
            // 创建目录结构
            createDirectories(baseDir);

            // 生成实体类
            generateEntity(baseDir, tableInfo);

            // 生成Mapper接口
            generateMapper(baseDir, tableInfo);

            // 生成Service接口和实现
            generateService(baseDir, tableInfo);
            generateServiceImpl(baseDir, tableInfo);

            // 生成Model类
            generateModels(baseDir, tableInfo);

            // 生成Controller
            generateController(baseDir, tableInfo);

            System.out.println("代码生成完成，路径: " + baseDir);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void createDirectories(String baseDir) {
        new File(baseDir + "entity").mkdirs();
        new File(baseDir + "mapper").mkdirs();
        new File(baseDir + "service").mkdirs();
        new File(baseDir + "service/impl").mkdirs();
        new File(baseDir + "model").mkdirs();
        new File(baseDir + "controller").mkdirs();
    }

    private static void generateEntity(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.framework.core.entity.aff;\n\n" +
                "import com.baomidou.mybatisplus.annotation.*;\n" +
                "import lombok.Data;\n" +
                "import lombok.EqualsAndHashCode;\n\n" +
                "import java.time.LocalDateTime;\n" +
                "import java.math.BigDecimal;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " 实体类\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Data\n" +
                "@EqualsAndHashCode(callSuper = false)\n" +
                "@TableName(\"" + tableInfo.getTableName() + "\")\n" +
                "public class " + tableInfo.getClassName() + " {\n\n" +
                generateEntityFields(tableInfo.getColumns()) +
                "}\n";

        writeToFile(baseDir + "entity/" + tableInfo.getClassName() + ".java", content);
    }

    private static void generateMapper(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.framework.core.mapper.aff;\n\n" +
                "import com.atlas.framework.core.entity.aff." + tableInfo.getClassName() + ";\n" +
                "import com.baomidou.mybatisplus.core.mapper.BaseMapper;\n" +
                "import org.apache.ibatis.annotations.Mapper;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " Mapper接口\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Mapper\n" +
                "public interface " + tableInfo.getClassName() + "Mapper extends BaseMapper<" + tableInfo.getClassName() + "> {\n" +
                "    \n" +
                "}\n";

        writeToFile(baseDir + "mapper/" + tableInfo.getClassName() + "Mapper.java", content);
    }

    private static void generateService(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.module.incent.service.aff;\n\n" +
                "import com.atlas.framework.core.entity.aff." + tableInfo.getClassName() + ";\n" +
                "import com.baomidou.mybatisplus.extension.service.IService;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " Service接口\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "public interface " + tableInfo.getClassName() + "Service extends IService<" + tableInfo.getClassName() + "> {\n" +
                "    \n" +
                "}\n";

        writeToFile(baseDir + "service/" + tableInfo.getClassName() + "Service.java", content);
    }

    private static void generateServiceImpl(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.module.incent.service.aff.impl;\n\n" +
                "import com.atlas.framework.core.entity.aff." + tableInfo.getClassName() + ";\n" +
                "import com.atlas.framework.core.mapper.aff." + tableInfo.getClassName() + "Mapper;\n" +
                "import com.atlas.module.incent.service.aff." + tableInfo.getClassName() + "Service;\n" +
                "import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;\n" +
                "import lombok.extern.slf4j.Slf4j;\n" +
                "import org.springframework.stereotype.Service;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " Service实现类\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Slf4j\n" +
                "@Service\n" +
                "public class " + tableInfo.getClassName() + "ServiceImpl extends ServiceImpl<" + tableInfo.getClassName() + "Mapper, " + tableInfo.getClassName() + "> implements " + tableInfo.getClassName() + "Service {\n" +
                "    \n" +
                "}\n";

        writeToFile(baseDir + "service/impl/" + tableInfo.getClassName() + "ServiceImpl.java", content);
    }

    private static void generateModels(String baseDir, TableInfo tableInfo) throws IOException {
        // SaveModel
        generateSaveModel(baseDir, tableInfo);
        // UpdateModel
        generateUpdateModel(baseDir, tableInfo);
        // PageModel
        generatePageModel(baseDir, tableInfo);
        // RespModel
        generateRespModel(baseDir, tableInfo);
    }

    private static void generateSaveModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.module.incent.model.aff;\n\n" +
                "import com.atlas.framework.mybatis.model.SaveModel;\n" +
                "import io.swagger.v3.oas.annotations.media.Schema;\n" +
                "import lombok.Data;\n" +
                "import lombok.EqualsAndHashCode;\n\n" +
                "import javax.validation.constraints.NotNull;\n" +
                "import java.time.LocalDateTime;\n" +
                "import java.math.BigDecimal;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " 保存请求模型\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Data\n" +
                "@EqualsAndHashCode(callSuper = true)\n" +
                "@Schema(description = \"" + tableInfo.getComment() + "保存请求\")\n" +
                "public class " + tableInfo.getClassName() + "SaveModel extends SaveModel {\n\n" +
                generateSaveModelFields(tableInfo.getColumns()) +
                "}\n";

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "SaveModel.java", content);
    }

    private static void generateUpdateModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.module.incent.model.aff;\n\n" +
                "import com.atlas.framework.mybatis.model.UpdateModel;\n" +
                "import io.swagger.v3.oas.annotations.media.Schema;\n" +
                "import lombok.Data;\n" +
                "import lombok.EqualsAndHashCode;\n\n" +
                "import javax.validation.constraints.NotNull;\n" +
                "import java.time.LocalDateTime;\n" +
                "import java.math.BigDecimal;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " 更新请求模型\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Data\n" +
                "@EqualsAndHashCode(callSuper = true)\n" +
                "@Schema(description = \"" + tableInfo.getComment() + "更新请求\")\n" +
                "public class " + tableInfo.getClassName() + "UpdateModel extends UpdateModel {\n\n" +
                generateUpdateModelFields(tableInfo.getColumns()) +
                "}\n";

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "UpdateModel.java", content);
    }

    private static void generatePageModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.module.incent.model.aff;\n\n" +
                "import com.atlas.framework.mybatis.model.PageModel;\n" +
                "import io.swagger.v3.oas.annotations.media.Schema;\n" +
                "import lombok.Data;\n" +
                "import lombok.EqualsAndHashCode;\n\n" +
                "import java.time.LocalDateTime;\n" +
                "import java.math.BigDecimal;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " 分页查询请求模型\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Data\n" +
                "@EqualsAndHashCode(callSuper = true)\n" +
                "@Schema(description = \"" + tableInfo.getComment() + "分页查询请求\")\n" +
                "public class " + tableInfo.getClassName() + "PageModel extends PageModel {\n\n" +
                generatePageModelFields(tableInfo.getColumns()) +
                "}\n";

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "PageModel.java", content);
    }

    private static void generateRespModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.module.incent.model.aff;\n\n" +
                "import io.swagger.v3.oas.annotations.media.Schema;\n" +
                "import lombok.Data;\n\n" +
                "import java.time.LocalDateTime;\n" +
                "import java.math.BigDecimal;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " 响应模型\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Data\n" +
                "@Schema(description = \"" + tableInfo.getComment() + "响应\")\n" +
                "public class " + tableInfo.getClassName() + "RespModel {\n\n" +
                generateRespModelFields(tableInfo.getColumns()) +
                "}\n";

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "RespModel.java", content);
    }

    private static void generateController(String baseDir, TableInfo tableInfo) throws IOException {
        String content = "package com.atlas.module.incent.controller.admin.aff;\n\n" +
                "import com.atlas.framework.core.entity.aff." + tableInfo.getClassName() + ";\n" +
                "import com.atlas.framework.core.mapper.aff." + tableInfo.getClassName() + "Mapper;\n" +
                "import com.atlas.framework.core.utils.OfferCheckUtils;\n" +
                "import com.atlas.framework.mybatis.controller.BaseController;\n" +
                "import com.atlas.framework.utils.CheckedException;\n" +
                "import com.atlas.module.incent.model.aff.*;\n" +
                "import com.atlas.module.incent.service.aff." + tableInfo.getClassName() + "Service;\n" +
                "import com.baomidou.mybatisplus.core.mapper.BaseMapper;\n" +
                "import io.swagger.v3.oas.annotations.tags.Tag;\n" +
                "import jakarta.annotation.Resource;\n" +
                "import lombok.extern.slf4j.Slf4j;\n" +
                "import org.springframework.web.bind.annotation.RequestMapping;\n" +
                "import org.springframework.web.bind.annotation.RestController;\n\n" +
                "/**\n" +
                " * " + tableInfo.getComment() + " 控制器\n" +
                " * \n" +
                " * <AUTHOR> +
                " * @date " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
                " */\n" +
                "@Tag(name = \"[offer改造] - " + tableInfo.getComment() + "\")\n" +
                "@RestController\n" +
                "@Slf4j\n" +
                "@RequestMapping(\"/aff/" + toKebabCase(tableInfo.getClassName()) + "\")\n" +
                "public class " + tableInfo.getClassName() + "Controller extends BaseController<\n" +
                "        " + tableInfo.getClassName() + ",\n" +
                "        " + tableInfo.getClassName() + "SaveModel,\n" +
                "        " + tableInfo.getClassName() + "UpdateModel,\n" +
                "        " + tableInfo.getClassName() + "PageModel,\n" +
                "        " + tableInfo.getClassName() + "RespModel> {\n\n" +
                "    @Resource\n" +
                "    private " + tableInfo.getClassName() + "Mapper " + toLowerCamelCase(tableInfo.getClassName()) + "Mapper;\n" +
                "    \n" +
                "    @Resource\n" +
                "    private " + tableInfo.getClassName() + "Service " + toLowerCamelCase(tableInfo.getClassName()) + "Service;\n\n" +
                "    @Override\n" +
                "    protected BaseMapper<" + tableInfo.getClassName() + "> getMapper() {\n" +
                "        return " + toLowerCamelCase(tableInfo.getClassName()) + "Mapper;\n" +
                "    }\n\n" +
                "    @Override\n" +
                "    protected void beforeCreate(" + tableInfo.getClassName() + "SaveModel saveModel) {\n" +
                "        " + generateBeforeCreateValidation(tableInfo.getColumns()) + "\n" +
                "        saveModel.setOrigin(originAtlas);\n" +
                "    }\n\n" +
                "    @Override\n" +
                "    protected void beforeUpdate(" + tableInfo.getClassName() + "UpdateModel updateModel) {\n" +
                "        " + generateBeforeUpdateValidation(tableInfo.getColumns()) + "\n" +
                "    }\n" +
                "}\n";

        writeToFile(baseDir + "controller/" + tableInfo.getClassName() + "Controller.java", content);
    }

    // 解析建表语句 - 更精确的版本
    private static TableInfo parseCreateTableSql(String sql) {
        TableInfo tableInfo = new TableInfo();

        // 提取表名
        Pattern tablePattern = Pattern.compile("CREATE TABLE `([^`]+)`", Pattern.CASE_INSENSITIVE);
        Matcher tableMatcher = tablePattern.matcher(sql);
        if (tableMatcher.find()) {
            String tableName = tableMatcher.group(1);
            tableInfo.setTableName(tableName);
            tableInfo.setClassName(toPascalCase(tableName.replace("t_", "").replace("aff_", "")));
        }

        // 提取表注释
        Pattern commentPattern = Pattern.compile("COMMENT='([^']+)'\\s*;?\\s*$", Pattern.CASE_INSENSITIVE);
        Matcher commentMatcher = commentPattern.matcher(sql);
        if (commentMatcher.find()) {
            tableInfo.setComment(commentMatcher.group(1));
        }

        // 按行分割SQL，逐行解析字段
        String[] lines = sql.split("\n");
        List<ColumnInfo> columns = new ArrayList<>();

        for (String line : lines) {
            line = line.trim();
            // 匹配字段定义行
            if (line.startsWith("`") && line.contains("COMMENT")) {
                // 更精确的字段解析
                Pattern fieldPattern = Pattern.compile("`([^`]+)`\\s+(\\w+)(?:\\([^)]+\\))?.*?COMMENT\\s+'([^']+)'", Pattern.CASE_INSENSITIVE);
                Matcher fieldMatcher = fieldPattern.matcher(line);

                if (fieldMatcher.find()) {
                    ColumnInfo column = new ColumnInfo();
                    column.setColumnName(fieldMatcher.group(1));
                    column.setColumnType(fieldMatcher.group(2));
                    column.setComment(fieldMatcher.group(3));
                    column.setFieldName(toCamelCase(column.getColumnName()));
                    column.setJavaType(mapToJavaType(column.getColumnType()));
                    columns.add(column);

                    // 调试输出
                    System.out.println("解析字段: " + column.getColumnName() + ", 类型: " + column.getColumnType() + ", 注释: " + column.getComment());
                }
            }
        }

        tableInfo.setColumns(columns);
        return tableInfo;
    }

    // 工具方法
    private static String generateEntityFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 为每个字段添加注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
            }

            // 为主键字段添加特殊注解
            if ("id".equals(column.getColumnName())) {
                sb.append("    @TableId(type = IdType.AUTO)\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateSaveModelFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 跳过自动生成的字段
            if ("id".equals(column.getColumnName()) || "create_time".equals(column.getColumnName()) || "update_time".equals(column.getColumnName())) {
                continue;
            }

            // 添加字段注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
                sb.append("    @Schema(description = \"").append(column.getComment()).append("\")\n");
            }

            // 为包含_id的字段添加非空校验（除了affiliate_id）
            if (column.getColumnName().contains("_id") && !"affiliate_id".equals(column.getColumnName())) {
                sb.append("    @NotNull(message = \"").append(column.getComment() != null ? column.getComment() : column.getFieldName()).append("不能为空\")\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateUpdateModelFields(List<ColumnInfo> columns) {
        return generateSaveModelFields(columns); // 更新模型字段与保存模型相同
    }

    private static String generatePageModelFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 跳过自动生成的字段
            if ("id".equals(column.getColumnName()) || "create_time".equals(column.getColumnName()) || "update_time".equals(column.getColumnName())) {
                continue;
            }

            // 添加字段注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
                sb.append("    @Schema(description = \"").append(column.getComment()).append("\")\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateRespModelFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 添加字段注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
                sb.append("    @Schema(description = \"").append(column.getComment()).append("\")\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateBeforeCreateValidation(List<ColumnInfo> columns) {
        for (ColumnInfo column : columns) {
            if ("offer_id".equals(column.getColumnName())) {
                return "if (!OfferCheckUtils.isExist(saveModel.getOfferId())) {\n" +
                        "            CheckedException.doThrow(\"根据offerId【\" + saveModel.getOfferId() + \"】未找到对应的offer\");\n" +
                        "        }";
            }
        }
        return "// 可在此添加业务校验逻辑";
    }

    private static String generateBeforeUpdateValidation(List<ColumnInfo> columns) {
        for (ColumnInfo column : columns) {
            if ("offer_id".equals(column.getColumnName())) {
                return "if (!OfferCheckUtils.isExist(updateModel.getOfferId())) {\n" +
                        "            CheckedException.doThrow(\"根据offerId【\" + updateModel.getOfferId() + \"】未找到对应的offer\");\n" +
                        "        }";
            }
        }
        return "// 可在此添加业务校验逻辑";
    }

    private static String mapToJavaType(String sqlType) {
        String lowerType = sqlType.toLowerCase();
        switch (lowerType) {
            case "bigint":
                return "Long";
            case "int":
            case "integer":
                return "Integer";
            case "varchar":
            case "text":
            case "char":
                return "String";
            case "decimal":
            case "numeric":
                return "BigDecimal";
            case "datetime":
            case "timestamp":
                return "LocalDateTime";
            case "date":
                return "LocalDate";
            case "time":
                return "LocalTime";
            case "tinyint":
                return "Boolean";
            default:
                return "String";
        }
    }

    private static String toCamelCase(String str) {
        String[] parts = str.split("_");
        StringBuilder result = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            result.append(parts[i].substring(0, 1).toUpperCase()).append(parts[i].substring(1));
        }
        return result.toString();
    }

    private static String toPascalCase(String str) {
        String camelCase = toCamelCase(str);
        return camelCase.substring(0, 1).toUpperCase() + camelCase.substring(1);
    }

    private static String toLowerCamelCase(String str) {
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }

    private static String toKebabCase(String str) {
        return str.replaceAll("([a-z])([A-Z])", "$1-$2").toLowerCase();
    }

    private static void writeToFile(String filePath, String content) throws IOException {
        File file = new File(filePath);
        file.getParentFile().mkdirs();
        try (FileWriter writer = new FileWriter(file)) {
            writer.write(content);
        }
    }

    // 数据结构类
    static class TableInfo {
        private String tableName;
        private String className;
        private String comment;
        private List<ColumnInfo> columns;

        // getters and setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        public List<ColumnInfo> getColumns() { return columns; }
        public void setColumns(List<ColumnInfo> columns) { this.columns = columns; }
    }

    static class ColumnInfo {
        private String columnName;
        private String columnType;
        private String comment;
        private String fieldName;
        private String javaType;

        // getters and setters
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        public String getColumnType() { return columnType; }
        public void setColumnType(String columnType) { this.columnType = columnType; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }
        public String getJavaType() { return javaType; }
        public void setJavaType(String javaType) { this.javaType = javaType; }
    }
}