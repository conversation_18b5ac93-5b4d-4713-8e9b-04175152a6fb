package com.atlas.module.data.utils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 代码生成器 - 根据建表语句生成完整的CRUD代码
 */
public class CodeGenerator {

    private static final String BASE_PATH = "D:/generated-code/";

    public static void main(String[] args) {
        // 示例建表语句
        String createTableSql = """
                CREATE TABLE `t_offer_mmp_grab_config` (
                                                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                                         `offer_name` varchar(255) DEFAULT NULL COMMENT 'offer name',
                                                                         `offer_id` bigint NOT NULL COMMENT 'Offer ID',
                                                                         `country` varchar(255) DEFAULT NULL COMMENT '国家',
                                                                         `affise_offer_id` varchar(100) NOT NULL COMMENT 'Affise 平台的 Offer ID',
                                                                         `bundle_id` varchar(100) NOT NULL COMMENT 'Bundle ID',
                                                                         `pid` varchar(100) DEFAULT NULL COMMENT '归因 Tracker 中的 pid',
                                                                         `af_prt` varchar(100) DEFAULT NULL COMMENT '归因 Tracker 中的 af_prt 参数',
                                                                         `mmp_type` varchar(50) NOT NULL COMMENT '平台类型：APPSFLYER / ADJUST / APPMETRICA',
                                                                         `status` varchar(50) DEFAULT 'ACTIVE' COMMENT '策略状态',
                                                                         `campaign` varchar(255) DEFAULT NULL COMMENT 'campaign',
                                                                         `origin_url` varchar(1024) DEFAULT NULL COMMENT '上次抓取的url信息',
                                                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                                         `create_user_id` bigint DEFAULT NULL COMMENT '创建人id',
                                                                         `create_user_name` varchar(64) DEFAULT NULL COMMENT '创建人名称',
                                                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                                         `update_user_id` bigint DEFAULT NULL COMMENT '更新人id',
                                                                         `update_user_name` varchar(64) DEFAULT NULL COMMENT '更新人名称',
                                                                         `del_status` tinyint(1) DEFAULT '0' COMMENT '删除状态',
                                                                         PRIMARY KEY (`id`)
                                                                       ) ENGINE=InnoDB AUTO_INCREMENT=1948340027983564803 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='MMP数据抓取策略配置';
            """;
        generateCode(createTableSql);
    }

    public static void generateCode(String createTableSql) {
        TableInfo tableInfo = parseCreateTableSql(createTableSql);
        String baseDir = BASE_PATH + tableInfo.getClassName() + "/";

        try {
            // 创建目录结构
            createDirectories(baseDir);

            // 生成实体类
            generateEntity(baseDir, tableInfo);

            // 生成Mapper接口
            generateMapper(baseDir, tableInfo);

            // 生成Service接口和实现
            generateService(baseDir, tableInfo);
            generateServiceImpl(baseDir, tableInfo);

            // 生成Model类
            generateModels(baseDir, tableInfo);

            // 生成Controller
            generateController(baseDir, tableInfo);

            System.out.println("代码生成完成，路径: " + baseDir);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void createDirectories(String baseDir) {
        new File(baseDir + "entity").mkdirs();
        new File(baseDir + "mapper").mkdirs();
        new File(baseDir + "service").mkdirs();
        new File(baseDir + "service/impl").mkdirs();
        new File(baseDir + "model").mkdirs();
        new File(baseDir + "controller").mkdirs();
    }

    private static void generateEntity(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.framework.core.entity.aff;
            
            import com.baomidou.mybatisplus.annotation.*;
            import lombok.Data;
            import lombok.EqualsAndHashCode;
            
            import java.time.LocalDateTime;
            import java.math.BigDecimal;
            
            /**
             * %s 实体类
             * 
             * <AUTHOR>
             * @date %s
             */
            @Data
            @EqualsAndHashCode(callSuper = false)
            @TableName("%s")
            public class %s {
            
            %s
            }
            """.formatted(
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getTableName(),
                tableInfo.getClassName(),
                generateEntityFields(tableInfo.getColumns())
        );

        writeToFile(baseDir + "entity/" + tableInfo.getClassName() + ".java", content);
    }

    private static void generateMapper(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.framework.core.mapper.aff;
            
            import com.atlas.framework.core.entity.aff.%s;
            import com.baomidou.mybatisplus.core.mapper.BaseMapper;
            import org.apache.ibatis.annotations.Mapper;
            
            /**
             * %s Mapper接口
             * 
             * <AUTHOR>
             * @date %s
             */
            @Mapper
            public interface %sMapper extends BaseMapper<%s> {
                
            }
            """.formatted(
                tableInfo.getClassName(),
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getClassName(),
                tableInfo.getClassName()
        );

        writeToFile(baseDir + "mapper/" + tableInfo.getClassName() + "Mapper.java", content);
    }

    private static void generateService(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.module.incent.service.aff;
            
            import com.atlas.framework.core.entity.aff.%s;
            import com.baomidou.mybatisplus.extension.service.IService;
            
            /**
             * %s Service接口
             * 
             * <AUTHOR>
             * @date %s
             */
            public interface %sService extends IService<%s> {
                
            }
            """.formatted(
                tableInfo.getClassName(),
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getClassName(),
                tableInfo.getClassName()
        );

        writeToFile(baseDir + "service/" + tableInfo.getClassName() + "Service.java", content);
    }

    private static void generateServiceImpl(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.module.incent.service.aff.impl;
            
            import com.atlas.framework.core.entity.aff.%s;
            import com.atlas.framework.core.mapper.aff.%sMapper;
            import com.atlas.module.incent.service.aff.%sService;
            import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
            import lombok.extern.slf4j.Slf4j;
            import org.springframework.stereotype.Service;
            
            /**
             * %s Service实现类
             * 
             * <AUTHOR>
             * @date %s
             */
            @Slf4j
            @Service
            public class %sServiceImpl extends ServiceImpl<%sMapper, %s> implements %sService {
                
            }
            """.formatted(
                tableInfo.getClassName(),
                tableInfo.getClassName(),
                tableInfo.getClassName(),
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getClassName(),
                tableInfo.getClassName(),
                tableInfo.getClassName(),
                tableInfo.getClassName()
        );

        writeToFile(baseDir + "service/impl/" + tableInfo.getClassName() + "ServiceImpl.java", content);
    }

    private static void generateModels(String baseDir, TableInfo tableInfo) throws IOException {
        // SaveModel
        generateSaveModel(baseDir, tableInfo);
        // UpdateModel
        generateUpdateModel(baseDir, tableInfo);
        // PageModel
        generatePageModel(baseDir, tableInfo);
        // RespModel
        generateRespModel(baseDir, tableInfo);
    }

    private static void generateSaveModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.module.incent.model.aff;
            
            import com.atlas.framework.mybatis.model.SaveModel;
            import io.swagger.v3.oas.annotations.media.Schema;
            import lombok.Data;
            import lombok.EqualsAndHashCode;
            
            import javax.validation.constraints.NotNull;
            import java.time.LocalDateTime;
            import java.math.BigDecimal;
            
            /**
             * %s 保存请求模型
             * 
             * <AUTHOR>
             * @date %s
             */
            @Data
            @EqualsAndHashCode(callSuper = true)
            @Schema(description = "%s保存请求")
            public class %sSaveModel extends SaveModel {
            
            %s
            }
            """.formatted(
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getComment(),
                tableInfo.getClassName(),
                generateSaveModelFields(tableInfo.getColumns())
        );

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "SaveModel.java", content);
    }

    private static void generateUpdateModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.module.incent.model.aff;
            
            import com.atlas.framework.mybatis.model.UpdateModel;
            import io.swagger.v3.oas.annotations.media.Schema;
            import lombok.Data;
            import lombok.EqualsAndHashCode;
            
            import javax.validation.constraints.NotNull;
            import java.time.LocalDateTime;
            import java.math.BigDecimal;
            
            /**
             * %s 更新请求模型
             * 
             * <AUTHOR>
             * @date %s
             */
            @Data
            @EqualsAndHashCode(callSuper = true)
            @Schema(description = "%s更新请求")
            public class %sUpdateModel extends UpdateModel {
            
            %s
            }
            """.formatted(
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getComment(),
                tableInfo.getClassName(),
                generateUpdateModelFields(tableInfo.getColumns())
        );

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "UpdateModel.java", content);
    }

    private static void generatePageModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.module.incent.model.aff;
            
            import com.atlas.framework.mybatis.model.PageModel;
            import io.swagger.v3.oas.annotations.media.Schema;
            import lombok.Data;
            import lombok.EqualsAndHashCode;
            
            import java.time.LocalDateTime;
            import java.math.BigDecimal;
            
            /**
             * %s 分页查询请求模型
             * 
             * <AUTHOR>
             * @date %s
             */
            @Data
            @EqualsAndHashCode(callSuper = true)
            @Schema(description = "%s分页查询请求")
            public class %sPageModel extends PageModel {
            
            %s
            }
            """.formatted(
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getComment(),
                tableInfo.getClassName(),
                generatePageModelFields(tableInfo.getColumns())
        );

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "PageModel.java", content);
    }

    private static void generateRespModel(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.module.incent.model.aff;
            
            import io.swagger.v3.oas.annotations.media.Schema;
            import lombok.Data;
            
            import java.time.LocalDateTime;
            import java.math.BigDecimal;
            
            /**
             * %s 响应模型
             * 
             * <AUTHOR>
             * @date %s
             */
            @Data
            @Schema(description = "%s响应")
            public class %sRespModel {
            
            %s
            }
            """.formatted(
                tableInfo.getComment(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                tableInfo.getComment(),
                tableInfo.getClassName(),
                generateRespModelFields(tableInfo.getColumns())
        );

        writeToFile(baseDir + "model/" + tableInfo.getClassName() + "RespModel.java", content);
    }

    private static void generateController(String baseDir, TableInfo tableInfo) throws IOException {
        String content = """
            package com.atlas.module.incent.controller.admin.aff;
            
            import com.atlas.framework.core.entity.aff.%s;
            import com.atlas.framework.core.mapper.aff.%sMapper;
            import com.atlas.framework.core.utils.OfferCheckUtils;
            import com.atlas.framework.mybatis.controller.BaseController;
            import com.atlas.framework.utils.CheckedException;
            import com.atlas.module.incent.model.aff.*;
            import com.atlas.module.incent.service.aff.%sService;
            import com.baomidou.mybatisplus.core.mapper.BaseMapper;
            import io.swagger.v3.oas.annotations.tags.Tag;
            import jakarta.annotation.Resource;
            import lombok.extern.slf4j.Slf4j;
            import org.springframework.web.bind.annotation.RequestMapping;
            import org.springframework.web.bind.annotation.RestController;
            
            /**
             * %s 控制器
             * 
             * <AUTHOR>
             * @date %s
             */
            @Tag(name = "[offer改造] - %s")
            @RestController
            @Slf4j
            @RequestMapping("/aff/%s")
            public class %sController extends BaseController<
                    %s,
                    %sSaveModel,
                    %sUpdateModel,
                    %sPageModel,
                    %sRespModel> {
            
                @Resource
                private %sMapper %sMapper;
                
                @Resource
                private %sService %sService;
            
                @Override
                protected BaseMapper<%s> getMapper() {
                    return %sMapper;
                }
            
                @Override
                protected void beforeCreate(%sSaveModel saveModel) {
                    %s
                    saveModel.setOrigin(originAtlas);
                }
            
                @Override
                protected void beforeUpdate(%sUpdateModel updateModel) {
                    %s
                }
            }
            """.formatted(
                tableInfo.getClassName(),                                    // 1
                tableInfo.getClassName(),                                    // 2
                tableInfo.getClassName(),                                    // 3
                tableInfo.getComment(),                                      // 4
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), // 5
                tableInfo.getComment(),                                      // 6
                toKebabCase(tableInfo.getClassName()),                       // 7
                tableInfo.getClassName(),                                    // 8
                tableInfo.getClassName(),                                    // 9
                tableInfo.getClassName(),                                    // 10
                tableInfo.getClassName(),                                    // 11
                tableInfo.getClassName(),                                    // 12
                tableInfo.getClassName(),                                    // 13
                tableInfo.getClassName(),                                    // 14
                toLowerCamelCase(tableInfo.getClassName()),                  // 15
                tableInfo.getClassName(),                                    // 16
                toLowerCamelCase(tableInfo.getClassName()),                  // 17
                tableInfo.getClassName(),                                    // 18
                toLowerCamelCase(tableInfo.getClassName()),                  // 19
                tableInfo.getClassName(),                                    // 20
                generateBeforeCreateValidation(tableInfo.getColumns()),      // 21
                tableInfo.getClassName(),                                    // 22
                generateBeforeUpdateValidation(tableInfo.getColumns())       // 23
        );

        writeToFile(baseDir + "controller/" + tableInfo.getClassName() + "Controller.java", content);
    }

    // 解析建表语句 - 更精确的版本
    private static TableInfo parseCreateTableSql(String sql) {
        TableInfo tableInfo = new TableInfo();

        // 提取表名
        Pattern tablePattern = Pattern.compile("CREATE TABLE `([^`]+)`", Pattern.CASE_INSENSITIVE);
        Matcher tableMatcher = tablePattern.matcher(sql);
        if (tableMatcher.find()) {
            String tableName = tableMatcher.group(1);
            tableInfo.setTableName(tableName);
            tableInfo.setClassName(toPascalCase(tableName.replace("t_", "").replace("aff_", "")));
        }

        // 提取表注释
        Pattern commentPattern = Pattern.compile("COMMENT='([^']+)'\\s*;?\\s*$", Pattern.CASE_INSENSITIVE);
        Matcher commentMatcher = commentPattern.matcher(sql);
        if (commentMatcher.find()) {
            tableInfo.setComment(commentMatcher.group(1));
        }

        // 按行分割SQL，逐行解析字段
        String[] lines = sql.split("\n");
        List<ColumnInfo> columns = new ArrayList<>();

        for (String line : lines) {
            line = line.trim();
            // 匹配字段定义行
            if (line.startsWith("`") && line.contains("COMMENT")) {
                // 更精确的字段解析
                Pattern fieldPattern = Pattern.compile("`([^`]+)`\\s+(\\w+)(?:\\([^)]+\\))?.*?COMMENT\\s+'([^']+)'", Pattern.CASE_INSENSITIVE);
                Matcher fieldMatcher = fieldPattern.matcher(line);

                if (fieldMatcher.find()) {
                    ColumnInfo column = new ColumnInfo();
                    column.setColumnName(fieldMatcher.group(1));
                    column.setColumnType(fieldMatcher.group(2));
                    column.setComment(fieldMatcher.group(3));
                    column.setFieldName(toCamelCase(column.getColumnName()));
                    column.setJavaType(mapToJavaType(column.getColumnType()));
                    columns.add(column);

                    // 调试输出
                    System.out.println("解析字段: " + column.getColumnName() + ", 类型: " + column.getColumnType() + ", 注释: " + column.getComment());
                }
            }
        }

        tableInfo.setColumns(columns);
        return tableInfo;
    }

    // 工具方法
    private static String generateEntityFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 为每个字段添加注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
            }

            // 为主键字段添加特殊注解
            if ("id".equals(column.getColumnName())) {
                sb.append("    @TableId(type = IdType.AUTO)\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateSaveModelFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 跳过自动生成的字段
            if ("id".equals(column.getColumnName()) || "create_time".equals(column.getColumnName()) || "update_time".equals(column.getColumnName())) {
                continue;
            }

            // 添加字段注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
                sb.append("    @Schema(description = \"").append(column.getComment()).append("\")\n");
            }

            // 为包含_id的字段添加非空校验（除了affiliate_id）
            if (column.getColumnName().contains("_id") && !"affiliate_id".equals(column.getColumnName())) {
                sb.append("    @NotNull(message = \"").append(column.getComment() != null ? column.getComment() : column.getFieldName()).append("不能为空\")\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateUpdateModelFields(List<ColumnInfo> columns) {
        return generateSaveModelFields(columns); // 更新模型字段与保存模型相同
    }

    private static String generatePageModelFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 跳过自动生成的字段
            if ("id".equals(column.getColumnName()) || "create_time".equals(column.getColumnName()) || "update_time".equals(column.getColumnName())) {
                continue;
            }

            // 添加字段注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
                sb.append("    @Schema(description = \"").append(column.getComment()).append("\")\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateRespModelFields(List<ColumnInfo> columns) {
        StringBuilder sb = new StringBuilder();
        for (ColumnInfo column : columns) {
            // 添加字段注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getComment()).append("\n");
                sb.append("     */\n");
                sb.append("    @Schema(description = \"").append(column.getComment()).append("\")\n");
            }

            sb.append("    private ").append(column.getJavaType()).append(" ").append(column.getFieldName()).append(";\n\n");
        }
        return sb.toString();
    }

    private static String generateBeforeCreateValidation(List<ColumnInfo> columns) {
        for (ColumnInfo column : columns) {
            if ("offer_id".equals(column.getColumnName())) {
                return "if (!OfferCheckUtils.isExist(saveModel.getOfferId())) {\n" +
                        "            CheckedException.doThrow(\"根据offerId【\" + saveModel.getOfferId() + \"】未找到对应的offer\");\n" +
                        "        }";
            }
        }
        return "// 可在此添加业务校验逻辑";
    }

    private static String generateBeforeUpdateValidation(List<ColumnInfo> columns) {
        for (ColumnInfo column : columns) {
            if ("offer_id".equals(column.getColumnName())) {
                return "if (!OfferCheckUtils.isExist(updateModel.getOfferId())) {\n" +
                        "            CheckedException.doThrow(\"根据offerId【\" + updateModel.getOfferId() + \"】未找到对应的offer\");\n" +
                        "        }";
            }
        }
        return "// 可在此添加业务校验逻辑";
    }

    private static String mapToJavaType(String sqlType) {
        return switch (sqlType.toLowerCase()) {
            case "bigint" -> "Long";
            case "int", "integer" -> "Integer";
            case "varchar", "text", "char" -> "String";
            case "decimal", "numeric" -> "BigDecimal";
            case "datetime", "timestamp" -> "LocalDateTime";
            case "date" -> "LocalDate";
            case "time" -> "LocalTime";
            case "tinyint" -> "Boolean";
            default -> "String";
        };
    }

    private static String toCamelCase(String str) {
        String[] parts = str.split("_");
        StringBuilder result = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            result.append(parts[i].substring(0, 1).toUpperCase()).append(parts[i].substring(1));
        }
        return result.toString();
    }

    private static String toPascalCase(String str) {
        String camelCase = toCamelCase(str);
        return camelCase.substring(0, 1).toUpperCase() + camelCase.substring(1);
    }

    private static String toLowerCamelCase(String str) {
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }

    private static String toKebabCase(String str) {
        return str.replaceAll("([a-z])([A-Z])", "$1-$2").toLowerCase();
    }

    private static void writeToFile(String filePath, String content) throws IOException {
        File file = new File(filePath);
        file.getParentFile().mkdirs();
        try (FileWriter writer = new FileWriter(file)) {
            writer.write(content);
        }
    }

    // 数据结构类
    static class TableInfo {
        private String tableName;
        private String className;
        private String comment;
        private List<ColumnInfo> columns;

        // getters and setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        public List<ColumnInfo> getColumns() { return columns; }
        public void setColumns(List<ColumnInfo> columns) { this.columns = columns; }
    }

    static class ColumnInfo {
        private String columnName;
        private String columnType;
        private String comment;
        private String fieldName;
        private String javaType;

        // getters and setters
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        public String getColumnType() { return columnType; }
        public void setColumnType(String columnType) { this.columnType = columnType; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }
        public String getJavaType() { return javaType; }
        public void setJavaType(String javaType) { this.javaType = javaType; }
    }
}