package com.siom.medivh.server.entity.strategy;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Appmetrica Offer弹夹链接替换表实体
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_tracker_clip")
public class TrackerClip {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联策略ID
     */
    @TableField("strategy_id")
    private Long strategyId;

    /**
     * 本地 Offer ID
     */
    @TableField("offer_id")
    private Long offerId;

    /**
     * 追踪链接
     */
    @TableField("tracker_url")
    private String trackerUrl;

    /**
     * 状态：unused-未使用，used-已使用
     */
    @TableField("status")
    private String status;

    /**
     * 使用时间
     */
    @TableField("used_time")
    private LocalDateTime usedTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}
