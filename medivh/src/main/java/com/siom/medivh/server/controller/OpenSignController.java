package com.siom.medivh.server.controller;


import com.siom.medivh.server.cert.sdk.enums.APIResultEnum;
import com.siom.medivh.server.cert.sdk.enums.SDKSignTypeEnum;
import com.siom.medivh.server.cert.sdk.service.*;
import com.siom.medivh.server.cert.sdk.service.pojo.SourcePositionProperty;
import com.siom.medivh.server.cert.sdk.utils.Base64;
import com.siom.medivh.server.cert.sdk.vo.base.Result;
import com.siom.medivh.server.cert.sdk.vo.base.SignLocation;
import com.siom.medivh.server.cert.sdk.vo.request.*;
import com.siom.medivh.server.cert.sdk.vo.response.CertEventResponse;
import com.siom.medivh.server.cert.sdk.vo.response.DocumentSignResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @Description: OpenSignController
 * @Package: org.resrun.controller
 * @ClassName: OpenSignController
 * @copyright 北京资源律动科技有限公司
 */
@Api(tags = "开放签-演示demo")
@RestController
@RequestMapping("/sign")
public class OpenSignController {

    @Autowired
    private SDKService sdkService;


    @ApiOperation("签署")
    @RequestMapping(value = "/doSign", method = RequestMethod.POST)
        public ResponseEntity<byte[]> doSign(@RequestPart SignRequest request, @RequestPart("file") MultipartFile file) {
        byte[] signFileBytes = null;
        byte[] entSealBytes = null;
        byte[] personalBytes = null;
       Result<CertEventResponse> entCert = null;
       Result<CertEventResponse> personalCert = null;
        //获取本地签署文件
        try {
            signFileBytes =  file.getBytes();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (signFileBytes == null) {
            throw new RuntimeException("签署失败");
        }
        //生成企业证书和个人证书
        try {
            if (request.getEntName() != null && request.getEntName().length() > 0) {
                CertEventRequest certEventRequest = new CertEventRequest();
                certEventRequest.setCertSubject("开放签@" + request.getEntName());
                certEventRequest.setCertPassword("123456");
                certEventRequest.setUniqueCode(UUID.randomUUID().toString());
                entCert = sdkService.certEvent(certEventRequest);
                if (entCert == null || !entCert.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
                    throw new RuntimeException("签署失败");
                }
            }
            if (request.getPersonalName() != null && request.getPersonalName().length() > 0) {
                CertEventRequest certEventRequest = new CertEventRequest();
                certEventRequest.setCertSubject("开放签@" + request.getPersonalName());
                certEventRequest.setCertPassword("123456");
                certEventRequest.setUniqueCode(UUID.randomUUID().toString());
                personalCert = sdkService.certEvent(certEventRequest);
                if (personalCert == null || !personalCert.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
                    throw new RuntimeException("签署失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //生成企业签章和个人签章
        if (request.getEntSeal() != null) {
            entSealBytes = Base64.decode(request.getEntSeal());
        }
        if (request.getPersonalSeal() != null) {
            personalBytes = Base64.decode(request.getPersonalSeal());
        }
        //进行签署操作
        byte[] operationByte = signFileBytes;
        //计算企业签署位置和个人签署位置
        DocumentSignRequest signRequest = new DocumentSignRequest();
        signRequest.setUniqueCode(UUID.randomUUID().toString());
        signRequest.setSignType(SDKSignTypeEnum.POSITION.getCode());
        if ((request.getEntPositionList() == null || request.getEntPositionList().size() == 0) &&
                (request.getPersonalPositionList() == null || request.getPersonalPositionList().size() == 0)) {
            throw new RuntimeException("签署失败");
        }
        //计算企业签署位置
        if (request.getEntPositionList() != null && request.getEntPositionList().size() > 0) {
            signRequest.setCertPassword(entCert.getData().getCertPassword());
            signRequest.setPfx(entCert.getData().getPfx());
            signRequest.setSignatureFile(Base64.encode(entSealBytes));
            signRequest.setDocumentFile(Base64.encode(operationByte));
            List<SignLocation> signLocations = new ArrayList<>();
            for (PositionRequest positionRequest : request.getEntPositionList()) {
                SignLocation signLocation = new SignLocation();
                signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                signLocation.setPage(positionRequest.getPage());
                signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                signLocations.add(signLocation);
            }
            signRequest.setSignLocationList(signLocations);
           Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
            if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
//                    String [] file = signResponse.getData().getDocumentFile().split(",");
                operationByte = signResponse.getData().getDocumentFile();
            } else {
                throw new RuntimeException("签署失败");
            }
        }
        //计算个人签署位置
        if (request.getPersonalPositionList() != null && request.getPersonalPositionList().size() > 0) {
            signRequest.setCertPassword(personalCert.getData().getCertPassword());
            signRequest.setPfx(personalCert.getData().getPfx());
            signRequest.setSignatureFile(Base64.encode(personalBytes));
            signRequest.setDocumentFile(Base64.encode(operationByte));
            List<SignLocation> signLocations = new ArrayList<>();
            for (PositionRequest positionRequest : request.getPersonalPositionList()) {
                SignLocation signLocation = new SignLocation();
                signLocation.setOffsetX(Float.valueOf(positionRequest.getOffsetX()));
                signLocation.setOffsetY(Float.valueOf(positionRequest.getOffsetY()));
                signLocation.setPage(positionRequest.getPage());
                signLocation.setPageHeight(Float.valueOf(positionRequest.getPageHeight()));
                signLocation.setPageWidth(Float.valueOf(positionRequest.getPageWidth()));
                signLocation.setSignHeight(Float.valueOf(positionRequest.getHeight()));
                signLocation.setSignWidth(Float.valueOf(positionRequest.getWidth()));
                signLocations.add(signLocation);
            }
            signRequest.setSignLocationList(signLocations);
          Result<DocumentSignResponse> signResponse = sdkService.documentSign(signRequest);
            if (signResponse.getCode().equals(APIResultEnum.SUCCESS.getCode())) {
                operationByte = signResponse.getData().getDocumentFile();
            } else {
                throw new RuntimeException("签署失败");
            }
        }
        // 关键部分：返回 PDF 文件作为下载
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDisposition(ContentDisposition.builder("attachment")
                .filename("签署完成.pdf", StandardCharsets.UTF_8)
                .build());
        String encode = Base64.encode(operationByte);
        System.out.println(encode);
        return new ResponseEntity<>(operationByte, headers, HttpStatus.OK);
    }



    public List<SourcePositionProperty> convert(List<PositionRequest> positionRequestList) {
        List<SourcePositionProperty> list = new ArrayList<>();
        for (PositionRequest request : positionRequestList) {
            SourcePositionProperty position = new SourcePositionProperty();
            position.setOffsetX(Float.valueOf(request.getOffsetX()));
            position.setOffsetY(Float.valueOf(request.getOffsetY()));
            position.setPage(request.getPage());
            position.setWidth(Float.valueOf(request.getWidth()));
            position.setHeight(Float.valueOf(request.getHeight()));
            position.setPageHeight(Float.valueOf(request.getPageHeight()));
            position.setPageWidth(Float.valueOf(request.getPageWidth()));
            list.add(position);
        }
        return list;
    }


    public byte[] getResourceFiles(String path) {
        try {
            InputStream inputStream = ResourceUtils.class.getClassLoader()
                    .getResourceAsStream(path);
            return read(inputStream);
        } catch (Exception e) {
            System.err.println(path);
            e.printStackTrace();
        }
        return null;
    }


    public byte[] read(InputStream inputStream) throws IOException {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int num = inputStream.read(buffer);
            while (num != -1) {
                baos.write(buffer, 0, num);
                num = inputStream.read(buffer);
            }
            baos.flush();
            return baos.toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

}