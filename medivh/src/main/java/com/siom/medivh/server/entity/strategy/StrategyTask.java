package com.siom.medivh.server.entity.strategy;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 策略任务主表实体
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_strategy_task")
public class StrategyTask {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略类型：如 APPMETRICA_CLIP
     */
    @TableField("strategy_type")
    private String strategyType;

    /**
     * 策略名称
     */
    @TableField("strategy_name")
    private String strategyName;

    /**
     * 本地 Offer ID
     */
    @TableField("offer_id")
    private Long offerId;

    /**
     * 第三方平台上的 Offer ID（如 Affise）
     */
    @TableField("advertiser_offer_id")
    private Long advertiserOfferId;

    /**
     * 定时执行表达式
     */
    @TableField("cron")
    private String cron;

    /**
     * 组id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 飞书通知人ID或Webhook
     */
    @TableField("notify_user")
    private String notifyUser;

    /**
     * 状态：ENABLED-启用，DISABLED-禁用
     */
    @TableField("status")
    private String status;

    /**
     * 最后执行时间
     */
    @TableField("last_execute_time")
    private LocalDateTime lastExecuteTime;

    /**
     * 下次执行时间
     */
    @TableField("next_execute_time")
    private LocalDateTime nextExecuteTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * Offer名称（非数据库字段）
     */
    @TableField(exist = false)
    private String offerName;
}
