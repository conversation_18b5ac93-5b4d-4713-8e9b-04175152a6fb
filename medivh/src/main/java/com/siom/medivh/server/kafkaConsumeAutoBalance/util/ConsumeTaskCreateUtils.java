package com.siom.medivh.server.kafkaConsumeAutoBalance.util;

import cn.hutool.extra.spring.SpringUtil;
import com.siom.medivh.server.kafkaConsumeAutoBalance.ConsumerTask;
import com.siom.medivh.server.kafkaConsumeAutoBalance.config.KafkaConfig;
import com.siom.medivh.server.utils.PoolExecutorInstance;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-18 17:14
 **/
@Slf4j
public class ConsumeTaskCreateUtils {
    private final Map<String, Set<ConsumerTask>> activeConsumers = new ConcurrentHashMap<>();

    private static final ExecutorService executor = PoolExecutorInstance.getThreadPoolExecutor();

    /**
     * 创建并执行任务
     */
    public static Future<?> createAndRunTask(String logo) {
        ConsumerTask consumerTask = new ConsumerTask(logo, SpringUtil.getBean(KafkaConfig.class));
        return executor.submit(consumerTask);
    }


}
