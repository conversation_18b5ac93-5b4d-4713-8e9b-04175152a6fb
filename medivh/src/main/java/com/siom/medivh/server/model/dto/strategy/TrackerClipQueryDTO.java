package com.siom.medivh.server.model.dto.strategy;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 追踪链接弹夹查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class TrackerClipQueryDTO {

    /**
     * 关联策略ID
     */
    private Long strategyId;

    /**
     * 本地 Offer ID
     */
    private Long offerId;

    /**
     * 追踪链接（模糊查询）
     */
    private String trackerUrl;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间开始
     */
    private LocalDateTime createdTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createdTimeEnd;

    /**
     * 使用时间开始
     */
    private LocalDateTime usedTimeStart;

    /**
     * 使用时间结束
     */
    private LocalDateTime usedTimeEnd;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
