package com.siom.medivh.server.config;

import com.obs.services.ObsClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class DataSourceConfig {

    @Bean
    //TODO 测试环境
    @ConfigurationProperties(prefix = "spring.datasource.test")
    //TODO 生产环境
    //@ConfigurationProperties(prefix = "spring.datasource.prop")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }


    @Value("${obs.accessKey:KGMV1GGCLWU5PZUYSONH}")
    private String accessKey;
    @Value("${obs.secretKey:NAfxlIBzuMNrQZXuR1m6ec4itolLwHJZ19jcV3gL}")
    private String secretKey;

    @Value("${obs.endPoint:https://obs.ap-southeast-1.myhuaweicloud.com}")
    private String endPoint;

    @Bean
    public ObsClient obsClient() {
        ObsClient obsClient = new ObsClient(accessKey, secretKey, endPoint);
        return obsClient;
    }
}
