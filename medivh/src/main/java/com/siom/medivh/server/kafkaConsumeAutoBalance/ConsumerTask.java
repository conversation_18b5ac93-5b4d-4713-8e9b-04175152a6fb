package com.siom.medivh.server.kafkaConsumeAutoBalance;

import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.siom.medivh.server.kafkaConsumeAutoBalance.config.KafkaConfig;
import com.siom.medivh.server.kafkaConsumeAutoBalance.util.GlobalConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.errors.WakeupException;

import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.Properties;
import java.util.UUID;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-18 16:56
 **/
@Data
@Slf4j
public class ConsumerTask implements Runnable {
    //需要监听的topic
    private String topic;
    //消费者组地址
    private String groupId;
    //服务器地址
    private String server;
    //消费者名称
    private String name;
    //是否运行的标识
    private volatile boolean running = true;
    /**
     * Kafka消费者实例，用于从Kafka集群消费消息。
     */
    private KafkaConsumer<String, String> consumer;
    private RateLimiter rateLimiter;

    public ConsumerTask(String logo, KafkaConfig kafkaConfig) {
        this.topic = String.format(GlobalConstants.TOPIC_GLOBAL, logo);
        this.groupId = String.format(GlobalConstants.GROUP_ID_GLOBAL, logo);
        this.server = kafkaConfig.getServer();
        //每个任务限制qps为10
        this.consumer = createConsumer();
        rateLimiter = RateLimiter.create(10);
        this.name = "Task-" + logo + "-" + UUID.randomUUID(); // 更唯一可控
    }


    @Override
    public void run() {
        //拉取数据
        while (this.running) {
            try {
                pullRecords();
                //偏移量提交
                this.consumer.commitAsync();
            } catch (Exception e) {
                log.error("consumer error", e);
            }
        }
    }

    /**
     * 数据拉取
     */
    private void pullRecords() {
        ConsumerRecords<String, String> records = this.consumer.poll(Duration.ofSeconds(3L));
        if (records.isEmpty()) {
            return;
        }
        for (ConsumerRecord<String, String> record : records) {
            //进行数据限流 保证平滑稳定
            rateLimiter.acquire();
            Object MsgIbj = JSONObject.parseObject(record.value(), Object.class);
            log.info("拉取到的数据:【{}】", MsgIbj);
            //TODO 进行消费
        }
    }

    /**
     * 创建消费者对象
     */
    private KafkaConsumer<String, String> createConsumer() {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", this.server);
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 50);
        properties.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 60000 * 10);
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        properties.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG, "org.apache.kafka.clients.consumer.RoundRobinAssignor");
        KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(properties);
        kafkaConsumer.subscribe(Collections.singletonList(this.topic), new ConsumerRebalanceListener() {
            @Override
            public void onPartitionsRevoked(Collection<TopicPartition> collection) {
                // 在分区撤销时提交当前偏移量，防止数据丢失
                log.info("Thread [{}] assigned partitions: {}", Thread.currentThread().getName(), collection);
                kafkaConsumer.commitSync();
            }

            @Override
            public void onPartitionsAssigned(Collection<TopicPartition> collection) {
                // 在分区分配时恢复上次提交的偏移量
                log.info("Thread [{}] assigned partitions: {}", Thread.currentThread().getName(), collection);
                for (TopicPartition partition : collection) {
                    long offset = getLastCommittedOffset(kafkaConsumer, partition);
                    kafkaConsumer.seek(partition, offset);
                }
            }
        });
        return kafkaConsumer;
    }

    // 模拟获取上次提交的偏移量
    private long getLastCommittedOffset(KafkaConsumer<String, String> kafkaConsumer, TopicPartition partition) {
        OffsetAndMetadata offsetAndMetadata = kafkaConsumer.committed(partition);
        return (offsetAndMetadata != null) ? offsetAndMetadata.offset() : 0;
    }
}
