package com.siom.medivh.server.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UrlMacroReplacer {

    private static final Pattern MACRO_PATTERN = Pattern.compile("\\{([^{}]+)}");

    public static String replaceMacros(String url, Map<String, String> values) {
        Matcher matcher = MACRO_PATTERN.matcher(url);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String key = matcher.group(1); // 拿到宏参数名，比如 clickid
            String replacement = values.getOrDefault(key, matcher.group(0)); // 如果map里没有，就保留原样
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    // 示例调用
    public static void main(String[] args) {
        String url = "https://example.com/v1/postback?clickid={clickid}&eventName={eventName}&subSiteId={af_sub_siteid}";
        Map<String, String> macros = new HashMap<String, String>();
        macros.put("clickid", "234342342");
        macros.put("eventName", "install");
        macros.put("af_sub_siteid", "123,3");


        String replacedUrl = replaceMacros(url, macros);
        System.out.println(replacedUrl);
    }
}
