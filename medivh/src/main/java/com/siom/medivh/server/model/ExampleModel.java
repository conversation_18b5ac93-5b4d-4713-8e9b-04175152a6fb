package com.siom.medivh.server.model;

import com.annotation.pattern.PatternForEnum;
import com.enums.FactoryObjectEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024-06-14 18:07
 * @PackageName:com.siom.deathwing.server.model
 * @ClassName: ExampleModel
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ExampleModel {

    @NotNull(message = "值不能为空")
    //@RangeAndAllowedValues(min = 1, max = 10, allowedValues = {1, 3, 4, 9}, message = "值必须在1到10之间，并且是1, 3, 4或9")
    private Integer value;
    @PatternForEnum(enumClass = FactoryObjectEnum.class,equalsValue = "status")
    private String v2;
}
