#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Word工具 - 支持多种转换方式
"""

import os
import sys
from pathlib import Path
import argparse

class PdfToWordConverter:
    def __init__(self):
        self.supported_methods = ['pdf2docx', 'pymupdf', 'pdfplumber']
    
    def convert_with_pdf2docx(self, pdf_path, output_path=None):
        """
        方法1: 使用pdf2docx库（推荐，保持格式最好）
        pip install pdf2docx
        """
        try:
            from pdf2docx import Converter
            
            if output_path is None:
                output_path = Path(pdf_path).with_suffix('.docx')
            
            print(f"🔄 使用pdf2docx转换: {pdf_path}")
            
            # 创建转换器
            cv = Converter(pdf_path)
            
            # 转换PDF到Word
            cv.convert(str(output_path))
            cv.close()
            
            print(f"✅ 转换成功: {output_path}")
            return True
            
        except ImportError:
            print("❌ 请先安装pdf2docx: pip install pdf2docx")
            return False
        except Exception as e:
            print(f"❌ pdf2docx转换失败: {e}")
            return False
    
    def convert_with_pymupdf(self, pdf_path, output_path=None):
        """
        方法2: 使用PyMuPDF (fitz)
        pip install PyMuPDF
        """
        try:
            import fitz  # PyMuPDF
            from docx import Document
            
            if output_path is None:
                output_path = Path(pdf_path).with_suffix('.docx')
            
            print(f"🔄 使用PyMuPDF转换: {pdf_path}")
            
            # 打开PDF文件
            pdf_document = fitz.open(pdf_path)
            
            # 创建Word文档
            doc = Document()
            
            # 逐页提取文本
            for page_num in range(pdf_document.page_count):
                page = pdf_document[page_num]
                text = page.get_text()
                
                # 添加页面标题
                if page_num > 0:
                    doc.add_page_break()
                
                doc.add_heading(f'第 {page_num + 1} 页', level=2)
                
                # 添加文本内容
                if text.strip():
                    doc.add_paragraph(text)
                else:
                    doc.add_paragraph("(此页无文本内容)")
            
            # 保存Word文档
            doc.save(output_path)
            pdf_document.close()
            
            print(f"✅ 转换成功: {output_path}")
            return True
            
        except ImportError:
            print("❌ 请先安装依赖: pip install PyMuPDF python-docx")
            return False
        except Exception as e:
            print(f"❌ PyMuPDF转换失败: {e}")
            return False
    
    def convert_with_pdfplumber(self, pdf_path, output_path=None):
        """
        方法3: 使用pdfplumber（适合表格处理）
        pip install pdfplumber python-docx
        """
        try:
            import pdfplumber
            from docx import Document
            
            if output_path is None:
                output_path = Path(pdf_path).with_suffix('.docx')
            
            print(f"🔄 使用pdfplumber转换: {pdf_path}")
            
            # 创建Word文档
            doc = Document()
            
            # 打开PDF文件
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    if page_num > 0:
                        doc.add_page_break()
                    
                    doc.add_heading(f'第 {page_num + 1} 页', level=2)
                    
                    # 提取文本
                    text = page.extract_text()
                    if text:
                        doc.add_paragraph(text)
                    
                    # 提取表格
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            doc.add_paragraph("表格:")
                            doc_table = doc.add_table(rows=len(table), cols=len(table[0]))
                            
                            for i, row in enumerate(table):
                                for j, cell in enumerate(row):
                                    if cell:
                                        doc_table.cell(i, j).text = str(cell)
            
            # 保存Word文档
            doc.save(output_path)
            
            print(f"✅ 转换成功: {output_path}")
            return True
            
        except ImportError:
            print("❌ 请先安装依赖: pip install pdfplumber python-docx")
            return False
        except Exception as e:
            print(f"❌ pdfplumber转换失败: {e}")
            return False
    
    def convert_batch(self, input_folder, output_folder=None, method='pdf2docx'):
        """
        批量转换PDF文件
        """
        input_path = Path(input_folder)
        if not input_path.exists():
            print(f"❌ 输入文件夹不存在: {input_folder}")
            return False
        
        if output_folder is None:
            output_folder = input_path / "converted_word"
        
        output_path = Path(output_folder)
        output_path.mkdir(exist_ok=True)
        
        # 查找所有PDF文件
        pdf_files = list(input_path.glob("*.pdf"))
        if not pdf_files:
            print(f"❌ 在 {input_folder} 中没有找到PDF文件")
            return False
        
        print(f"📁 找到 {len(pdf_files)} 个PDF文件")
        
        success_count = 0
        for pdf_file in pdf_files:
            output_file = output_path / f"{pdf_file.stem}.docx"
            
            print(f"\n🔄 转换: {pdf_file.name}")
            
            if self.convert_single(str(pdf_file), str(output_file), method):
                success_count += 1
            else:
                print(f"❌ 转换失败: {pdf_file.name}")
        
        print(f"\n📊 批量转换完成: {success_count}/{len(pdf_files)} 成功")
        return success_count > 0
    
    def convert_single(self, pdf_path, output_path=None, method='pdf2docx'):
        """
        转换单个PDF文件
        """
        if not Path(pdf_path).exists():
            print(f"❌ PDF文件不存在: {pdf_path}")
            return False
        
        if method == 'pdf2docx':
            return self.convert_with_pdf2docx(pdf_path, output_path)
        elif method == 'pymupdf':
            return self.convert_with_pymupdf(pdf_path, output_path)
        elif method == 'pdfplumber':
            return self.convert_with_pdfplumber(pdf_path, output_path)
        else:
            print(f"❌ 不支持的转换方法: {method}")
            print(f"支持的方法: {', '.join(self.supported_methods)}")
            return False
    
    def install_dependencies(self):
        """
        安装所需依赖
        """
        dependencies = [
            "pdf2docx",
            "PyMuPDF", 
            "pdfplumber",
            "python-docx"
        ]
        
        print("📦 安装依赖包...")
        for dep in dependencies:
            try:
                import subprocess
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ {dep} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {dep} 安装失败")

def main():
    parser = argparse.ArgumentParser(description='PDF转Word工具')
    parser.add_argument('input', help='输入PDF文件或文件夹路径')
    parser.add_argument('-o', '--output', help='输出文件或文件夹路径')
    parser.add_argument('-m', '--method', choices=['pdf2docx', 'pymupdf', 'pdfplumber'], 
                       default='pdf2docx', help='转换方法 (默认: pdf2docx)')
    parser.add_argument('--batch', action='store_true', help='批量转换模式')
    parser.add_argument('--install', action='store_true', help='安装依赖包')
    
    args = parser.parse_args()
    
    converter = PdfToWordConverter()
    
    if args.install:
        converter.install_dependencies()
        return
    
    if args.batch:
        success = converter.convert_batch(args.input, args.output, args.method)
    else:
        success = converter.convert_single(args.input, args.output, args.method)
    
    if success:
        print("\n🎉 转换完成!")
    else:
        print("\n💥 转换失败!")

if __name__ == "__main__":
    # 如果没有命令行参数，提供交互式界面
    if len(sys.argv) == 1:
        print("📄 PDF转Word工具")
        print("=" * 40)
        
        # 获取用户输入
        pdf_path = input("请输入PDF文件路径: ").strip().strip('"')
        
        if not pdf_path:
            print("❌ 请提供PDF文件路径")
            sys.exit(1)
        
        method = input("选择转换方法 (1:pdf2docx, 2:pymupdf, 3:pdfplumber) [1]: ").strip()
        method_map = {'1': 'pdf2docx', '2': 'pymupdf', '3': 'pdfplumber'}
        method = method_map.get(method, 'pdf2docx')
        
        converter = PdfToWordConverter()
        success = converter.convert_single(pdf_path, method=method)
        
        if success:
            print("\n🎉 转换完成!")
        else:
            print("\n💥 转换失败!")
    else:
        main()
