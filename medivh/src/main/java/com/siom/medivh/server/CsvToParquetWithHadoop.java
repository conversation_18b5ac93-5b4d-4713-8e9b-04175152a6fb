package com.siom.medivh.server;

import com.opencsv.CSVReader;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericRecord;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.parquet.avro.AvroParquetWriter;
import org.apache.parquet.hadoop.ParquetWriter;
import org.apache.parquet.hadoop.metadata.CompressionCodecName;

import java.io.FileReader;

public class CsvToParquetWithHadoop {
    /**
     <dependency>
     <groupId>org.apache.hadoop</groupId>
     <artifactId>hadoop-common</artifactId>
     <version>3.3.4</version>
     </dependency>
     <dependency>
     <groupId>org.apache.hadoop</groupId>
     <artifactId>hadoop-mapreduce-client-core</artifactId>
     <version>3.3.4</version>
     </dependency>
     <dependency>
     <groupId>org.apache.parquet</groupId>
     <artifactId>parquet-avro</artifactId>
     <version>1.13.1</version>
     </dependency>
     <dependency>
     <groupId>org.apache.avro</groupId>
     <artifactId>avro</artifactId>
     <version>1.11.1</version>
     </dependency>
     <dependency>
     <groupId>com.opencsv</groupId>
     <artifactId>opencsv</artifactId>
     <version>5.7.1</version>
     </dependency>
     */
    public static void main(String[] args) throws Exception {
        String csvFile = "D:\\100w.csv";
        String parquetFile = "D:\\output.snappy.parquet";

        String avroSchemaJson = "{\n" +
                "  \"type\": \"record\",\n" +
                "  \"name\": \"DeviceInfo\",\n" +
                "  \"fields\": [\n" +
                "    {\"name\": \"id\", \"type\": \"string\"},\n" +
                "    {\"name\": \"did_md5\", \"type\": \"string\"},\n" +
                "    {\"name\": \"gadvid\", \"type\": [\"null\", \"string\"], \"default\": null},\n" +
                "    {\"name\": \"idfa\", \"type\": [\"null\", \"string\"], \"default\": null},\n" +
                "    {\"name\": \"ip\", \"type\": \"string\"},\n" +
                "    {\"name\": \"ua\", \"type\": \"string\"},\n" +
                "    {\"name\": \"os\", \"type\": \"string\"},\n" +
                "    {\"name\": \"ds_adx\", \"type\": \"string\"}\n" +
                "  ]\n" +
                "}";

        Schema schema = new Schema.Parser().parse(avroSchemaJson);

        Configuration conf = new Configuration();

        try (
                CSVReader reader = new CSVReader(new FileReader(csvFile));
                ParquetWriter<GenericRecord> writer = AvroParquetWriter.<GenericRecord>builder(new Path(parquetFile))
                        .withSchema(schema)
                        .withConf(conf)
                        .withCompressionCodec(CompressionCodecName.SNAPPY)
                        .build()
        ) {
            reader.readNext(); // 跳过表头
            String[] row;
            while ((row = reader.readNext()) != null) {
                String did = row[0];
                String did_md5 = row[1];
                String did_type = row[2];
                String userAgent = row[3];
                String ip = row[4];
                String os = "unknown";
                if ("android".equalsIgnoreCase(did_type) || "oaid".equalsIgnoreCase(did_type)) {
                    os = "android";
                } else if ("ios".equalsIgnoreCase(did_type) || "idfa".equalsIgnoreCase(did_type)) {
                    os = "ios";
                }
                GenericRecord record = new GenericData.Record(schema);
                record.put("id", did);
                record.put("did_md5", did_md5);
                record.put("gadvid", os.equals("android") ? did : "");
                record.put("idfa", os.equals("ios") ? did : "");
                record.put("ip", ip);
                record.put("ua", userAgent);
                record.put("os", os);
                record.put("ds_adx", "t0");

                writer.write(record);
            }
        }

        System.out.println("✅ CSV 转 Snappy Parquet 完成！");
    }
}
