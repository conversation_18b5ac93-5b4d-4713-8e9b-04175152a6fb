package com.siom.medivh.server.entity.strategy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 策略字段表实体
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_strategy_filed")
public class StrategyField {

    @TableId("id")
    private Long id;

    /**
     * 字段名称
     */
    @TableField("filed_nama")
    private String fieldName;

    /**
     * 算法
     */
    @TableField("algorithm")
    private String algorithm;

    /**
     * 平台
     */
    @TableField("mmp")
    private String mmp;
}
