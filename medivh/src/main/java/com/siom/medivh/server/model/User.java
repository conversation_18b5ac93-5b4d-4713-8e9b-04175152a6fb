package com.siom.medivh.server.model;

import com.annotation.SensitiveEnum;
import com.enums.SensitiveType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024-06-18 14:36
 * @PackageName:com.siom.deathwing.server.model
 * @ClassName: User
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class User {

    private String username;
    private Integer typeCode;

    @SensitiveEnum(enumClass = SensitiveType.class, codeField = "typeCode", codeFieldForEnum = "code", valueFieldForEnum = "value")
    private String typeValue;
}
