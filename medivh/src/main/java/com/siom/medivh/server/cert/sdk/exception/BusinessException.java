package com.siom.medivh.server.cert.sdk.exception;


import com.siom.medivh.server.cert.sdk.enums.APIResultEnum;
import com.siom.medivh.server.cert.sdk.enums.BusinessErrorEnum;

/**
 * @Description: PaasException
 * @Package: org.resrun.exception
 * @ClassName: PaasException
 * @author: Feng<PERSON>ai_Gong
 */
public class BusinessException extends RuntimeException{

//    private static final long serialVersionUID = 1L;

    protected final transient APIResultEnum resultEnum ;

    protected final transient BusinessErrorEnum businessErrorEnum ;


    public BusinessException(APIResultEnum resultEnum, BusinessErrorEnum businessErrorEnum){
        this.resultEnum = resultEnum ;
        this.businessErrorEnum = businessErrorEnum;
    }





}