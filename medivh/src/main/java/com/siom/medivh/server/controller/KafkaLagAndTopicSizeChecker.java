package com.siom.medivh.server.controller;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-03 12:18
 **/

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.jboss.logging.Logger;

import java.time.Duration;
import java.util.*;
import java.util.logging.Level;

import java.util.stream.Collectors;

public class KafkaLagAndTopicSizeChecker {

    public static int count = 0;

    private static final String BOOTSTRAP_SERVERS = "************:9094,***************:9094,*************:9094";

    public static void main(String[] args) throws Exception {
        Logger.getLogger("org.apache.kafka").isEnabled(Logger.Level.ERROR);
        Properties adminProps = new Properties();
        adminProps.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        try (AdminClient adminClient = AdminClient.create(adminProps)) {

            // 获取所有 topic
            Set<String> topics = adminClient.listTopics()
                    .names()
                    .get();
            topics = topics.stream().filter(x -> x.equalsIgnoreCase("DC_RU_CLICK_STREAM")).collect(Collectors.toSet());

            Map<String, TopicDescription> topicDescriptions = adminClient.describeTopics(topics).all().get();

            // 创建一个临时 consumer 获取 end offset
            Properties consumerProps = new Properties();
            consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
            consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
            consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
            consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, "kafka-checker-" + UUID.randomUUID());
            consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");

            KafkaConsumer<String, String> consumer = new KafkaConsumer<>(consumerProps);
            // 获取每个分区的 endOffset
            Map<TopicPartition, Long> logEndOffsets = new HashMap<>();
            while (true) {
                for (TopicDescription desc : topicDescriptions.values()) {
                    for (org.apache.kafka.common.TopicPartitionInfo partitionInfo : desc.partitions()) {
                        TopicPartition tp = new TopicPartition(desc.name(), partitionInfo.partition());
                        logEndOffsets.put(tp, 0L); // 初始化
                    }
                }
                logEndOffsets.putAll(consumer.endOffsets(logEndOffsets.keySet(), Duration.ofSeconds(5)));
                // 输出每个 Topic 的总消息数
                System.out.println("==== Topic Total Message Counts ====");
                Map<String, Long> topicMessageCounts = new HashMap<>();
                for (Map.Entry<TopicPartition, Long> entry : logEndOffsets.entrySet()) {
                    topicMessageCounts.merge(entry.getKey().topic(), entry.getValue(), Long::sum);
                }
                topicMessageCounts.entrySet().stream()
                        .filter(x -> x.getValue() > 0)
                        .filter(x -> x.getKey().equalsIgnoreCase("DC_RU_CLICK_STREAM"))
                        .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                        .forEach(e -> {
                            System.out.printf("Topic: %-30s Messages: %10d  add: %10d\n", e.getKey(), e.getValue(), e.getValue() - count);
                            count = Math.toIntExact(e.getValue());
                        });
                //consumer.close();
                Thread.sleep(10000);
            }


            // 获取所有 consumer group
//            Collection<String> groupIds = adminClient.listConsumerGroups().all().get()
//                    .stream().map(ConsumerGroupListing::groupId).collect(Collectors.toList());
//
//            Map<String, Long> groupLags = new HashMap<>();

//            System.out.println("\n==== Consumer Group Lag Summary ====");
//            for (String groupId : groupIds) {
//                Map<TopicPartition, OffsetAndMetadata> groupOffsets;
//                try {
//                    groupOffsets = adminClient.listConsumerGroupOffsets(groupId).partitionsToOffsetAndMetadata().get();
//                } catch (Exception e) {
//                    continue;
//                }
//
//                long totalLag = 0;
//                for (Map.Entry<TopicPartition, OffsetAndMetadata> entry : groupOffsets.entrySet()) {
//                    TopicPartition tp = entry.getKey();
//                    long committedOffset = entry.getValue().offset();
//                    long logEndOffset = logEndOffsets.getOrDefault(tp, 0L);
//                    long lag = logEndOffset - committedOffset;
//                    totalLag += Math.max(lag, 0);
//                }
//
//                groupLags.put(groupId, totalLag);
//            }
//            groupLags.entrySet().stream()
//                    .filter(e -> e.getValue() > 0)
//                    .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
//                    .forEach(e -> System.out.printf("Group: %-30s Lag: %10d\n", e.getKey(), e.getValue()));

        }
    }
}




