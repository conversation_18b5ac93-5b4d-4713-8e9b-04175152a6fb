package com.siom.medivh.server.controller;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.TopicPartitionInfo;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * @program: atlas_oversea_micro_services
 * @description: 自定义分区选择器
 * @author: renBo
 * @create: 2025-06-18 11:23
 **/
public class KafkaLagUtils {

    public static Map<Integer, Long> getLagByPartition(String topic, String groupId, String bootstrapServers)
            throws ExecutionException, InterruptedException {
        Map<Integer, Long> lagMap = new HashMap<>();
        Properties adminProps = new Properties();
        adminProps.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        try (AdminClient adminClient = AdminClient.create(adminProps)) {
            DescribeTopicsResult describeTopics = adminClient.describeTopics(Collections.singleton(topic));
            Map<String, TopicDescription> topicDescriptionMap = describeTopics.all().get();
            List<TopicPartition> partitions = new ArrayList<>();
            for (TopicPartitionInfo info : topicDescriptionMap.get(topic).partitions()) {
                partitions.add(new TopicPartition(topic, info.partition()));
            }
            Map<TopicPartition, OffsetAndMetadata> committedOffsets =
                    adminClient.listConsumerGroupOffsets(groupId).partitionsToOffsetAndMetadata().get();
            Properties consumerProps = new Properties();
            consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, UUID.randomUUID().toString());
            consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
            try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(consumerProps)) {
                Map<TopicPartition, Long> endOffsets = consumer.endOffsets(partitions, Duration.ofSeconds(3));
                for (TopicPartition tp : partitions) {
                    long committed = committedOffsets.getOrDefault(tp, new OffsetAndMetadata(0L)).offset();
                    long end = endOffsets.getOrDefault(tp, 0L);
                    long lag = Math.max(end - committed, 0);
                    lagMap.put(tp.partition(), lag);
                }
            }
        }
        return lagMap;
    }
}
