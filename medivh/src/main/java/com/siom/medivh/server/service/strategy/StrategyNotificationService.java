package com.siom.medivh.server.service.strategy;

import com.siom.medivh.server.entity.strategy.StrategyTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 策略通知服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Service
public class StrategyNotificationService {

    @Autowired
    private StrategyExecuteLogService strategyExecuteLogService;

    private final WebClient webClient = WebClient.builder().build();

    /**
     * 发送成功通知
     */
    public void sendSuccessNotification(StrategyTask task, String message, String groupTraceId) {
        if (StrUtil.isEmpty(task.getNotifyUser())) {
            return;
        }

        try {
            String content = buildSuccessMessage(task, message, groupTraceId);
            sendFeishuNotification(task.getNotifyUser(), content, "success");
            log.info("成功通知已发送: {}", task.getStrategyName());
        } catch (Exception e) {
            log.error("发送成功通知失败: {}, 错误: {}", task.getStrategyName(), e.getMessage(), e);
        }
    }

    /**
     * 发送失败通知
     */
    public void sendFailureNotification(StrategyTask task, String message, String groupTraceId) {
        if (StrUtil.isEmpty(task.getNotifyUser())) {
            return;
        }

        try {
            String content = buildFailureMessage(task, message, groupTraceId);
            sendFeishuNotification(task.getNotifyUser(), content, "error");
            log.info("失败通知已发送: {}", task.getStrategyName());
        } catch (Exception e) {
            log.error("发送失败通知失败: {}, 错误: {}", task.getStrategyName(), e.getMessage(), e);
        }
    }

    /**
     * 定时发送未通知的失败日志
     */
    public void sendPendingFailureNotifications() {
        strategyExecuteLogService.getFailedLogsNotNotified()
                .forEach(log -> {
                    try {
                        // 这里可以根据策略ID获取策略信息，然后发送通知
                        String content = String.format(
                                "策略执行失败通知\n" +
                                "策略ID: %d\n" +
                                "策略类型: %s\n" +
                                "执行时间: %s\n" +
                                "错误信息: %s",
                                log.getStrategyId(),
                                log.getStrategyType(),
                                log.getExecuteTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                                log.getMessage()
                        );

                        // 这里需要根据策略ID获取通知用户信息
                        // sendFeishuNotification(notifyUser, content, "error");
                        
                        // 标记通知已发送
                        strategyExecuteLogService.markNotifySent(log.getId());
                        
                    } catch (Exception e) {
                        log.error("发送待处理失败通知异常: {}", e.getMessage(), e);
                    }
                });
    }

    /**
     * 发送飞书通知
     */
    private void sendFeishuNotification(String webhook, String content, String type) {
        try {
            Map<String, Object> message = new HashMap<>();
            message.put("msg_type", "text");
            
            Map<String, String> textContent = new HashMap<>();
            textContent.put("text", content);
            message.put("content", textContent);

            String requestBody = JSONUtil.toJsonStr(message);

            String response = webClient.post()
                    .uri(webhook)
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            log.info("飞书通知发送成功, 响应: {}", response);

        } catch (Exception e) {
            log.error("发送飞书通知失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 构建成功消息
     */
    private String buildSuccessMessage(StrategyTask task, String message, String groupTraceId) {
        return String.format(
                "✅ 策略执行成功\n" +
                "策略名称: %s\n" +
                "策略类型: %s\n" +
                "Offer ID: %d\n" +
                "执行时间: %s\n" +
                "执行结果: %s\n" +
                "追踪ID: %s",
                task.getStrategyName(),
                task.getStrategyType(),
                task.getOfferId(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                message,
                groupTraceId
        );
    }

    /**
     * 构建失败消息
     */
    private String buildFailureMessage(StrategyTask task, String message, String groupTraceId) {
        return String.format(
                "❌ 策略执行失败\n" +
                "策略名称: %s\n" +
                "策略类型: %s\n" +
                "Offer ID: %d\n" +
                "执行时间: %s\n" +
                "失败原因: %s\n" +
                "追踪ID: %s",
                task.getStrategyName(),
                task.getStrategyType(),
                task.getOfferId(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                message,
                groupTraceId
        );
    }
}
