package com.siom.medivh.server.utils;

import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.exceptions.PdfException;

import java.io.*;

public class PdfEncryptUtil {

    public static byte[] encryptPdf(byte[] originalPdf, String userPassword, String ownerPassword) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        PdfReader reader = new PdfReader(new ByteArrayInputStream(originalPdf));
        WriterProperties props = new WriterProperties()
                .setStandardEncryption(
                        userPassword.getBytes(),
                        ownerPassword.getBytes(),
                        EncryptionConstants.ALLOW_PRINTING,
                        EncryptionConstants.ENCRYPTION_AES_128
                );

        PdfWriter writer = new PdfWriter(baos, props);
        PdfDocument pdfDoc = new PdfDocument(reader, writer);
        pdfDoc.close();

        return baos.toByteArray();
    }

    public static byte[] encryptPdf(byte[] originalPdf, String ownerPassword) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        PdfReader reader = new PdfReader(new ByteArrayInputStream(originalPdf));
        WriterProperties props = new WriterProperties()
                .setStandardEncryption(
                        null, // 用户密码为 null，打开 PDF 不需要密码
                        ownerPassword.getBytes(), // 所有者密码，用于控制权限
                        EncryptionConstants.ALLOW_PRINTING, // 允许打印，其它禁止（包括修改）
                        EncryptionConstants.ENCRYPTION_AES_128
                );

        PdfWriter writer = new PdfWriter(baos, props);
        PdfDocument pdfDoc = new PdfDocument(reader, writer);
        pdfDoc.close();

        return baos.toByteArray();
    }

}
