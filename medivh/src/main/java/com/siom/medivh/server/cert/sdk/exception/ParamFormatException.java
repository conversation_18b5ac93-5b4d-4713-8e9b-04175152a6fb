package com.siom.medivh.server.cert.sdk.exception;


import com.siom.medivh.server.cert.sdk.enums.APIResultEnum;
import com.siom.medivh.server.cert.sdk.enums.ParamFormatErrorEnum;

/**
 * @Description: PaasException
 * @Package: org.resrun.exception
 * @ClassName: PaasException
 * @author: <PERSON><PERSON><PERSON>_<PERSON>
 */
public class ParamFormatException extends RuntimeException{

    protected final transient APIResultEnum resultEnum ;

    protected final transient ParamFormatErrorEnum paramFormatErrorEnum ;


    public ParamFormatException(APIResultEnum resultEnum, ParamFormatErrorEnum paramFormatErrorEnum){
        this.resultEnum = resultEnum ;
        this.paramFormatErrorEnum = paramFormatErrorEnum;
    }





}