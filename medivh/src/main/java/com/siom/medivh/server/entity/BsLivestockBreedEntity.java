package com.siom.medivh.server.entity;/*
 *    Copyright (c) 2018-2025, xiaomuding All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: xiaomuding
 */



import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 良繁配种记录
 *
 * <AUTHOR>
 * @date 2024-06-26 16:26:17
 */
@Data
@TableName("bs_livestock_breed")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "良繁配种记录")
public class BsLivestockBreedEntity extends Model<BsLivestockBreedEntity> {

    private static final long serialVersionUID = 1L;


	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 蓄只id
	*/
    @Schema(description="蓄只id")
    private Long livestockId;

	/**
	* 蓄只类型
	*/
    @Schema(description="蓄只类型")
    private Integer livestockType;

	/**
	* 所在牧场
	*/
    @Schema(description="所在牧场")
    private Long farmId;

	/**
	* 配种日期
	*/
    @Schema(description="配种日期")
    private String breedDate;

	/**
	* 配种方式 1：人工 2：本交
	*/
    @Schema(description="配种方式 1：人工 2：本交")
    private Integer breedType;

	/**
	* 配种员
	*/
    @Schema(description="配种员")
    private Long breedUserId;

	/**
	* 配种员
	*/
    @Schema(description="配种员")
    private String breedUserName;

	/**
	* 配种员
	*/
    @Schema(description="配种员")
    private String breedUserPhone;

	/**
	* 配种员签名
	*/
    @Schema(description="配种员签名")
    private String breedUserSign;

	/**
	* 冻精Id
	*/
    @Schema(description="冻精Id")
    private Long frozenSemenId;

	/**
	* 配种现场照片 多张逗号隔开
	*/
    @Schema(description="配种现场照片 多张逗号隔开")
    private String breedPhotoList;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人名称
	*/
    @Schema(description="创建人名称")
    private String createUserName;

	/**
	* 创建人id
	*/
    @Schema(description="创建人id")
    private Long createUserId;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 更新人
	*/
    @Schema(description="更新人")
    private String updateUserName;

	/**
	* 更新人id
	*/
    @Schema(description="更新人id")
    private Long updateUserId;

	/**
	* 逻辑删除标记(0:未删除，1:已删除)
	*/
    @Schema(description="逻辑删除标记(0:未删除，1:已删除)")
    private Integer deleted;

}