package com.siom.medivh.server.controller;

import org.apache.kafka.common.PartitionInfo;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-06-23 20:11
 **/
public class Demo3 {

    public static void main(String[] args) throws ExecutionException, InterruptedException {
        Map<Integer, Long> dcGbClickStream = KafkaLagUtils.getLagByPartition("DC_GB_CLICK_STREAM", "prod-group-gb", "************:9094,***************:9094,*************:9094");
        System.out.println(dcGbClickStream);
        List<Integer> ls = new ArrayList<>();
        for (int i = 0; i < 16; i++) {
            ls.add(i);
        }
        List<Integer> topPartitions = ls.stream()
                .sorted(Comparator.comparingLong(p -> dcGbClickStream.getOrDefault(p, Long.MAX_VALUE)))
                .limit(5)
                .collect(Collectors.toList());
        System.out.println(topPartitions);
    }

    public static void main2(String[] args) {
        int targetConsumerCount = 64 / 10;
        System.out.println(targetConsumerCount);
    }
}
