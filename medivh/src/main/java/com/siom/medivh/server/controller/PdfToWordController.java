package com.siom.medivh.server.controller;

import com.siom.medivh.server.utils.PdfToWordConverter;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * PDF转Word Web接口
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping("/api/pdf-converter")
public class PdfToWordController {

    private static final String UPLOAD_DIR = "uploads/";
    private static final String OUTPUT_DIR = "outputs/";

    /**
     * 上传PDF文件并转换为Word
     */
    @PostMapping("/upload-and-convert")
    public ResponseEntity<Map<String, Object>> uploadAndConvert(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择文件");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (!file.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
                result.put("success", false);
                result.put("message", "只支持PDF文件");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 创建目录
            Files.createDirectories(Paths.get(UPLOAD_DIR));
            Files.createDirectories(Paths.get(OUTPUT_DIR));
            
            // 保存上传的文件
            String originalFilename = file.getOriginalFilename();
            String pdfFileName = System.currentTimeMillis() + "_" + originalFilename;
            Path pdfPath = Paths.get(UPLOAD_DIR + pdfFileName);
            file.transferTo(pdfPath.toFile());
            
            // 生成输出文件名
            String wordFileName = pdfFileName.replaceAll("\\.pdf$", ".docx");
            String wordFilePath = OUTPUT_DIR + wordFileName;
            
            // 转换PDF到Word
            boolean success = PdfToWordConverter.convertPdfToWord(pdfPath.toString(), wordFilePath);
            
            if (success) {
                result.put("success", true);
                result.put("message", "转换成功");
                result.put("originalFile", originalFilename);
                result.put("convertedFile", wordFileName);
                result.put("downloadUrl", "/api/pdf-converter/download/" + wordFileName);
                
                // 文件信息
                result.put("originalSize", file.getSize());
                result.put("convertedSize", Files.size(Paths.get(wordFilePath)));
            } else {
                result.put("success", false);
                result.put("message", "转换失败");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            result.put("success", false);
            result.put("message", "文件处理失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 下载转换后的Word文件
     */
    @GetMapping("/download/{filename}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String filename) {
        try {
            Path filePath = Paths.get(OUTPUT_DIR + filename);
            
            if (!Files.exists(filePath)) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(filePath.toFile());
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 通过文件路径转换（本地文件）
     */
    @PostMapping("/convert-local")
    public ResponseEntity<Map<String, Object>> convertLocalFile(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String pdfPath = request.get("pdfPath");
            String wordPath = request.get("wordPath");
            
            if (pdfPath == null || pdfPath.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "请提供PDF文件路径");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 检查文件是否存在
            if (!Files.exists(Paths.get(pdfPath))) {
                result.put("success", false);
                result.put("message", "PDF文件不存在: " + pdfPath);
                return ResponseEntity.badRequest().body(result);
            }
            
            // 转换文件
            boolean success = PdfToWordConverter.convertPdfToWord(pdfPath, wordPath);
            
            if (success) {
                result.put("success", true);
                result.put("message", "转换成功");
                result.put("outputPath", wordPath != null ? wordPath : pdfPath.replaceAll("\\.pdf$", ".docx"));
            } else {
                result.put("success", false);
                result.put("message", "转换失败");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 批量转换本地文件夹
     */
    @PostMapping("/convert-batch")
    public ResponseEntity<Map<String, Object>> convertBatch(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String inputFolder = request.get("inputFolder");
            String outputFolder = request.get("outputFolder");
            
            if (inputFolder == null || inputFolder.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "请提供输入文件夹路径");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 执行批量转换
            PdfToWordConverter.convertBatch(inputFolder, outputFolder);
            
            result.put("success", true);
            result.put("message", "批量转换完成");
            result.put("inputFolder", inputFolder);
            result.put("outputFolder", outputFolder);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量转换失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/file-info")
    public ResponseEntity<Map<String, Object>> getFileInfo(@RequestParam String filePath) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Path path = Paths.get(filePath);
            
            if (!Files.exists(path)) {
                result.put("success", false);
                result.put("message", "文件不存在");
                return ResponseEntity.badRequest().body(result);
            }
            
            result.put("success", true);
            result.put("fileName", path.getFileName().toString());
            result.put("fileSize", Files.size(path));
            result.put("filePath", path.toAbsolutePath().toString());
            result.put("lastModified", Files.getLastModifiedTime(path).toString());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取文件信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 清理临时文件
     */
    @DeleteMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanup() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 清理上传文件夹
            cleanupDirectory(UPLOAD_DIR);
            
            // 清理输出文件夹
            cleanupDirectory(OUTPUT_DIR);
            
            result.put("success", true);
            result.put("message", "清理完成");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "清理失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 清理目录中的文件
     */
    private void cleanupDirectory(String directory) throws IOException {
        Path dir = Paths.get(directory);
        if (Files.exists(dir)) {
            Files.walk(dir)
                    .filter(Files::isRegularFile)
                    .forEach(file -> {
                        try {
                            Files.delete(file);
                        } catch (IOException e) {
                            System.err.println("删除文件失败: " + file);
                        }
                    });
        }
    }
}
