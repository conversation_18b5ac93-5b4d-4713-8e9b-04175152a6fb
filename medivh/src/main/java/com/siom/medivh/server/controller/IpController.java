package com.siom.medivh.server.controller;

import com.bo.R;
import com.utils.IpUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-12-11 9:25
 * @ClassName: AppAreaInfoSettingServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@RestController
@RequestMapping("/ip")
public class IpController {


    @GetMapping("/t1")
    public R t1(@RequestParam("ip") String ip){
        return R.ok(IpUtils.getGeoLocation(ip));
    }
}
