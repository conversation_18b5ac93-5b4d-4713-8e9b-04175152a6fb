package com.siom.medivh.server.service.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siom.medivh.server.entity.strategy.StrategyTask;
import com.siom.medivh.server.entity.strategy.StrategyConfigGroup;
import com.siom.medivh.server.mapper.strategy.StrategyTaskMapper;
import com.siom.medivh.server.mapper.strategy.StrategyConfigGroupMapper;
import com.siom.medivh.server.model.dto.strategy.StrategyTaskQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 策略任务服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class StrategyTaskService extends ServiceImpl<StrategyTaskMapper, StrategyTask> {

    @Autowired
    private StrategyConfigGroupMapper strategyConfigGroupMapper;

    /**
     * 分页查询策略任务
     */
    public IPage<StrategyTask> pageQuery(StrategyTaskQueryDTO queryDTO) {
        return this.page(new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize()),
                new LambdaQueryWrapper<StrategyTask>()
                        .eq(StrUtil.isNotEmpty(queryDTO.getStrategyType()), StrategyTask::getStrategyType, queryDTO.getStrategyType())
                        .like(StrUtil.isNotEmpty(queryDTO.getStrategyName()), StrategyTask::getStrategyName, queryDTO.getStrategyName())
                        .eq(queryDTO.getOfferId() != null, StrategyTask::getOfferId, queryDTO.getOfferId())
                        .eq(queryDTO.getAdvertiserOfferId() != null, StrategyTask::getAdvertiserOfferId, queryDTO.getAdvertiserOfferId())
                        .eq(StrUtil.isNotEmpty(queryDTO.getGroupId()), StrategyTask::getGroupId, queryDTO.getGroupId())
                        .eq(StrUtil.isNotEmpty(queryDTO.getStatus()), StrategyTask::getStatus, queryDTO.getStatus())
                        .ge(queryDTO.getCreatedTimeStart() != null, StrategyTask::getCreatedTime, queryDTO.getCreatedTimeStart())
                        .le(queryDTO.getCreatedTimeEnd() != null, StrategyTask::getCreatedTime, queryDTO.getCreatedTimeEnd())
                        .ge(queryDTO.getLastExecuteTimeStart() != null, StrategyTask::getLastExecuteTime, queryDTO.getLastExecuteTimeStart())
                        .le(queryDTO.getLastExecuteTimeEnd() != null, StrategyTask::getLastExecuteTime, queryDTO.getLastExecuteTimeEnd())
                        .orderByDesc(StrategyTask::getCreatedTime));
    }

    /**
     * 查询需要执行的任务
     */
    public List<StrategyTask> getTasksToExecute() {
        return baseMapper.selectTasksToExecute(LocalDateTime.now());
    }

    /**
     * 更新任务执行时间
     */
    public boolean updateExecuteTime(Long id, LocalDateTime lastExecuteTime, LocalDateTime nextExecuteTime) {
        return baseMapper.updateExecuteTime(id, lastExecuteTime, nextExecuteTime) > 0;
    }

    /**
     * 根据组ID查询任务
     */
    public List<StrategyTask> getByGroupId(String groupId) {
        return this.list(new LambdaQueryWrapper<StrategyTask>()
                .eq(StrategyTask::getGroupId, groupId));
    }

    /**
     * 启用/禁用任务
     */
    public boolean updateStatus(Long id, String status) {
        return Optional.ofNullable(this.getById(id))
                .map(task -> {
                    task.setStatus(status);
                    return this.updateById(task);
                })
                .orElse(false);
    }

    /**
     * 根据组维度查询信息
     */
    public List<StrategyConfigGroup> pageByGroup(String groupId) {
        return strategyConfigGroupMapper.selectList(new LambdaQueryWrapper<StrategyConfigGroup>()
                        .eq(StrUtil.isNotEmpty(groupId), StrategyConfigGroup::getId, groupId))
                .stream()
                .peek(group -> {
                    // 查询当前组下面所有的任务
                    List<StrategyTask> tasks = this.list(new LambdaQueryWrapper<StrategyTask>()
                            .eq(StrategyTask::getGroupId, group.getId()));
                    group.setStrategyTasks(tasks);
                })
                .collect(Collectors.toList());
    }
}
