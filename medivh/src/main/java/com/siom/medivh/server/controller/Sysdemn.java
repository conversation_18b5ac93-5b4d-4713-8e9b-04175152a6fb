package com.siom.medivh.server.controller;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;

/**
 * @program: simon
 * @description: TODO
 * @author: renBo
 * @create: 2025-04-10 17:00
 **/
public class Sysdemn {

    public static void main(String[] args) throws InterruptedException {
        SystemInfo si = new SystemInfo();
        CentralProcessor processor = si.getHardware().getProcessor();
        GlobalMemory memory = si.getHardware().getMemory();

        // CPU 占用率（平均值）
        double cpuLoad = processor.getSystemCpuLoadBetweenTicks( processor.getSystemCpuLoadTicks()) * 100;
        System.out.printf("CPU占用率：%.2f%%\n", cpuLoad);

        // 内存使用
        long totalMem = memory.getTotal();
        long availableMem = memory.getAvailable();
        long usedMem = totalMem - availableMem;

        System.out.printf("总内存：%.2f GB\n", totalMem / 1024.0 / 1024 / 1024);
        System.out.printf("已用内存：%.2f GB\n", usedMem / 1024.0 / 1024 / 1024);
        System.out.printf("可用内存：%.2f GB\n", availableMem / 1024.0 / 1024 / 1024);
    }
}
