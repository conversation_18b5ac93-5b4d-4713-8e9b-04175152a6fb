<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siom.medivh.server.mapper.BsLivestockBreedMapper">


    <insert id="insertList">
        INSERT INTO bs_livestock_breed (id, create_user_name,create_user_id,update_user_name,update_user_id,create_time,update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.createUserName}, #{item.createUserId}, #{item.updateUserName}, #{item.updateUserId}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>