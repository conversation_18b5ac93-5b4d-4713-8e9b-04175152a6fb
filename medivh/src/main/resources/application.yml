server:
  port: 8858
spring:
  application:
    name: @artifactId@
  datasource:
    test:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: ${MYSQL_USER:root}
      password: ${MYSQL_PWD:Wmy@testMysql20230512}
      jdbcUrl: jdbc:mysql://${MYSQL_HOST:xiaomuding-mysql}:${MYSQL_PORT:3306}/${MYSQL_DB:pasture5}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&allowMultiQueries=true&allowPublicKeyRetrieval=true
    prop:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: ${MYSQL_USER:xrrcloud_root}
      password: ${MYSQL_PWD:A$Y2#3swJE4#%K50oG}
      jdbcUrl: jdbc:mysql://${MYSQL_HOST:prd-xiaomuding.rwlb.rds.aliyuncs.com}:${MYSQL_PORT:3690}/${MYSQL_DB:pasture5}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&allowMultiQueries=true&allowPublicKeyRetrieval=true
  redis:
    enable: false
    host: **************
    port: 6379
    password: <EMAIL>
    database: 6
  servlet:
    multipart:
      max-file-size: 5012MB
      max-request-size: 5012MB
kaifangqian:
  #证书服务地址
  cert-apply-url: https://service.kaifangqian.com/service/cert/event
  #授权token
  token: 123456
  # 默认 false 签发本地测试证书 true 签发 CA 证书
  prod: false
# Logger Config
logging:
  level:
   org.apache.kafka: OFF
   kafka: OFF
   com.siom.medivh.server.mapper: error
