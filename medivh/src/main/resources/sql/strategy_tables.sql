-- 策略任务主表
CREATE TABLE `t_strategy_task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `strategy_type` varchar(64) NOT NULL COMMENT '策略类型：如 APPMETRICA_CLIP',
  `strategy_name` varchar(255) NOT NULL COMMENT '策略名称',
  `offer_id` bigint NOT NULL COMMENT '本地 Offer ID',
  `advertiser_offer_id` bigint NOT NULL COMMENT '第三方平台上的 Offer ID（如 Affise）',
  `cron` varchar(64) DEFAULT '0 0 2 * * ?' COMMENT '定时执行表达式',
  `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组id',
  `notify_user` varchar(255) DEFAULT NULL COMMENT '飞书通知人ID或Webhook',
  `status` enum('ENABLED','DISABLED') DEFAULT 'ENABLED',
  `last_execute_time` datetime DEFAULT NULL,
  `next_execute_time` datetime DEFAULT NULL,
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_strategy_type` (`strategy_type`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_status` (`status`),
  KEY `idx_next_execute_time` (`next_execute_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1942508689351159810 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略任务主表';

-- Appmetrica Offer弹夹链接替换表
CREATE TABLE `t_tracker_clip` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `strategy_id` bigint NOT NULL COMMENT '关联策略ID',
  `offer_id` bigint NOT NULL COMMENT '本地 Offer ID',
  `tracker_url` varchar(1024) NOT NULL,
  `status` enum('unused','used') DEFAULT 'unused',
  `used_time` datetime DEFAULT NULL,
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_offer_id` (`offer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_strategy_status` (`strategy_id`, `status`)
) ENGINE=InnoDB AUTO_INCREMENT=1942511373504413698 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='追踪链接弹夹表';

-- 调度任务执行记录表
CREATE TABLE `t_strategy_execute_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `strategy_id` bigint NOT NULL COMMENT '策略主键',
  `strategy_type` varchar(64) DEFAULT NULL COMMENT '策略类型，例如 APPSFLYER_PARAM_ROTATE',
  `offer_id` bigint DEFAULT NULL COMMENT '本地 offer ID',
  `advertiser_offer_id` bigint DEFAULT NULL COMMENT 'Affise offer ID',
  `group_trace_id` varchar(64) DEFAULT NULL COMMENT '同组策略统一执行标识',
  `execute_time` datetime NOT NULL COMMENT '实际执行时间',
  `success` tinyint(1) NOT NULL COMMENT '执行是否成功',
  `message` text COMMENT '错误信息或状态说明',
  `request_payload` text COMMENT '替换请求结构体，JSON 格式',
  `response_payload` text COMMENT 'API 响应体，JSON 格式',
  `notify_sent` tinyint(1) DEFAULT '0' COMMENT '是否已发送通知',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_execute_time` (`execute_time`),
  KEY `idx_success` (`success`),
  KEY `idx_group_trace_id` (`group_trace_id`),
  KEY `idx_notify_sent` (`notify_sent`)
) ENGINE=InnoDB AUTO_INCREMENT=1942521839400202243 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略执行日志表';

-- 参数配置表 此id关联主表t_strategy_task的group_id
CREATE TABLE `t_strategy_config_group` (
  `id` bigint NOT NULL,
  `status` varchar(16) DEFAULT NULL COMMENT '是否启用',
  `description` varchar(255) DEFAULT NULL COMMENT '说明',
  `rule_list` text COMMENT '规则  key-value ',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略配置组表';

-- 策略字段表
CREATE TABLE `t_strategy_filed` (
  `id` bigint NOT NULL,
  `filed_nama` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `algorithm` varchar(255) DEFAULT NULL COMMENT '算法',
  `mmp` varchar(255) DEFAULT NULL COMMENT '平台',
  PRIMARY KEY (`id`),
  KEY `idx_algorithm` (`algorithm`),
  KEY `idx_mmp` (`mmp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略字段表';

-- 插入示例数据
INSERT INTO `t_strategy_config_group` (`id`, `status`, `description`, `rule_list`) VALUES
(1, 'ENABLED', 'Appmetrica弹夹替换配置组', '{"rotation_interval": "1h", "max_clips": 100}'),
(2, 'ENABLED', 'AppsFlyer参数轮换配置组', '{"param_rotation": "daily", "backup_params": true}');

INSERT INTO `t_strategy_filed` (`id`, `filed_nama`, `algorithm`, `mmp`) VALUES
(1, 'click_id', 'UUID', 'APPMETRICA'),
(2, 'install_time', 'TIMESTAMP', 'APPMETRICA'),
(3, 'af_click_id', 'HASH', 'APPSFLYER'),
(4, 'af_install_time', 'TIMESTAMP', 'APPSFLYER');

-- 示例策略任务
INSERT INTO `t_strategy_task` (`strategy_type`, `strategy_name`, `offer_id`, `advertiser_offer_id`, `cron`, `group_id`, `notify_user`, `status`) VALUES
('APPMETRICA_CLIP', 'Appmetrica弹夹替换-测试', 1001, 2001, '0 0 */2 * * ?', '1', 'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-here', 'ENABLED'),
('APPSFLYER_PARAM_ROTATE', 'AppsFlyer参数轮换-测试', 1002, 2002, '0 0 1 * * ?', '2', 'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-here', 'ENABLED');

-- 示例弹夹数据
INSERT INTO `t_tracker_clip` (`strategy_id`, `offer_id`, `tracker_url`, `status`) VALUES
(1, 1001, 'https://appmetrica.yandex.com/serve/click?id=test1', 'unused'),
(1, 1001, 'https://appmetrica.yandex.com/serve/click?id=test2', 'unused'),
(1, 1001, 'https://appmetrica.yandex.com/serve/click?id=test3', 'unused');
