package com.customize.core.enmus.common;

import lombok.Getter;

/**
 * 牲畜类别 0:其他 1：羊 2：猪 3：牛 4：驴 5：鸡
 */
@Getter
public enum LivestockTypeEnum {

    OTHER_TYPE(0,"其他",""),
    SHEEP_TYPE(1,"羊","羔"),
    PIG_TYPE(2,"猪","仔"),
    CATTLE_TYPE(3,"牛","犊"),
    DONKEY_TYPE(4,"驴","犊"),
    CHICKEN_TYPE(5,"鸡","仔"),
    ;
    private int code;
    private String msg;
    private String desc;

    /**
     * Msg string.
     *
     * @return the string
     */
    public String msg() {
        return msg;
    }

    /**
     * Code int.
     *
     * @return the int
     */
    public int code() {
        return code;
    }
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getDesc() {
        return desc;
    }


    
    LivestockTypeEnum(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }

    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static LivestockTypeEnum getEnum(int code) {
        for (LivestockTypeEnum ele : LivestockTypeEnum.values()) {
            if (ele.code() == code) {
                return ele;
            }
        }
        return null;
    }
    
    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static String getType(String code) {
        for (LivestockTypeEnum ele : LivestockTypeEnum.values()) {
            if (code.equals(ele.code()+"")) {
                return ele.msg;
            }
        }
        return null;
    }

}
