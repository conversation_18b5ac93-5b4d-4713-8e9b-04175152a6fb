package com.customize.core.mqtt.config;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

import com.customize.core.mqtt.handler.MqttReceiveHandler;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 实现了对 inboundtopic 中的主题监听，当有消息推送到 inboundtopic 主题上时可以接受
 * MQTT 消费端
 * Create By Spring-2023/07/09
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@IntegrationComponentScan("com.customize.core.mqtt")
@ConditionalOnProperty(name = "mqtt.use", havingValue = "true")
@Import({MqttConfiguration.class, MqttOutboundConfiguration.class})
public class MqttInboundConfiguration {

    @Resource
    private MqttConfiguration mqttProperties;

    @Value("${spring.application.name}")
    private String applicationName;

    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }


    @Bean
    public MqttPahoClientFactory mqttInClient() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        String[] mqttServerUrls = mqttProperties.getUrl().split(StrUtil.COMMA);
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(mqttServerUrls);
        options.setUserName(mqttProperties.getUsername());
        options.setPassword(mqttProperties.getPassword().toCharArray());
        options.setKeepAliveInterval(mqttProperties.getKeepalive());
        //接受离线消息
        options.setCleanSession(false);
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * 配置Client，监听Topic
     * 如果我要配置多个client，应该怎么处理呢？这个也简单, 模仿此方法，多写几个client
     *
     * @return
     */
    @Bean
    public MessageProducer inbound() {
        String[] inboundTopics = mqttProperties.getReceiveDefaultTopics().split(StrUtil.COMMA);
        String random = UUID.randomUUID().toString();
        String clientId = mqttProperties.getClientId() + StrUtil.UNDERLINE + applicationName + StrUtil.UNDERLINE + random + "_inbound";
        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(clientId, mqttInClient(), inboundTopics);
        List<String> list = SpringUtil.getBeansOfType(MqttReceiveHandler.class).values().stream().map(MqttReceiveHandler::addTopic).collect(Collectors.toList());
        String[] addTopics = list.toArray(new String[list.size()]);
        // 添加 TOPICS
        adapter.addTopic(addTopics);
        adapter.setCompletionTimeout(1000 * 5);
        adapter.setQos(mqttProperties.getQos());
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setOutputChannel(mqttInputChannel());

        return adapter;
    }

    /**
     * 通过通道获取数据,即处理 MQTT 发送过来的消息，可以通过 MQTTX 工具发送数据测试
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public MessageHandler handler() {
        return message -> SpringUtil.getBeansOfType(MqttReceiveHandler.class).forEach((k, v) -> v.handle(message));
    }


//    //===========================
//    @Bean
//    public MessageChannel mqttOneInputChannel() {
//        return new DirectChannel();
//    }
//
//    @Bean
//    public MqttPahoClientFactory mqttOneInClient() {
//        return mqttInClient();
//    }
//
//    /**
//     * 配置Client，监听Topic
//     * 如果我要配置多个client，应该怎么处理呢？这个也简单, 模仿此方法，多写几个client
//     *
//     * @return
//     */
//    @Bean
//    public MessageProducer oneInbound() {
//        String[] inboundTopics = mqttProperties.getReceiveDefaultTopics().split(StrUtil.COMMA);
//        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(mqttProperties.getClientId() + "_inbound",
//                mqttInClient(), inboundTopics);
//        Map<Integer, List<MqttReceiveHandler>> collect = SpringUtil.getBeansOfType(MqttReceiveHandler.class).values().stream().collect(Collectors.groupingBy(MqttReceiveHandler::getQos));
//        List<MqttReceiveHandler> mqttReceiveHandlers = collect.get(ManaConstant.ONE);
//        String[] addTopics = mqttReceiveHandlers.toArray(new String[mqttReceiveHandlers.size()]);
//        // 添加 TOPICS
//        adapter.addTopic(addTopics);
//        adapter.setCompletionTimeout(1000 * 5);
//        adapter.setQos(ManaConstant.ONE);
//        adapter.setConverter(new DefaultPahoMessageConverter());
//        adapter.setOutputChannel(mqttOneInputChannel());
//
//        return adapter;
//    }
//
//
//    /**
//     * 通过通道获取数据,即处理 MQTT 发送过来的消息，可以通过 MQTTX 工具发送数据测试
//     */
//    @Bean
//    @ServiceActivator(inputChannel = "mqttOneInputChannel")
//    public MessageHandler handlerOne() {
//        return message -> SpringUtil.getBeansOfType(MqttReceiveHandler.class).forEach((k, v) -> v.handle(message));
//    }


}
