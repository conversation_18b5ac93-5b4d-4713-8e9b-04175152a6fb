package com.customize.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName: PreventAttackingProperties
 * @Description: TODO
 * @date 2023-04-21 14:20
 */
@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "xmd.core.attacking")
public class PreventAttackingConfig {

	/**
	 * 状态 true 打开 否则关闭
	 */
	private Boolean status;
	/**
	 * 单位时间限制通过请求数
	 */
	private Integer limit;
	/**
	 * 单位时间，单位秒
	 */
	private Integer time;
	/**
	 * 最大访问次数
	 */
	private Integer max;
	/**
	 * 达到限流提示语
	 */
	private String message;
}
