package com.customize.core.entity;


import com.core.util.WebUtils;
import com.customize.core.annotation.OperateLog;
import com.customize.core.interceptor.AccessLimitInterceptor;
import com.customize.core.params.BaseParams;

import com.customize.core.enmus.StatusEnums;
import com.customize.core.utils.IpUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @ClassName: LogEntity
 * @Description: 系统日志
 * @date 2023-04-25 10:32
 */
@Data
@Slf4j
public class OperateLogEntity extends BaseParams {
    private String title;
    private String businessType;
    private String status;
    private String requestData;
    private String requestUrl;
    private String requestIp;
    private String requestMethod;
    private String requestClassName;
    private String requestParams;
    private String requestBody;
    private String responseData;
    private String errorMsg;

    /**
     * 初始化日日志
     *
     * @param logAnno
     * @param pjp
     * @return
     */
    public static OperateLogEntity init(OperateLog logAnno, ProceedingJoinPoint pjp) {
        OperateLogEntity logEntity = new OperateLogEntity();
        try {
            HttpServletRequest request = WebUtils.getRequest();
            // 请求的IP地址
            logEntity.setRequestIp(IpUtils.getIpAddr(request));
            //请求url
            logEntity.setRequestUrl(request.getRequestURI());
            // 设置方法名称
            logEntity.setRequestMethod(request.getMethod());
            //获取请求参数
            logEntity.setRequestParams(request.getQueryString());
            //获取请求方法体
            logEntity.setRequestBody(AccessLimitInterceptor.getPostData(request));
            //标题
            logEntity.setTitle(logAnno.title());
            //操作类型
            logEntity.setBusinessType(logAnno.businessType().toString());
            //状态 默认0成功
            logEntity.setStatus(StatusEnums.SUCCESS.toString());
            logEntity.setRequestClassName(pjp.getTarget().getClass().getName());
        } catch (Exception e) {
            log.error("日志初始化失败--{}", e.getMessage());
            logEntity.setErrorMsg("日志初始化失败");
        }
        return logEntity;
    }
}
