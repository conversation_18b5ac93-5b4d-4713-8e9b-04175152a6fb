package com.customize.core.enmus.request;

/**
 * <AUTHOR> admin
 * @date : 2023/5/23
 * @description : 短信验证码的类型
 */
public enum SmsCodeTypeEnum {
	/**
	 * 1：登录验证码
	 */
	LOGIN_SYSTEM_SMS_CODE("login_system_sms_code","短信-登录验证码","1"),
	/**
	 * 2：修改密码 
	 */
	UPDATE_PASSWORD_SMS_CODE("update_password_sms_code","短信-修改用户密码","2"),
	/**
	 * 3：注册账号
	 */
	REGISTER_ACCOUNT_SMS_CODE("register_account_sms_code","短信-注册账号","3"),
	/**
	 * 4.见犊补母验证码
	 */
	JDBM_REGISTER_SMS_CODE("jdbm_register_sms_code","短信-见犊补母验证码","4"),

	/**
	 * 5.金融贷款申请验证码
	 */
	FINANCE_APPLY_ORDER_SMS_CODE("finance_apply_order_sms_code","短信-金融贷款申请验证码","5"),

	;
	
	/**
	 *  短信验证码类型
	 */
	private String codeType;
	/**
	 * 短信验证码的描述
	 */
	private String codeDesc;
	/**
	 * 对应的原始值
	 */
	private String originalValue;
	
	public String getCodeType() {
		return codeType;
	}
	
	public void setCodeType(String codeType) {
		this.codeType = codeType;
	}
	
	public String getCodeDesc() {
		return codeDesc;
	}
	
	public void setCodeDesc(String codeDesc) {
		this.codeDesc = codeDesc;
	}
	
	public String getOriginalValue() {
		return originalValue;
	}
	
	public void setOriginalValue(String originalValue) {
		this.originalValue = originalValue;
	}
	
	SmsCodeTypeEnum(String codeType, String codeDesc, String originalValue) {
		this.codeType = codeType;
		this.codeDesc = codeDesc;
		this.originalValue = originalValue;
	}
	
	/**
	 * Gets enum.
	 * @param codeType the code
	 * @return the enum
	 */
	public static SmsCodeTypeEnum getSmsCodeByCode(String codeType) {
		for (SmsCodeTypeEnum typeEnum : SmsCodeTypeEnum.values()) {
			if (codeType.equalsIgnoreCase(typeEnum.getCodeType())) {
				return typeEnum;
			}
		}
		return null;
	}
	
	/**
	 * Gets enum.
	 * @param originalValue the code
	 * @return the enum
	 */
	public static SmsCodeTypeEnum getSmsCodeByValue(String originalValue) {
		for (SmsCodeTypeEnum typeEnum : SmsCodeTypeEnum.values()) {
			if (originalValue.equalsIgnoreCase(typeEnum.getOriginalValue())) {
				return typeEnum;
			}
		}
		return null;
	}
}
