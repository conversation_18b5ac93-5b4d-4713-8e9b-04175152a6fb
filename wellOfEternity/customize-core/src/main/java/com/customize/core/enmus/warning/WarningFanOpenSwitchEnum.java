package com.customize.core.enmus.warning;

import java.util.Objects;

/**
 * 摄像头告警类型
 * <AUTHOR>
 */
public enum WarningFanOpenSwitchEnum {

	/**
	 * 打开
	 */
	OPEN_SWITCH(0,"打开"),
	/**
	 * 关闭
	 */
	CLOSE_SWITCH(1,"关闭"),

	;

    private Integer value;
    private String descr;

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	WarningFanOpenSwitchEnum(Integer value, String descr) {
		this.value = value;
		this.descr = descr;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WarningFanOpenSwitchEnum getInstance(String value){
		for (WarningFanOpenSwitchEnum typeEnum : WarningFanOpenSwitchEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
				return typeEnum;
			}
		}
		return null;
	}
	
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WarningFanOpenSwitchEnum camera = getInstance(value);
		if (Objects.nonNull(camera)){
			return camera.getDescr();
		}
		return "";
	}
}
