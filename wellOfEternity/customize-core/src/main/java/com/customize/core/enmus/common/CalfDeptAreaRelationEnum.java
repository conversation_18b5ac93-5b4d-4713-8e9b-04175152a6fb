package com.customize.core.enmus.common;

/**
 * 硬件设备名称
 * <AUTHOR>
 */
public enum CalfDeptAreaRelationEnum {
	
	/**
	 * 榆阳区
	 */
	YUYANGQU_DEPT_AREA("810010","42223","榆阳区"),
	/**
	 * 洛南县
	 */
	LUONANXIAN_DEPT_AREA("810011","42598","洛南县"),
	
	;

    private String deptId;
	private String areaId;
    private String desc;
	
	public String getDeptId() {
		return deptId;
	}
	
	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}
	
	public String getAreaId() {
		return areaId;
	}
	
	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}
	
	public String getDesc() {
		return desc;
	}
	
	public void setDesc(String desc) {
		this.desc = desc;
	}
	
	CalfDeptAreaRelationEnum(String deptId, String areaId, String desc) {
		this.deptId = deptId;
		this.areaId = areaId;
		this.desc = desc;
	}
	
	/**
	 * Gets enum.
	 * @param deptId the code
	 * @return the enum
	 */
	public static String getAreaId(String deptId) {
		for (CalfDeptAreaRelationEnum ele : CalfDeptAreaRelationEnum.values()) {
			if (ele.getDeptId().equals(deptId)) {
				return ele.getAreaId();
			}
		}
		return null;
	}
	
	
	/**
	 * Gets enum.
	 * @param deptId the code
	 * @return the enum
	 */
	public static CalfDeptAreaRelationEnum getAreaInfo(String deptId) {
		for (CalfDeptAreaRelationEnum ele : CalfDeptAreaRelationEnum.values()) {
			if (ele.getDeptId().equals(deptId)) {
				return ele;
			}
		}
		return null;
	}
	
	
}
