package com.customize.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023-07-06 16:32
 * @PackageName:com.xmd.xiaomuding.customize.core.entity
 * @ClassName: LogExceptionEntity
 * @Description: TODO
 * @Version 1.0
 */
@Data
@Slf4j
@TableName("log_exception")
public class LogExceptionEntity {
    private Long id;
    private String requestMethodName;
    private String requestUrl;
    private String requestHeader;
    private String requestParam;
    private String requestBody;
    private String errorMsg;
    private String errorContent;
    private LocalDateTime createTime;
    private Integer disposeStatus;
    private LocalDateTime disposeTime;
    private Long disposeBy;
    private String disposeName;
    private String disposeDesc;
}
