package com.customize.core.config;

import com.customize.core.enmus.request.RequestEnvTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description :
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@RefreshScope
@Component
@ConfigurationProperties(prefix = "current")
public class CurrentInfoConfig {
	/**
	 * 当前环境,参考:com.xmd.xiaomuding.customize.core.enmus.request.RequestEnvTypeEnum 的值
	 */
	private String environment = RequestEnvTypeEnum.TEST_ENVIRONMENT.getRequestEnv();
	/**
	 * 默认密码
	 */
	private String defaultPwd = "666666";
	/**
	 * 接口 oauth2 访问地址       
	 */
	private String defaultUrl = "http://xiaomuding.com:60000/auth/oauth2/token?grant_type=";
	/**
	 * 短信验证码失效哦时间
	 */
	private int defaultSmsExpire = 5;
	/**
	 * 云辉耳标
	 */
	private int yunHuiEarTime;
	/**
	 * 中农智联耳标
	 */
	private int zhongNongZhiLianEarTime;
	/**
	 * 规则之外的用户
	 */
	private String excludeUsers;
	
	
}
