package com.customize.core.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName: AutoIdempotentConfig
 * @Description: TODO
 * @date 2023-04-21 11:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@RefreshScope
@Component
@ConfigurationProperties(prefix = "xmd.core.idempotent")
public class AutoIdempotentConfig {

	private Boolean enabled;
	private String idempotentType;


}
