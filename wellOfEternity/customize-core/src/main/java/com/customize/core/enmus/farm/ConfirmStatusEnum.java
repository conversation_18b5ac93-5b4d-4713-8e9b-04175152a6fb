package com.customize.core.enmus.farm;

/**
 * <AUTHOR> admin
 * @date : 2023/6/14
 * @description :
 */
public enum ConfirmStatusEnum {
	
	
	/**
	 * 1.待确认,2.已确认; 3.已驳回
	 */
	FARM_CONFIRM_WAIT(1, "待确认"),
	
	FARM_CONFIRM_OK(2, "已确认"),
	
	FARM_CONFIRM_REJECTED(3, "已驳回"),
	;
	
	/**
	 *  值
	 */
	private Integer value;
	/**
	 * 描述
	 */
	private String valDesc;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getValDesc() {
		return valDesc;
	}
	
	public void setValDesc(String valDesc) {
		this.valDesc = valDesc;
	}
	
	ConfirmStatusEnum(Integer value, String valDesc) {
		this.value = value;
		this.valDesc = valDesc;
	}
	
	/**
	 * Gets enum.
	 * @param value the code
	 * @return the enum
	 */
	public static ConfirmStatusEnum getFarmType(Integer value) {
		for (ConfirmStatusEnum typeEnum : ConfirmStatusEnum.values()) {
			if (value.intValue() == typeEnum.getValue().intValue()) {
				return typeEnum;
			}
		}
		return null;
	}
	
}
