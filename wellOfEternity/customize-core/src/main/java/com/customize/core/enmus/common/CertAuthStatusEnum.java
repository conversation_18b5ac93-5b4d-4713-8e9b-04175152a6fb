package com.customize.core.enmus.common;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @Description 证件认证状态（同时应用于是否成为管理员枚举）
* @Return
**/
@AllArgsConstructor
@Getter
public enum CertAuthStatusEnum {

    //审核状态(0:未审核;1:审核中;2:审核成功;3:审核失败)
    NO_REAL_NAME_AUTH(-1,"未认证"),
    NOT_REAL_NAME_AUTH(0,"未审核"),
    NON_REAL_NAME_AUTH(1,"审核中"),
    REAL_NAME_AUTH(2,"审核成功"),
    AUDITING(3,"审核失败"),
    ;

    private Integer value;
    private String description;

    public static CertAuthStatusEnum getValueOf(Integer value){
        for (CertAuthStatusEnum val: CertAuthStatusEnum.values()) {
            if(value.equals(val.getValue())){
                return val;
            }
        }
        return null;
    }
    
    public static String getDesc(String value){
        for (CertAuthStatusEnum val: CertAuthStatusEnum.values()) {
            if(value.equals(val.getValue()+"")){
                return val.getDescription();
            }
        }
        return null;
    }
}