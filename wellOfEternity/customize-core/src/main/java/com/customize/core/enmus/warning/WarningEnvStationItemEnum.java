package com.customize.core.enmus.warning;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * 告警类型
 * <AUTHOR>
 */
public enum WarningEnvStationItemEnum {
	//'数据类别：1：温度 2：湿度 3：风速 4：二氧化碳 5：氨气 6：硫化氢'，7：光照 
	/**
	 * 温度
	 */
	TEMPERATURE_WARNING("1","温度","℃","https://file.vmuyun.com/image/cover/AB6935972F664FDB9AAA62A3FC345467-6-2.png?width=100&height=100"),
	/**
	 * 湿度
	 */
	HUMIDITY_WARNING("2","湿度","%","https://file.vmuyun.com/image/cover/699625FA98F74138948628C2C0F624D4-6-2.png?width=100&height=100"),
	/**
	 * 硫化氢
	 */
	HYDROGEN_SULFIDE_WARNING("3","硫化氢","ppm","https://file.vmuyun.com/image/cover/7F9172654AC5495C9DCFE8301DBF74F4-6-2.png?width=100&height=100"),
	/**
	 * 氨气
	 */
	AMMONIA_WARNING("4","氨气","ppm","https://file.vmuyun.com/image/cover/91F5A41F72BC49A2835B3ED1E32F1CEA-6-2.png?width=100&height=100"),
	/**
	 * 光照
	 */
	ILLUMINATION_WARNING("5","光照","Lux","https://file.vmuyun.com/image/cover/CF1DF21381294B70A319712A41AC49FA-6-2.png?width=100&height=100"),
	/**
	 * 二氧化碳
	 */
	CARBON_DIOXIDE_WARNING("6","二氧化碳","ppm","https://file.vmuyun.com/image/cover/0BAF48FA0D274A4383D7ADDA5963F251-6-2.png?width=88&height=88"),
	/**
	 * 含氧量
	 */
	OXYGEN_CONTENT_WARNING("7","含氧量","ppm","https://file.vmuyun.com/image/cover/75A7699F8F374FB9A4B91C0DFDAF798D-6-2.png?width=54&height=54"),
	/**
	 * 风速
	 */
	WIND_SPEED_WARNING("8","风速","m/s","https://file.vmuyun.com/image/cover/6A965ACF4B0F47E1B15B5AC70D3BB305-6-2.png?width=54&height=54"),
	/**
	 * 降雨量
	 */
	RAINFALL_WARNING("9","降雨量","mm",""),
	/**
	 * 大气压力
	 */
	ATMOSPHERIC_PRESSURE_WARNING("10","大气压力","hPa",""),
	/**
	 * 风向
	 */
	WIND_DIRECTION_WARNING("11","风向","°",""),

	/**
	 * 太阳辐射
	 */
	SOLAR_RADIATION_WARNING("12","太阳辐射","W/m2"),
	;
	
	/**
	 * 类型
	 */
    private String value;
	/**
	 * 名称描述
	 */
    private String descr;
	/**
	 * 单位
	 */
	private String unit;
	/**
	 * 单位
	 */
	private String image;
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescr() {
		return descr;
	}
	
	public void setDescr(String descr) {
		this.descr = descr;
	}
	
	public String getUnit() {
		return unit;
	}
	
	public void setUnit(String unit) {
		this.unit = unit;
	}
	
	public String getImage() {
		return image;
	}
	
	public void setImage(String image) {
		this.image = image;
	}
	
	WarningEnvStationItemEnum(String value, String descr, String unit) {
		this.value = value;
		this.descr = descr;
		this.unit = unit;
	}
	
	WarningEnvStationItemEnum(String value, String descr, String unit, String image) {
		this.value = value;
		this.descr = descr;
		this.unit = unit;
		this.image = image;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WarningEnvStationItemEnum getInstance(String value){
		for (WarningEnvStationItemEnum typeEnum : WarningEnvStationItemEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue())) {
				return typeEnum;
			}
		}
		return null;
	}
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WarningEnvStationItemEnum envItemEnum = getInstance(value);
		if (Objects.nonNull(envItemEnum)){
			return envItemEnum.getDescr();
		}
		return "";
	}


	@Data
	@Schema(description = "告警类型")
	public static class WarningEnvStationItem{
		public WarningEnvStationItem(WarningEnvStationItemEnum envStationItemEnum){
			this.keyCode=envStationItemEnum.value;
			this.keyName=envStationItemEnum.descr;
			this.keyUnit=envStationItemEnum.unit;
			this.keyImage= envStationItemEnum.image;
		}
		@Schema(description="关键字、词：温度")
		private String keyName;
		@Schema(description="关键词code：temperature")
		private String keyCode;
		@Schema(description="单位")
		private String keyUnit;
		@Schema(description="图片")
		private String keyImage;
	}
}
