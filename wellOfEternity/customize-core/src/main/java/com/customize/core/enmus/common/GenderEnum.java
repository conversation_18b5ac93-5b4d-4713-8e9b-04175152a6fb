package com.customize.core.enmus.common;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum GenderEnum {
    NOT_SET(0,"未填写"),
    MALE(1,"男"),
    FEMALE(2,"女"),
    ;

    private Integer value;
    private String description;
    public static Integer getValueOf(String description){
        Map<String,Integer> resMap =new HashMap<String,Integer>();
        for (GenderEnum val : GenderEnum.values()) {
            resMap.put(val.getDescription(),val.value);
        }
        return resMap.get(description);
    }
}
