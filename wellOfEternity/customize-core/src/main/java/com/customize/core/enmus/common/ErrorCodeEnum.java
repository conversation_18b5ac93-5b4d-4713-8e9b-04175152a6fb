package com.customize.core.enmus.common;

public enum ErrorCodeEnum {
    /**
     * Gl 99990100 error code enum.
     */
    GL99990100(9999100, "参数异常"),
    /**
     * Gl 99990401 error code enum.
     */
    GL99990401(99990401, "无访问权限"),
    /**
     * Gl 000500 error code enum.
     */
    GL99990500(500, "未知异常"),
    /**
     * Gl 000403 error code enum.
     */
    GL99990403(9999403, "无权访问"),
    /**
     * Gl 000404 error code enum.
     */
    GL9999404(9999404, "找不到指定资源"),
    /**
     * Gl 99990001 error code enum.
     */
    GL99990001(99990001, "注解使用错误"),
    /**
     * Gl 99990002 error code enum.
     */
    GL99990002(99990002, "微服务不在线,或者网络超时"),
    /**
     * PB 10010001 error code enum.
     */
    PB10010001(10010001, "此设备已存在"),
    /**
     * PB 10010002 error code enum.
     */
    PB10010002(10010002, "设备验证码不能为空"),
    /**
     * PB 10010003 error code enum.
     */
    PB10010003(10010003, "请选择正确的牲畜类别"),
    /**
     * PB 10010004 error code enum.
     */
    PB10010004(10010004, "您已经添加此设备"),
    /**
     * PB 10010005 error code enum.
     */
    PB10010005(10010005, "此设备属于其他用户，您无法添加。"),
    /**
     * PB 10010006 error code enum.
     */
    PB10010006(10010006, "开通设备直播服务失败，请稍后再试！"),
    /**
     * PB 10010007 error code enum.
     */
    PB10010007(10010007, "绑定失败，请检查设备连接是否正常"),
    /**
     * PB 10010008 error code enum.
     */
    PB10010008(10010008, "创建萤石子账户出错"),
    /**
     * PB 10010009 error code enum.
     */
    PB10010009(10010009, "未找到可用设备"),
    /**
     * PB 10010010 error code enum.
     */
    PB10010010(10010010, "设备序列号不能为空"),
    /**
     * PB 10010011 error code enum.
     */
    PB10010011(10010011, "已登记！请勿重复登记"),
    /**
     * PB 10010012 error code enum.
     */
    PB10010012(10010012, "暂时没有新版本信息"),
    /**
     * PB 10010013 error code enum.
     */
    PB10010013(10010013, "账号不存在"),
    /**
     * PB 10010014 error code enum.
     */
    PB10010014(10010014, "密码错误"),
    /**
     * PB 10010015 error code enum.
     */
    PB10010015(10010015, "账号状态异常，禁止登录"),
    /**
     * PB 10010016 error code enum.
     */
    PB10010016(10010016, "验证码错误"),
    /**
     * PB 10010017 error code enum.
     */
    PB10010017(10010017, "验证码未过期"),
    /**
     * PB 10010018 error code enum.
     */
    PB10010018(10010018, "验证码发送失败"),
    /**
     * PB 10010019 error code enum.
     */
    PB10010019(10010019, "账号已存在"),
    /**
     * PB 10010020 error code enum.
     */
    PB10010020(10010020, "一键登录失败"),
    /**
     * PB 10010021 error code enum.
     */
    PB10010021(10010021, "部门已存在"),
    /**
     * PB 10010022 error code enum.
     */
    PB10010022(10010022, "角色已存在"),
    /**
     * PB 10010023 error code enum.
     */
    PB10010023(10010023, "请输入正确的断奶数量"),
    /**
     * PB 10010024 error code enum.
     */
    PB10010024(10010024, "存在下级部门，不能删除"),
    /**
     * PB 10010025 error code enum.
     */
    PB10010025(10010025,"应用已使用!!不能删除!" ),
    /**
     * PB 10010026 error code enum.
     */
    PB10010026(10010026, "角色已使用!!不能删除!"),
    /**
     * PB 10010027 error code enum.
     */
    PB10010027(10010027, "删除失败！！请联系管理员！"),
    /**
     * PB 10010028 error code enum.
     */
    PB10010028(10010028, "存在下级菜单，不能删除"),
    /**
     * PB 10010029 error code enum.
     */
    PB10010029(10010029, "该设备序列号无效"),
    /**
     * PB 10010030 error code enum.
     */
    PB10010030(10010030, "该用户已开通项目"),
    /**
     * PB 10010031 error code enum.
     */
    PB10010031(10010031, "RFID设备连接异常"),
    /**
     * PB 10010032 error code enum.
     */
    PB10010032(10010032, "AI盘点异常，盘点结束"),
    /**
     * PB 10010033 error code enum.
     */
    PB10010033(10010033, "断奶数量不能大于健仔和弱仔总和"),
    /**
     * PB 10010034 error code enum.
     */
    PB10010034(10010034, "耳标已存在"),
    /**
     * PB 10010035 error code enum.
     */
    PB10010035(10010035, "设备不在线或网络异常"),
    /**
     * PB 10010035 error code enum.
     */
    PB10010036(10010036, "您的账号已被冻结，如有疑问请拨打0912-3533387"),

	/**
	 * PB PB10010037 error code enum.
	 */
	PB10010037(10010037, "设备类型不能为空"),

	/**
	 * PB PB10010038 error code enum.
	 */
	PB10010038(10010038, "此设备不存在"),


	/**
	 * PB 10010039 error code enum.
	 */
	PB10010039(10010039, "设备已被其它账户绑定，请解绑后再试"),


	/**
	 * PB 10010040 error code enum.
	 */
	PB10010040(10010040, "设备已绑定在其它的项目，请解绑后再试"),

	/**
	 * PB 10010041 error code enum.
	 */
	PB10010041(10010041, "设备已开启"),
	/**
	 * PB 10010042 error code enum.
	 */
	PB10010042(10010042, "设备已关闭"),
	/**
	 * PB 10010043 error code enum.
	 */
	PB10010043(10010043, "此设备属于其它用户"),
	;




	private int code;
    private String msg;

    /**
     * Msg string.
     *
     * @return the string
     */
    public String msg() {
        return msg;
    }

    /**
     * Code int.
     *
     * @return the int
     */
    public int code() {
        return code;
    }

    ErrorCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static ErrorCodeEnum getEnum(int code) {
        for (ErrorCodeEnum ele : ErrorCodeEnum.values()) {
            if (ele.code() == code) {
                return ele;
            }
        }
        return null;
    }
}
