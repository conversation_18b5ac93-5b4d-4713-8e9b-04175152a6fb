package com.customize.core.utils;

import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 */
@Component
public class MybatisBatchUtils {
	
	private static final int BATCH_SIZE = 1000;
	
	@Autowired
	private SqlSessionFactory sqlSessionFactory;
	
	public <T,U,R> boolean batchUpdateOrInsert(List<T> data, Class<U> mapperClass, BiFunction<T,U,R> function) {
		int i = 1;
		SqlSession batchSqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
		try {
			U mapper = batchSqlSession.getMapper(mapperClass);
			int size = data.size();
			for (T element : data) {
				function.apply(element, mapper);
				if ((i % BATCH_SIZE == 0) || i == size) {
					batchSqlSession.flushStatements();
				}
				i++;
			}
			// 非事务环境下强制commit，事务情况下该commit相当于无效
			batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
			return true;
		} catch (Exception e) {
			batchSqlSession.rollback();
			throw new RuntimeException(e);
		} finally {
			batchSqlSession.close();
		}
	}
}
