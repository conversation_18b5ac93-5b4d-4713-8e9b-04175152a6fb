/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.customize.core.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * 自定义OAuth2Exception
 */
public class UserLoginOutException extends Exception {
	@Getter
	private int errorCode;

	public UserLoginOutException(String msg) {
		super(msg);
		errorCode= HttpStatus.OK.value();
	}

	public UserLoginOutException(String msg, Throwable t) {
		super(msg,t);
	}

	public UserLoginOutException(String msg, int errorCode) {
		super(msg);
		this.errorCode = errorCode;
	}
}
