package com.customize.core.utils.excel.jdbm;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class BreedingCowsData implements Serializable {
	
	/** 所在镇 */
	private String townName;
	
	/** 所在村 */
	private String villageName;
	
	/** 养殖场名称 */
	private String farmName;
	
	/** 农户名称 */
	private String farmUserName;
	
	/** 农户手机 */
	private String farmUserPhone;
	
	/** 信息员名称 */
	private String messengerName;
	
	/** 信息员手机 */
	private String messengerPhone;
	
	/** 饲喂品种 */
	private String breedingType;
	
	/** 总数量 */
	private String totalCattleCoount;
	
	/** 母牛数量 */
	private String cowCattleCoount;
	
	/** 犊牛数量 */
	private String calfCattleCoount;
	
	public BreedingCowsData() {
	}
	
	public BreedingCowsData(String townName, String villageName, String farmName, String farmUserName, String farmUserPhone, String messengerName, String messengerPhone, String breedingType, String totalCattleCoount, String cowCattleCoount, String calfCattleCoount) {
		this.townName = townName;
		this.villageName = villageName;
		this.farmName = farmName;
		this.farmUserName = farmUserName;
		this.farmUserPhone = farmUserPhone;
		this.messengerName = messengerName;
		this.messengerPhone = messengerPhone;
		this.breedingType = breedingType;
		this.totalCattleCoount = totalCattleCoount;
		this.cowCattleCoount = cowCattleCoount;
		this.calfCattleCoount = calfCattleCoount;
	}
	
	public String getTownName() {
		return townName;
	}
	
	public void setTownName(String townName) {
		this.townName = townName;
	}
	
	public String getVillageName() {
		return villageName;
	}
	
	public void setVillageName(String villageName) {
		this.villageName = villageName;
	}
	
	public String getFarmName() {
		return farmName;
	}
	
	public void setFarmName(String farmName) {
		this.farmName = farmName;
	}
	
	public String getFarmUserName() {
		return farmUserName;
	}
	
	public void setFarmUserName(String farmUserName) {
		this.farmUserName = farmUserName;
	}
	
	public String getFarmUserPhone() {
		return farmUserPhone;
	}
	
	public void setFarmUserPhone(String farmUserPhone) {
		this.farmUserPhone = farmUserPhone;
	}
	
	public String getMessengerName() {
		return messengerName;
	}
	
	public void setMessengerName(String messengerName) {
		this.messengerName = messengerName;
	}
	
	public String getMessengerPhone() {
		return messengerPhone;
	}
	
	public void setMessengerPhone(String messengerPhone) {
		this.messengerPhone = messengerPhone;
	}
	
	public String getBreedingType() {
		return breedingType;
	}
	
	public void setBreedingType(String breedingType) {
		this.breedingType = breedingType;
	}
	
	public String getTotalCattleCoount() {
		return totalCattleCoount;
	}
	
	public void setTotalCattleCoount(String totalCattleCoount) {
		this.totalCattleCoount = totalCattleCoount;
	}
	
	public String getCowCattleCoount() {
		return cowCattleCoount;
	}
	
	public void setCowCattleCoount(String cowCattleCoount) {
		this.cowCattleCoount = cowCattleCoount;
	}
	
	public String getCalfCattleCoount() {
		return calfCattleCoount;
	}
	
	public void setCalfCattleCoount(String calfCattleCoount) {
		this.calfCattleCoount = calfCattleCoount;
	}
}
