package com.customize.core.utils;
import com.customize.core.constant.CustomizedCoreConstant;
import lombok.extern.slf4j.Slf4j;

import java.math.BigInteger;
import java.security.MessageDigest;

/**
 * <AUTHOR>
 * @ClassName: MD5Utils
 * @Description: TODO
 * @date 2023-04-20 18:16
 */
@Slf4j
public class MD5Utils {
	/**
	 * @return java.lang.String
	 * <AUTHOR>
	 * @Description md5加密 防止key过大
	 * @Date 2023-04-19
	 * @Param [str]
	 */
	public static String MD5Str(String str) {
		try {
			byte[] digest = MessageDigest.getInstance(CustomizedCoreConstant.MD5Str).digest(str.getBytes(CustomizedCoreConstant.UTF_8));
			//16是表示转换为16进制数
			return new BigInteger(1, digest).toString(16);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("MD5加密出错");
			return str;
		}

	}

	public static void main(String[] args) {
		long l = System.currentTimeMillis();
		System.out.println(l);
		String s = String.valueOf(l);
		String apiSign = MD5Str(
				"66980521".concat("#")
						.concat(s)
						.concat("#")
						.concat("ak")
						.concat("#")
						.concat("ak"));
		System.out.println(apiSign);


	}
}
