package com.customize.core.enmus.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @Description 项目状态
**/
@AllArgsConstructor
@Getter
public enum ApplicationStatusEnum {

    NORMAL(0,"服务中"),
    EXPIRED(1,"已过期"),


    APPLICATION_TYPE_ZHYZ(1,"智慧养殖"),
    APPLICATION_TYPE_ZHXM(2,"智慧畜牧"),
    APPLICATION_TYPE_ZJFW(3,"专家服务"),
    ;

    private Integer value;
    private String description;

}