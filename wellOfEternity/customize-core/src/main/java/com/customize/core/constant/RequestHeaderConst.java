package com.customize.core.constant;

/**
 * <AUTHOR> admin
 * @date : 2023/5/23
 * @description : 请求头信息常量
 */
public class RequestHeaderConst {
	
	/**
	 * 1.客户端类型(clientType): android,ios,
	 */
	public static final  String CLIENT_TYPE = "clientType";
	/**
	 * 2.发送请求的环境(requestEnv): test, prod,
	 */
	public static final  String REQUEST_ENV = "requestEnv";
	/**
	 * 3.设备类型(requestDeviceType): xiaomi,oppo,huawei
	 */
	public static final  String REQUEST_DEVICE_TYPE = "requestDeviceType";
	/**
	 * 4.版本号(appVersion): version
	 */
	public static final  String APP_VERSION = "appVersion";
	
	
	/**
	 *test
	 */
	public static final  String REQUEST_TEST_ENV = "test";
	/**
	 *prod
	 */
	public static final  String REQUEST_PROD_ENV = "prod";
	
	
	/**
	 * 客户端类型
	 */
	public enum ClientTypeEnum{
		/**
		 * android请求
		 */
		ANDROID("ANDROID","android请求"),
		
		/**
		 *ios请求
		 */
		IOS("IOS","ios请求"),
		
		/**
		 *PC请求
		 */
		PC("PC","PC请求"),
		
		;
		
		private String clientType;
		private String clientDesc;
		
		ClientTypeEnum(String clientType, String clientDesc) {
			this.clientType = clientType;
			this.clientDesc = clientDesc;
		}
		
		public String getClientType() {
			return clientType;
		}
		
		public void setClientType(String clientType) {
			this.clientType = clientType;
		}
		
		public String getClientDesc() {
			return clientDesc;
		}
		
		public void setClientDesc(String clientDesc) {
			this.clientDesc = clientDesc;
		}
	}
	
}
