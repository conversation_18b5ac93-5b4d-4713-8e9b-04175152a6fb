package com.customize.core.enmus.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OnlineEnum {
    ONLINE(0,"在线"),
    OFF_LINE(1,"离线"),


    ;

    private Integer value;
    private String description;

    /**
     * Gets enum.
     * @param value the code
     * @return the enum
     */
    public static String getStatusDesc(String value) {
        for (OnlineEnum onlineEnum : OnlineEnum.values()) {
            if (value.equalsIgnoreCase(onlineEnum.getValue()+"")) {
                return onlineEnum.description;
            }
        }
        return null;
    }
}
