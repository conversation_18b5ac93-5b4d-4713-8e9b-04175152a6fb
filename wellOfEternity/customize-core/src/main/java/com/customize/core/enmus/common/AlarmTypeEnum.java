package com.customize.core.enmus.common;
public enum AlarmTypeEnum {
    COUNT("count","盘点异常"),
    ANIMALHEAT("animalHeat","体温检测"),
    HEATMONITOR("heatMonitor","疾病预警"),
    BREED("breed","繁殖优化"),
    EARANIMALHEAT("earAnimalHeat","测温耳标检测"),


    CAMERA_BOUNDARY_ALARM("1","摄像头边界告警"),
    CAMERA_TEMPERATURE_ALARM("2","摄像头温度告警"),

    ;

    private String type;

    private String msg;

    public String getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    AlarmTypeEnum(String type, String msg) {
        this.type=type;
        this.msg=msg;

    }
}
