package com.customize.core.enmus.farm;

/**
 * <AUTHOR> admin
 * @date : 2023/6/14
 * @description :
 */
public enum SubjectTypeEnum {
	
	/**
	 * 规模养殖场
	 */
	SCALE_FARM(1,"规模养殖场"),
	/**
	 * 中小养殖户
	 */
	GENERAL_FARM(2,"中小养殖户"),
	;
	
	/**
	 *  值
	 */
	private Integer value;
	/**
	 * 描述
	 */
	private String valDesc;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getValDesc() {
		return valDesc;
	}
	
	public void setValDesc(String valDesc) {
		this.valDesc = valDesc;
	}
	
	SubjectTypeEnum(Integer value, String valDesc) {
		this.value = value;
		this.valDesc = valDesc;
	}
	
	/**
	 * Gets enum.
	 * @param value the code
	 * @return the enum
	 */
	public static SubjectTypeEnum getSubjectType(Integer value) {
		for (SubjectTypeEnum typeEnum : SubjectTypeEnum.values()) {
			if (value.intValue() == typeEnum.getValue().intValue()) {
				return typeEnum;
			}
		}
		return null;
	}
}
