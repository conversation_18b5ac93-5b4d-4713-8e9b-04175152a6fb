package com.customize.core.enmus.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 消息状态
 **/
@AllArgsConstructor
@Getter
public enum MessageStatusEnum {

    /**
     * 用户联系牧场主状态
     */
    CALLING(0, "联系中"),
    AGREE_CALL(1, "已同意"),
    NOT_CALL(2, "未联系"),



    /**
     * 状态
     */
    TO_CONFIRM_STATUS(1, "待确认"),
    AGREE_STATUS(2, "已同意"),
    REFUSED_STATUS(3, "已拒绝"),
    CLOSE_STATUS(4, "已关闭"),

    /**
     * 是否已读
     */
    NOT_READ(0, "未读"),
    ON_READ(1, "已读"),

    /**
     * 消息类别
     */
    MSG_TYPE_ONE(1, "联系消息"),
    MSG_TYPE_TWO(2, "申请经纪人"),
    MSG_TYPE_THREE(3, "邀请成为管理员"),

    /**
     * 是否公开
     */
    PUBLIC(0,"公开"),
    PRIVATE(1,"指定"),

    /**
     * 是否置顶
     */
    top(0,"置顶"),
    UN_TOP(1,"取消置顶"),
    ;

    private Integer value;
    private String description;

}