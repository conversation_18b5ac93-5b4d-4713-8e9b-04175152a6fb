package com.customize.core.utils;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.customize.core.params.PageQuery;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.SneakyThrows;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023-07-05 10:18
 * @PackageName:com.xmd.xiaomuding.base.server.controller
 * @ClassName: PageUtils
 * @Description: TODO
 * @Version 1.0
 */
public class PageUtils {


    /***
     * <AUTHOR>
     * @Description //pageHelper -> mp.page
     * @Date 14:39 2023-05-25
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * @Param [int, int, com.github.pagehelper.ISelect]
     */
    public static <E> Page<E> startPage(PageQuery pageQuery, ISelect iSelect) {
        return startPage(pageQuery.getCurrent(), pageQuery.getSize(), iSelect);
    }

    /***
     * <AUTHOR>
     * @Description //pageHelper -> mp.page
     * @Date 14:39 2023-05-25
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * @Param [int, int, com.github.pagehelper.ISelect]
     */
    public static <E> Page<E> startPage(int pageNum, int pageSize, ISelect iSelect) {
        PageInfo<E> pageInfo = PageHelper.startPage(pageNum, pageSize)
                .doSelectPageInfo(iSelect);
        Page<E> page = new Page<>();
        page.setSize(pageSize);
        page.setRecords(pageInfo.getList());
        page.setTotal(pageInfo.getTotal());
        page.setSize(pageInfo.getPageSize());
        page.setCurrent(pageInfo.getPageNum());
        return page;
    }


    /***
     * <AUTHOR>
     * @Description //pageHelper -> mp.page
     * @Date 14:39 2023-05-25
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * @Param [int, int, com.github.pagehelper.ISelect]
     */
    public static <E> Page<E> startPage(PageQuery pageQuery, ISelect iSelect, Class<E> eClass) {
        return startPage(pageQuery.getCurrent(), pageQuery.getSize(), iSelect, eClass);
    }

    @SneakyThrows
    public static <E> Page<E> startPage(int pageNum, int pageSize, ISelect iSelect, Class<E> eClass) {
        PageInfo<E> pageInfo = PageHelper.startPage(pageNum, pageSize)
                .doSelectPageInfo(iSelect);
        Page<E> page = new Page<>();
        page.setSize(pageSize);
        page.setRecords(BeanUtils.copyToList(pageInfo.getList(), eClass));
        page.setTotal(pageInfo.getTotal());
        page.setSize(pageInfo.getPageSize());
        page.setCurrent(pageInfo.getPageNum());
        return page;
    }

    /**
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * <AUTHOR>
     * @Description //手动分页 默认不要调用 性能差 若是统计的数据进行分页非sql直接查出没办法用这个
     * @Date 17:47 2023-07-12
     * @Param [int, int, java.util.List<E>]
     */
    public static <E> Page<E> startPage(PageQuery pageQuery, List<E> list) {
        return PageInfoUtils.list2Page(list, pageQuery.getCurrent(), pageQuery.getSize());
    }

    /**
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<E>
     * <AUTHOR>
     * @Description //手动分页 默认不要调用 性能差 若是统计的数据进行分页非sql直接查出没办法用这个
     * @Date 17:47 2023-07-12
     * @Param [int, int, java.util.List<E>]
     */
    public static <E> Page<E> startPage(int pageNum, int pageSize, List<E> list) {
        return PageInfoUtils.list2Page(list, pageNum, pageSize);
    }
}
