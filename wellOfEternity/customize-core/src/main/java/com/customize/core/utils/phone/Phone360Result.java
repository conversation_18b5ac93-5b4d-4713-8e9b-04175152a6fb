package com.customize.core.utils.phone;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class Phone360Result implements Serializable {
    /**
     * 返回code
     */
    private int code;
    /**
     * 结果
     */
    private PhoneAddressInfo data;


    public String formatData() {
        if (ObjectUtil.isNotEmpty(data)) {
            return data.getProvince() + data.getCity() + data.getSp();
        }
        return null;
    }

}