package com.customize.core.utils.excel;


import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExcelFillCellMergeStrategyDisk implements CellWriteHandler {

    /**
     * 合并字段的下标，如第一到五列new int[]{0,1,2,3,4}
     */
    private int[] mergeColumnIndex;
    /**
     * 从第几行开始合并，如果表头占两行，这个数字就是2
     */
    private int mergeRowIndex;

    public ExcelFillCellMergeStrategyDisk() {
    }

    private String header;
    private String footer;

    public ExcelFillCellMergeStrategyDisk(int mergeRowIndex, int[] mergeColumnIndex, String header, String footer){
        this.footer=footer;
        this.header=header;
        this.mergeRowIndex = mergeRowIndex;
        this.mergeColumnIndex = mergeColumnIndex;
    }


    public ExcelFillCellMergeStrategyDisk(int mergeRowIndex, int[] mergeColumnIndex) {
        this.mergeRowIndex = mergeRowIndex;
        this.mergeColumnIndex = mergeColumnIndex;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
                                 Head head, Integer integer, Integer integer1, Boolean aBoolean) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell,
                                Head head, Integer integer, Boolean aBoolean) {
//        //super.afterSheetCreate(writeWorkbookHolder, writeSheetHolder);
//        // 获取当前 sheet 的 POI 对象
//        Sheet sheet = writeSheetHolder.getSheet();
//        // 创建页眉对象
//        Header header = sheet.getHeader();
//        // 设置页眉内容，可以使用变量，如 &A 表示左对齐、&C 表示居中、&B 表示右对齐
//        header.setCenter(this.header);
//        // 创建页脚对象
//        Footer footer = sheet.getFooter();
//        // 设置页脚内容
//        footer.setRight(this.footer);
    }


    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
        //当前行
        int curRowIndex = cell.getRowIndex();
        //当前列
        int curColIndex = cell.getColumnIndex();

        if (curRowIndex > mergeRowIndex) {
            for (int i = 0; i < mergeColumnIndex.length; i++) {
                if (curColIndex == mergeColumnIndex[i]) {
                    mergeWithPrevRow(writeSheetHolder, cell, curRowIndex, curColIndex);
                    break;
                }
            }
        }
    }

    private void mergeWithPrevRow(WriteSheetHolder writeSheetHolder, Cell cell, int curRowIndex, int curColIndex) {
        //获取当前行的当前列的数据和上一行的当前列列数据，通过上一行数据是否相同进行合并
        Object curData = cell.getCellTypeEnum() == CellType.STRING ? cell.getStringCellValue() :
                cell.getNumericCellValue();

        Row preRow = cell.getSheet().getRow(curRowIndex - 1);
        Cell preCell = preRow.getCell(curColIndex);
        Object preData = preCell.getCellTypeEnum() == CellType.STRING ? preCell.getStringCellValue() :
                preCell.getNumericCellValue();
        if (StringUtils.isBlank(String.valueOf(curData)) || StringUtils.isBlank(String.valueOf(preData))) {
            return;
        }
        // 比较当前行的第一列的单元格与上一行是否相同，相同合并当前单元格与上一行
        if (curData.equals(preData)) {
            //从第1列开始校验上一列数据是否相同
            if (curColIndex > 1) {
                //当前行上一列数据
                Cell curRowPreCloCell = cell.getSheet().getRow(curRowIndex).getCell(curColIndex - 1);
                Object curRowPreCloData = curRowPreCloCell.getCellTypeEnum() == CellType.STRING ? curRowPreCloCell.getStringCellValue() :
                        curRowPreCloCell.getNumericCellValue();

                //上一行上一列数据
                Cell preRowPreCloCell = preRow.getCell(curColIndex - 1);
                Object preRowPreCloData = preRowPreCloCell.getCellTypeEnum() == CellType.STRING ? preRowPreCloCell.getStringCellValue() :
                        preRowPreCloCell.getNumericCellValue();
                if (StringUtils.isBlank(String.valueOf(curRowPreCloData)) || StringUtils.isBlank(String.valueOf(preRowPreCloData))) {
                    return;
                }
                //比较当前行上一列数据与上一行上一列数据是否相同，相同合并单元格
                if (!curRowPreCloData.equals(preRowPreCloData)) {
                    return;
                }
            }
            Sheet sheet = writeSheetHolder.getSheet();
            List<CellRangeAddress> mergeRegions = sheet.getMergedRegions();
            boolean isMerged = false;
            for (int i = 0; i < mergeRegions.size() && !isMerged; i++) {
                CellRangeAddress cellRangeAddr = mergeRegions.get(i);
                // 若上一个单元格已经被合并，则先移出原有的合并单元，再重新添加合并单元
                if (cellRangeAddr.isInRange(curRowIndex - 1, curColIndex)) {
                    sheet.removeMergedRegion(i);
                    cellRangeAddr.setLastRow(curRowIndex);
                    sheet.addMergedRegion(cellRangeAddr);
                    isMerged = true;
                }
            }
            // 若上一个单元格未被合并，则新增合并单元
            if (!isMerged) {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(curRowIndex - 1, curRowIndex, curColIndex,
                        curColIndex);
                sheet.addMergedRegion(cellRangeAddress);
            }
        }
    }

    public int[] getMergeColumnIndex() {
        return mergeColumnIndex;
    }

    public void setMergeColumnIndex(int[] mergeColumnIndex) {
        this.mergeColumnIndex = mergeColumnIndex;
    }

    public int getMergeRowIndex() {
        return mergeRowIndex;
    }

    public void setMergeRowIndex(int mergeRowIndex) {
        this.mergeRowIndex = mergeRowIndex;
    }
}

