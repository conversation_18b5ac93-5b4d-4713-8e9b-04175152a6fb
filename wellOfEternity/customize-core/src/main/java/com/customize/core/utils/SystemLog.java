package com.customize.core.utils;

import com.core.util.SpringContextHolder;
import com.core.util.WebUtils;
import com.customize.core.interceptor.AccessLimitInterceptor;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @ClassName: SystemLog
 * @Description: 系统日志记录
 * @date 2023-04-24 15:56
 * @eo.api-type http
 * @eo.groupName 默认分组
 * @eo.path
 * @eo.rpc-version null
 * @eo.rpc-group com.xmd.xiaomuding.common.core.util
 */
public class SystemLog {

    static InheritableThreadLocal<Object> local_message = new InheritableThreadLocal<>();


    /**
     * @param message
     * @return void
     * @eo.name writeLog
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     */
    public static void writeLog(Object message) {
        try {
            //将参数放入副本
            local_message.set(message);
            HttpServletRequest request = WebUtils.getRequest();
            //获取接口名称
            String requestURI = request.getRequestURI();
            //获取请求参数
            String queryString = request.getQueryString();
            //获取请求body
            String postData = AccessLimitInterceptor.getPostData(request);
            //TODO 获取登录人员信息 操作人
            //TODO 进行入库操作
            SpringContextHolder.clearHolder();
        } catch (Exception e) {
            //系统操作日志异常 不要抛出异常影响正常逻辑
            e.printStackTrace();
        } finally {
            local_message.remove();
        }

    }


}
