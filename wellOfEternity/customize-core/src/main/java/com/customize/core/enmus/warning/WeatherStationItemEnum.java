package com.customize.core.enmus.warning;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * 告警类型
 * <AUTHOR>
 */
public enum WeatherStationItemEnum {
	//'数据类别：1.温度 2.湿度 3.风速 4.风向  5.雨量 6.太阳辐射
	/**
	 * 温度
	 */
	TEMPERATURE_WARNING("1","温度","℃"),
	/**
	 * 湿度
	 */
	HUMIDITY_WARNING("2","湿度","%"),
	/**
	 * 风速
	 */
	WIND_SPEED_WARNING("8","风速","m/s"),
	/**
	 * 风向
	 */
	WIND_DIRECTION_WARNING("11","风向","°"),
	/**
	 * 降雨量
	 */
	RAINFALL_WARNING("9","降雨量","mm"),
	/**
	 * 太阳辐射
	 */
	SOLAR_RADIATION_WARNING("12","太阳辐射","W/m2"),

	;

    private String value;
    private String descr;
	private String unit;
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescr() {
		return descr;
	}
	
	public void setDescr(String descr) {
		this.descr = descr;
	}
	
	public String getUnit() {
		return unit;
	}
	
	public void setUnit(String unit) {
		this.unit = unit;
	}
	
	WeatherStationItemEnum(String value, String descr, String unit) {
		this.value = value;
		this.descr = descr;
		this.unit = unit;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WeatherStationItemEnum getInstance(String value){
		for (WeatherStationItemEnum typeEnum : WeatherStationItemEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue())) {
				return typeEnum;
			}
		}
		return null;
	}
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WeatherStationItemEnum envItemEnum = getInstance(value);
		if (Objects.nonNull(envItemEnum)){
			return envItemEnum.getDescr();
		}
		return "";
	}


	@Data
	@Schema(description = "告警类型")
	public static class WeatherStationItem{
		public WeatherStationItem(WeatherStationItemEnum envStationItemEnum){
			this.keyCode=envStationItemEnum.value;
			this.keyName=envStationItemEnum.descr;
			this.keyUnit=envStationItemEnum.unit;
		}
		@Schema(description="关键字、词：温度")
		private String keyName;

		@Schema(description="关键词code：temperature")
		private String keyCode;
		@Schema(description="单位")
		private String keyUnit;
	}
}
