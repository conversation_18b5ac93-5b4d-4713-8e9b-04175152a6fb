package com.customize.core.annotation;


import com.customize.core.enmus.request.IdempotentTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @ClassName: 此注解标识需要有幂等性的接口方法上, 拦截器会对有此注解的方法实现幂等性操作
 * @Description: 分布式索自定义注解
 * @date 2023-04-20 11:21
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AutoIdempotent {

	/**
	 * 允许接口的请求次数 幂等性操作下默认是1
	 */
	long expireTime() default 3L;

	/**
	 * 时间单位 默认为妙
	 */
	TimeUnit timeUnit() default TimeUnit.SECONDS;

	/**
	 * 以何种方式进行接口幂等 ** 如果配置全局生效【OVERALL】 则相当于针对接口做 key = 接口本身自己
	 */
	IdempotentTypeEnum idempotentType() default IdempotentTypeEnum.IP;

}
