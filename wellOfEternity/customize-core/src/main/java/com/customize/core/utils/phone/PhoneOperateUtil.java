package com.customize.core.utils.phone;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class PhoneOperateUtil {
	
	/**
	 * 获取手机号运营商信息
	 * @return
	 */
	public static String getPhoneLess(String phone){
		if (StringUtils.isEmpty(phone)){
			return  "";
		}
		try {
			Phone360Result result = getPhoneInfo(phone);
			if (Objects.nonNull(result)){
				return result.getData().getSp();
			}
		}catch (Exception e){
			return "";
		}
		return "";
	}
	
	/**
	 * 获取手机号运营商信息
	 * @return
	 */
	public static String getPhonePart(String phone){
		if (StringUtils.isEmpty(phone)){
			return  "";
		}
		try {
			Phone360Result result = getPhoneInfo(phone);
			if (Objects.nonNull(result)){
				return result.getData().getCity()+result.getData().getSp();
			}
		}catch (Exception e){
			return "";
		}
		return "";
	}
	
	/**
	 * 获取手机号运营商信息
	 * @return
	 */
	public static String getPhoneFull(String phone){
		if (StringUtils.isEmpty(phone)){
			return  "";
		}
		try {
			Phone360Result result = getPhoneInfo(phone);
			if (Objects.nonNull(result)){
				return result.getData().getProvince()+result.getData().getCity()+result.getData().getSp();
			}
		}catch (Exception e){
			return "";
		}
		return "";
	}
	
	
	/**
	 * 获取手机号运营商信息
	 * @return
	 */
	public static Phone360Result getPhoneInfo(String phone){
		String httpUrl = "https://cx.shouji.360.cn/phonearea.php?number="+phone;
		HashMap<String, String> headers = new HashMap<>();
		HashMap<String, Object> map = new HashMap<>();
		//请求
		HttpResponse response=HttpUtil.createGet(httpUrl).addHeaders(headers).form(map).execute();
		if (response.isOk()){
			String result = response.body();
			String res = unicodeDecode(result);
			res = res.replaceAll("\r\n","");
			//{"code":0,"data":{"province":"陕西","city":"榆林","sp":"移动"}}
			Phone360Result phone360Result = JSON.parseObject(res,Phone360Result.class);
			return phone360Result;
		}
		return null;
	}
	
	/**
	 * @Title: unicodeDecode
	 * @Description: unicode解码
	 * @param string
	 * @return
	 */
	private static String unicodeDecode(String string) {
		Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
		Matcher matcher = pattern.matcher(string);
		char ch;
		while (matcher.find()) {
			ch = (char) Integer.parseInt(matcher.group(2), 16);
			string = string.replace(matcher.group(1), ch + "");
		}
		return string;
	}
}
