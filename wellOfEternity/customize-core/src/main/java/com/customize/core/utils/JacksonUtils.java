package com.customize.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import lombok.SneakyThrows;

import java.io.IOException;
import java.util.Iterator;
import java.util.List;

//import org.springframework.boot.configurationprocessor.json.JSONObject;

/**
 * <AUTHOR>
 */
public class JacksonUtils {
    private JacksonUtils() {
        //default construct
    }

    public static String jsonStr(Object obj) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        String str = mapper.writeValueAsString(obj);
        return str;
    }

    /**
     * Json反序列化
     *
     * @param val
     * @param cls
     * @return
     * @throws JsonParseException
     * @throws IOException
     * @desc 1.实体上
     * @JsonInclude(Include.NON_NULL) 将该标记放在属性上，如果该属性为NULL则不参与序列化
     * 如果放在类上边,那对这个类的全部属性起作用
     * Include.Include.ALWAYS 默认
     * Include.NON_DEFAULT 属性为默认值不序列化
     * Include.NON_EMPTY 属性为 空（“”） 或者为 NULL 都不序列化
     * Include.NON_NULL 属性为NULL 不序列化
     * 2.代码上
     * ObjectMapper mapper = new ObjectMapper();
     * mapper.setSerializationInclusion(Include.
     * NON_NULL);
     * 通过该方法对mapper对象进行设置，所有序列化的对象都将按改规则进行系列化
     * Include.Include.ALWAYS 默认
     * Include.NON_DEFAULT 属性为默认值不序列化
     * Include.NON_EMPTY 属性为 空（“”） 或者为 NULL 都不序列化
     * Include.NON_NULL 属性为NULL 不序列化
     */
    public static <T> T parseJsonFromString(String val, Class<T> cls)
            throws JsonParseException, IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT,true);
        return mapper.readValue(val, cls);
    }

    public static String serialObject(Object obj, FilterProvider filters) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        if (filters != null) {
            mapper.setFilterProvider(filters);
        }
        //把null替换为""字符串
        mapper.getSerializerProvider().setNullValueSerializer(
                new JsonSerializer<Object>() {
                    @Override
                    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
                        gen.writeString("");
                    }
                });
        return mapper.writeValueAsString(obj);
    }

    public static JsonNode parseJsonFromString(String val) throws JsonProcessingException, IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(val);
    }

    public static final <T> List<T> parseList(String text, Class<T> clazz) {
        return JSON.parseArray(text, clazz);
    }

    public static final String toPrettyJSONString(Object object) {
        return JSON.toJSONString(object, new SerializerFeature[]{SerializerFeature.PrettyFormat});
    }

    public static final String toJSONString(Object object, SerializerFeature... features) {
        return JSON.toJSONString(object, features);
    }

    public static final <T> T parseObject(String text, Class<T> clazz, Feature... features) {
        return JSON.parseObject(text, clazz, features);
    }
    /**
     * json去掉空格
     * @param jsonObject
     * @return
     */
    @SneakyThrows
    public static JSONObject JsonStrTrim(JSONObject jsonObject){

        if( jsonObject != null){
//            Iterator itt = jsonObject.keys();
            Iterator itt = jsonObject.keySet().iterator();
            while (itt.hasNext()) {
                String key = itt.next().toString();
                String value = jsonObject.getString(key);

                if(value == null){
                    continue ;
                }else if("".equals(value.trim())){
                    continue ;
                }else{
                    jsonObject.put(key, value.trim());
                }
            }
        }
        return jsonObject;
    }
}
