package com.customize.core.utils;

import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5 {
    
    /**
     * 利用MD5进行加密
     */
    public static String EncoderByMd5(String str) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        //确定计算方法
        return Base64.encodeBase64String(MessageDigest.getInstance("MD5").digest(str.getBytes("utf-8")));
    }
    
    /**
     * 判断用户密码是否正确
     * newPassWord 用户输入的密码
     * oldPassword 正确密码
     */
    public static boolean checkPassword(String newPassWord, String oldPassword) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        return EncoderByMd5(newPassWord).equals(oldPassword);
    }
}
