package com.customize.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/*
 * @title SmsUtils
 * @description: 短信验证码工具类
 * <AUTHOR>
 * @version 1.0
 * @create 2024/3/14
 */
@Component
@Slf4j
public class SmsUtils {

    private static RedisTemplate<String, String> strRedisTemplate;

    @Resource
    public void setStrRedisTemplate(RedisTemplate<String, String> strRedisTemplate){
        SmsUtils.strRedisTemplate = strRedisTemplate;
    }

    /**
     * @description: 校验短信验证码是否正确
     * @param phone 验证的手机号
     * @param phoneCode 验证码
     * @param codeType 项目类型(SmsCodeTypeEnum.java)
     * <AUTHOR>
     * @time 2024/3/14
     */
    public static String checkSmsPhoneCode(String phone, String phoneCode, String codeType){
        String message = "";
        if (StringUtils.isBlank(phoneCode)){
            message ="请输入短信验证码";
            return message;
        }
        String key = "CODE_REDIS_KEY" + codeType + ":"+ phone;
        String value = Objects.toString(strRedisTemplate.opsForValue().get(key),"");
        if (StringUtils.isBlank(value)){
            message ="短信验证码已经失效了";
            return message;
        }
        if (!phoneCode.trim().equalsIgnoreCase(value)){
            message ="短信验证码输入有误";
            return message;
        }
        return message;
    }
}
