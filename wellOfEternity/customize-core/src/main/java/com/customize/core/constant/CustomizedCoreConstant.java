package com.customize.core.constant;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @ClassName: CustomizedCoreConstant
 * @Description: TODO
 * @date 2023-04-20 16:56
 */
public class CustomizedCoreConstant {

	/**
	 * The System property name of Standalone mode
	 */
	public static String UTF_8 = "utf-8";
	public static String MD5Str = "md5";
	public static String HASH  = "#";
	public static String BLACK  = "";
	public static String DOT   = "\\.";
	
	public static String COLON   = ":";
	public static String DISTRIBUTED_LOCK = "xmd:distributed:lock:";
	public static String AUTO_IDEMPOTENT_LOCK = "xmd:autoIdempotent:lock:";
	public static String AUTO_ATTACKING_LOCK = "xmd:attacking:lock:";
	/**
	 * Redis相关类
	 */
	public static final String PREFIX = "RATE_LIMITER_";
	
	/**
	 * 默认验证码
	 */
	public static final String DEFAULT_VERIFICATION_CODE = "666666";
	/**
	 * 默认密码
	 */
	public static final String DEFAULT_USER_PASSWORD = "666666";
	/**
	 * 全局验证码 key 头
	 */
	public static final String SMS_GLOBAL_CACHE_KEY = "mobile:sms:code:";
	
	/**
	 * 阿里短信发送成功标识
	 */
	public static final String ALI_SMS_SUCCESS_CODE = "OK";
	
	/**
	 * 阿里短信发送成功标识
	 */
	public static final String DEFAULT_STEP = "100";
	
	/**
	 * 原始类型
	 */
	public static HashMap<String, Class> PRIMIVATE_MAP = new HashMap<String, Class>() {
		{
			put("java.lang.Integer", int.class);
			put("java.lang.Double", double.class);
			put("java.lang.Float", float.class);
			put("java.lang.Long", long.class);
			put("java.lang.Short", short.class);
			put("java.lang.Boolean", boolean.class);
			put("java.lang.Char", char.class);
		}
	};
	
	
}
