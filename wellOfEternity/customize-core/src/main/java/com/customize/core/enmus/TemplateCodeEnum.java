package com.customize.core.enmus;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 短信模板
 * @Date : 2022/4/12 15:50
 **/
public enum TemplateCodeEnum {

    SMS_205876727("SMS_205876727","验证码","验证码 ${code}，请尽快完成验证，为了安全起见不要告诉他人！"),

    SMS_237570969("SMS_237570969","关注的专家兽医开播通知","你关注的${teacher}老师开始直播了，点击 xiaomuding.com 观看。"),

    SMS_237570789("SMS_237570789","关注的专家兽医新视频通知","你关注的${teacher}老师发布了新视频，点击 xiaomuding.com 观看。"),

    SMS_237585657("SMS_237585657","关联牧场监控设备掉线通知","您管理的牧场监控设备已掉线，请及时联系检查电源或网络情况，以免影响展销和算法监测服务，详情点击xiaomuding.com查看"),

    SMS_237590586("SMS_237590586","视频问诊提醒通知" ,"有客户向您发起视频通话问诊，请及时接听，点击 xiaomuding.com 快速打开。"),

    SMS_237585623("SMS_237585623","屠宰订单支付成功通知","您已成功支付屠宰订单金额￥${money}，请勿重复支付，订单详情点击xiaomuding.com查看"),

    SMS_237585621("SMS_237585621","屠宰订单待支付通知","您有待支付屠宰订单金额￥${money}，避免影响您的正常屠宰进场请及时支付，详情点击xiaomuding.com查看"),

    SMS_237560698("SMS_237560698","设备掉线通知","你的监控设备已掉线，请及时检查电源或网络情况，避免影响展销和算法监测，详情点击xiaomuding.com查看"),

    SMS_237570187("SMS_237570187","牲畜购买联系牧场主通知","有客户联系你购买牲畜，点击 xiaomuding.com 查看，小牧丁活畜交易市场" ),

    SMS_237560242("SMS_237560242","牲畜采购联系经纪人通知","有客户联系你采购牲畜，点击 xiaomuding.com 查看，小牧丁活畜交易市场" ),

    SMS_237560013("SMS_237560013","专家兽医开通通知","您的专家兽医认证升级已成功通过，电脑端登录请至vmuyun.com，帐号：本机号码（小牧丁注册帐号）；默认密码：666666，为保障数据安全，请尽快使用小牧丁App修改密码，修改后请使用新密码登录"),

    SMS_237560011("SMS_237560011","屠宰管理开通通知","您的屠宰场管理认证升级已成功通过，电脑端登录请至vmuyun.com，帐号：本机号码（小牧丁注册帐号）；默认密码：666666，为保障数据安全，请尽快使用小牧丁App修改密码，修改后请使用新密码登录" ),

    SMS_237207641("SMS_237207641","智慧养殖开通通知", "您的智慧养殖认证升级已成功通过，电脑端登录请至vmuyun.com，帐号：本机号码（小牧丁注册帐号）；默认密码：666666，为保障数据安全，请尽快使用小牧丁App修改密码，修改后请使用新密码登录"),

    SMS_237207634("SMS_237207634","监控畜只算法告警通知", "设备监测到畜只异常，点击xiaomuding.com查看"),

    SMS_237212552("SMS_237212552","通知屠宰场、贩运人、交易市场联系", "有客户联系你销售牲畜，点击 xiaomuding.com 查看，小牧丁活畜交易市场"),

    SMS_219744591("SMS_219744591","智慧畜牧开通通知","您的智慧畜牧公共服务平台管理帐号已成功授权，登录请至http://vmuyun.com管理辖区内畜牧信息数据及服务，帐号：本机号码（小牧丁注册帐号）；默认密码：666666，为保障数据安全，请尽快使用小牧丁App修改密码，修改后请使用新密码登录，App下载地址http://xiaomuding.com"),

    SMS_219738402("SMS_219738402","智慧畜牧开通通知2", "您好，智慧畜牧公共服务平台管理帐号已成功授权，登录请至http://vmuyun.com，输入小牧丁帐号：本机号码；默认密码：666666，为保障数据安全，请尽快至小牧丁App修改密码，修改后请使用新密码登录，下载地址http://xiaomuding.com"),

    SMS_243230726("SMS_243230726", "服务开通成功通知-修订版", "您的${service}认证升级已成功通过，电脑端登录请至vmuyun.com，帐号：本机号码（小牧丁注册帐号）；默认密码：666666，为保障数据安全，请尽快使用小牧丁App修改密码，修改后请使用新密码登录"),

    SMS_238473075("SMS_238473075", "服务开通失败通知", "您的${service}认证升级审核未通过，请${cause}后重新提交，详情点击xiaomuding.com查看"),

	//start :见犊补母
	SMS_262500116("SMS_262500116", "登记“见犊补母”项目养殖场信息的验证码", "信息员正在为您登记“见犊补母”项目养殖场信息,请向信息员出示校验码:${code},登记完成后可前往xiaomuding.com下载小牧丁APP进行查看,如非本人操作，请忽略。"),
	SMS_262565126("SMS_262565126", "用户录入权限赋予通知","尊敬的用户${userName}您好，您的牧场${farmName}母牛畜只录入权限已由信息员${optName}移交予您，您可自行前往xiaomuding.com下载小牧丁APP进行录入、登记。 小牧丁温馨提示为了您的项目正常进行，请务必尽快下载完善。"),

	SMS_276296683("SMS_276296683", "见犊补母备案通知", "您的养殖场已备案至榆阳区肉牛增量提质智慧畜牧平台，请前往xiaomuding.com下载小牧丁APP，上报母牛相关信息申请补贴。"),
	//end :见犊补母
	SMS_268480945("SMS_268480945","异常通知","${name}异常告警")
    ;
    private String code;
    private String msg;
    private String params;

    /**
     * Msg string.
     *
     * @return the string
     */
    public String msg() {
        return msg;
    }
    /**
     * params string.
     *
     * @return the string
     */
    public String params() {
        return params;
    }

    /**
     * Code int.
     *
     * @return the int
     */
    public String code() {
        return code;
    }

    TemplateCodeEnum(String code, String msg, String params) {
        this.code = code;
        this.msg = msg;
        this.params = params;
    }

    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static TemplateCodeEnum getEnum(String code) {
        for (TemplateCodeEnum ele : TemplateCodeEnum.values()) {
            if (code.equals(ele.code())) {
                return ele;
            }
        }
        return null;
    }


}
