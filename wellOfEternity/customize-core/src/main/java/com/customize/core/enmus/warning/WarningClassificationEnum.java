package com.customize.core.enmus.warning;

import java.util.Objects;

/**
 * 告警分类
 * <AUTHOR>
 */
public enum WarningClassificationEnum {
	/**
	 * 环境异常
	 */
	ENVIRONMENT_CLASSIFICATION(1,"环境数据异常",1),
	/**
	 * 设备异常
	 */
	DEVICE_CLASSIFICATION(2,"设备异常",3),
	/**
	 * 饲喂异常
	 */
	FEEDING_CLASSIFICATION(3,"饲喂异常",4),
	/**
	 * 繁育异常
	 */
	BREEDING_CLASSIFICATION(4,"繁育数据异常",5),
	
	/**
	 * 气象站异常
	 */
	METEOROLOGICAL_STATION_CLASSIFICATION(5,"气象站数据异常",2),
	
	;

    private Integer value;
    private String descr;
	private Integer order;
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getDescr() {
		return descr;
	}
	
	public void setDescr(String descr) {
		this.descr = descr;
	}
	
	public Integer getOrder() {
		return order;
	}
	
	public void setOrder(Integer order) {
		this.order = order;
	}
	
	WarningClassificationEnum(Integer value, String descr,Integer order) {
		this.value = value;
		this.descr = descr;
		this.order = order;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WarningClassificationEnum getInstance(String value){
		for (WarningClassificationEnum typeEnum : WarningClassificationEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
				return typeEnum;
			}
		}
		return null;
	}
	
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WarningClassificationEnum classification = getInstance(value);
		if (Objects.nonNull(classification)){
			return classification.getDescr();
		}
		return "";
	}
}
