package com.customize.core.enmus.common;

import java.util.Arrays;
//"关联类型(1:棚标签;2:摄像头;3:控制设备;4:传感设备)
public enum ShedJoinDataTypeEnum {
    IOT_BODY_TYPE(1,"棚标签"),
    TEC_ROBOT(2,"摄像头"),
    FEED_CAR(3,"控制设备"),
    FLY_CONTROL(4,"传感设备")
    ;

    private Integer type;

    private String msg;

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    ShedJoinDataTypeEnum(Integer type, String msg) {
        this.type=type;
        this.msg=msg;

    }

    public static ShedJoinDataTypeEnum getEnumByType(Integer type) {
        return Arrays.stream(ShedJoinDataTypeEnum.values())
                .filter(x -> x.getType().equals(type))
                .findFirst()
                .orElse(null);
    }


}
