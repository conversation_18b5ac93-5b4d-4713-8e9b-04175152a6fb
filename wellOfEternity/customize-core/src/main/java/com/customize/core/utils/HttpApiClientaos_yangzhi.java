//
//  Created by  fred on 2017/1/12.
//  Copyright © 2016年 Alibaba. All rights reserved.
//

package com.customize.core.utils;

import com.alibaba.cloudapi.sdk.client.ApacheHttpClient;
import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.ParamPosition;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.alibaba.cloudapi.sdk.model.ApiCallback;
import com.alibaba.cloudapi.sdk.model.ApiRequest;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;


public class HttpApiClientaos_yangzhi extends ApacheHttpClient{
    public final static String HOST = "api.aos-etp.com";
    static HttpApiClientaos_yangzhi instance = new HttpApiClientaos_yangzhi();
    public static HttpApiClientaos_yangzhi getInstance(){return instance;}
    @Override
    public void init(HttpClientBuilderParams httpClientBuilderParams){
        httpClientBuilderParams.setScheme(Scheme.HTTP);
        httpClientBuilderParams.setHost(HOST);
        super.init(httpClientBuilderParams);
    }




    public void startAlgoBySerial(String appKey , String deviceSerial , Integer channelNo , String algoEngName , String orderNo , ApiCallback callback) {
        String path = "/open/v1/startAlgoBySerial";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , true);
        request.addParam("channelNo" , String.valueOf(channelNo) , ParamPosition.QUERY , false);
        request.addParam("algoEngName" , algoEngName , ParamPosition.QUERY , false);
        request.addParam("orderNo" , orderNo , ParamPosition.QUERY , false);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse startAlgoBySerialSyncMode(String appKey , String deviceSerial , String channelNo , String algoEngName , String orderNo) {
        String path = "/open/v1/startAlgoBySerial";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , true);
        request.addParam("channelNo" , channelNo , ParamPosition.QUERY , false);
        request.addParam("algoEngName" , algoEngName , ParamPosition.QUERY , false);
        request.addParam("orderNo" , orderNo , ParamPosition.QUERY , false);



        return sendSyncRequest(request);
    }
    public void closeAlgoBySerial(String appKey , String deviceSerial , Integer channelNo , String algoEngName , ApiCallback callback) {
        String path = "/open/v1/closeAlgoBySerial";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , true);
        request.addParam("channelNo" , String.valueOf(channelNo) , ParamPosition.QUERY , false);
        request.addParam("algoEngName" , algoEngName , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse closeAlgoBySerialSyncMode(String appKey , String deviceSerial , String channelNo , String algoEngName, String orderId) {
        String path = "/open/v1/closeAlgoBySerial";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , true);
        request.addParam("channelNo" , channelNo , ParamPosition.QUERY , false);
        request.addParam("algoEngName" , algoEngName , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void deleteUser(Long id , String appKey , ApiCallback callback) {
        String path = "/open/v1/deleteUser/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse deleteUserSyncMode(Long id , String appKey) {
        String path = "/open/v1/deleteUser/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        return sendSyncRequest(request);
    }
    public void deleteDevice(Long id , String appKey , ApiCallback callback) {
        String path = "/open/v1/deleteDevice/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse deleteDeviceSyncMode(Long id , String appKey) {
        String path = "/open/v1/deleteDevice/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        return sendSyncRequest(request);
    }
    public void deleteFarm(Long id , String appKey , ApiCallback callback) {
        String path = "/open/v1/deleteFarm/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse deleteFarmSyncMode(Long id , String appKey) {
        String path = "/open/v1/deleteFarm/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        return sendSyncRequest(request);
    }
    public void deleteIsvResource(Long id , String appKey , ApiCallback callback) {
        String path = "/open/v1/deleteIsvResource/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse deleteIsvResourceSyncMode(Long id , String appKey) {
        String path = "/open/v1/deleteIsvResource/[id]";
        ApiRequest request = new ApiRequest(HttpMethod.DELETE , path);
        request.addParam("id" , String.valueOf(id) , ParamPosition.PATH , true);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        return sendSyncRequest(request);
    }
    public void listUser(String appKey , Integer pageNum , Integer pageSize , ApiCallback callback) {
        String path = "/open/v1/listUser";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse listUserSyncMode(String appKey , Integer pageNum , Integer pageSize) {
        String path = "/open/v1/listUser";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void addUser(String appKey , String userName , String certificateType , String certificateNumber , String userAddress , String mobile , String linkman , ApiCallback callback) {
        String path = "/open/v1/addUser";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("userName" , userName , ParamPosition.QUERY , true);
        request.addParam("certificateType" , certificateType , ParamPosition.QUERY , false);
        request.addParam("certificateNumber" , certificateNumber , ParamPosition.QUERY , false);
        request.addParam("userAddress" , userAddress , ParamPosition.QUERY , true);
        request.addParam("mobile" , mobile , ParamPosition.QUERY , true);
        request.addParam("linkman" , linkman , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse addUserSyncMode(String appKey , String userName , String certificateType , String certificateNumber , String userAddress , String mobile , String linkman) {
        String path = "/open/v1/addUser";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("userName" , userName , ParamPosition.QUERY , true);
        request.addParam("certificateType" , certificateType , ParamPosition.QUERY , false);
        request.addParam("certificateNumber" , certificateNumber , ParamPosition.QUERY , false);
        request.addParam("userAddress" , userAddress , ParamPosition.QUERY , true);
        request.addParam("mobile" , mobile , ParamPosition.QUERY , true);
        request.addParam("linkman" , linkman , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void listIsvResource(String appKey , Integer pageNum , Integer pageSize , ApiCallback callback) {
        String path = "/open/v1/listIsvResource";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse listIsvResourceSyncMode(String appKey , Integer pageNum , Integer pageSize) {
        String path = "/open/v1/listIsvResource";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void addIsvResource(String appKey , String accountName , String ysyAppKey , String appSecret , Integer resourceType , Integer bandwidth , Integer accessDeviceNum , Integer accountFrequencyMax , Integer resourceSafeDays , ApiCallback callback) {
        String path = "/open/v1/addIsvResource";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("accountName" , accountName , ParamPosition.QUERY , true);
        request.addParam("ysyAppKey" , ysyAppKey , ParamPosition.QUERY , true);
        request.addParam("appSecret" , appSecret , ParamPosition.QUERY , true);
        request.addParam("resourceType" , String.valueOf(resourceType) , ParamPosition.QUERY , true);
        request.addParam("bandwidth" , String.valueOf(bandwidth) , ParamPosition.QUERY , true);
        request.addParam("accessDeviceNum" , String.valueOf(accessDeviceNum) , ParamPosition.QUERY , true);
        request.addParam("accountFrequencyMax" , String.valueOf(accountFrequencyMax) , ParamPosition.QUERY , true);
        request.addParam("resourceSafeDays" , String.valueOf(resourceSafeDays) , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse addIsvResourceSyncMode(String appKey , String accountName , String ysyAppKey , String appSecret , Integer resourceType , Integer bandwidth , Integer accessDeviceNum , Integer accountFrequencyMax , Integer resourceSafeDays) {
        String path = "/open/v1/addIsvResource";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("accountName" , accountName , ParamPosition.QUERY , true);
        request.addParam("ysyAppKey" , ysyAppKey , ParamPosition.QUERY , true);
        request.addParam("appSecret" , appSecret , ParamPosition.QUERY , true);
        request.addParam("resourceType" , String.valueOf(resourceType) , ParamPosition.QUERY , true);
        request.addParam("bandwidth" , String.valueOf(bandwidth) , ParamPosition.QUERY , true);
        request.addParam("accessDeviceNum" , String.valueOf(accessDeviceNum) , ParamPosition.QUERY , true);
        request.addParam("accountFrequencyMax" , String.valueOf(accountFrequencyMax) , ParamPosition.QUERY , true);
        request.addParam("resourceSafeDays" , String.valueOf(resourceSafeDays) , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void listDeviceByCondition(String appKey , Long farmId , String house , Integer pageNum , Integer pageSize , ApiCallback callback) {
        String path = "/open/v1/listDeviceByCondition";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("farmId" , String.valueOf(farmId) , ParamPosition.QUERY , true);
        request.addParam("house" , house , ParamPosition.QUERY , false);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse listDeviceByConditionSyncMode(String appKey , Long farmId , String house , Integer pageNum , Integer pageSize) {
        String path = "/open/v1/listDeviceByCondition";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("farmId" , String.valueOf(farmId) , ParamPosition.QUERY , true);
        request.addParam("house" , house , ParamPosition.QUERY , false);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void listFarm(String appKey , Integer pageNum , Integer pageSize , Long userId , ApiCallback callback) {
        String path = "/open/v1/listFarm";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);
        request.addParam("userId" , String.valueOf(userId) , ParamPosition.QUERY , false);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse listFarmSyncMode(String appKey , Integer pageNum , Integer pageSize , Long userId) {
        String path = "/open/v1/listFarm";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);
        request.addParam("userId" , String.valueOf(userId) , ParamPosition.QUERY , false);



        return sendSyncRequest(request);
    }
    public void getAlgoResultBySerial(String appKey , String deviceSerial , Integer channelNo , String algoEngName , String ds , String columnCode , ApiCallback callback) {
        String path = "/open/v1/getAlgoResultBySerial";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , true);
        request.addParam("channelNo" , String.valueOf(channelNo) , ParamPosition.QUERY , false);
        request.addParam("algoEngName" , algoEngName , ParamPosition.QUERY , false);
        request.addParam("ds" , ds , ParamPosition.QUERY , false);
        request.addParam("columnCode" , columnCode , ParamPosition.QUERY , false);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse getAlgoResultBySerialSyncMode(String appKey , String deviceSerial , String channelNo , String algoEngName , String ds , String columnCode , String orderNo) {
        String path = "/open/v1/getAlgoResultBySerial";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , true);
        request.addParam("channelNo" , channelNo , ParamPosition.QUERY , false);
        request.addParam("algoEngName" , algoEngName , ParamPosition.QUERY , false);
        request.addParam("ds" , ds , ParamPosition.QUERY , false);
        request.addParam("columnCode" , columnCode , ParamPosition.QUERY , false);
        request.addParam("orderNo" , orderNo , ParamPosition.QUERY , false);



        return sendSyncRequest(request);
    }
    public void getProvince(String appKey , ApiCallback callback) {
        String path = "/open/v1/getProvince";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse getProvinceSyncMode(String appKey) {
        String path = "/open/v1/getProvince";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);



        return sendSyncRequest(request);
    }
    public void getCity(String appKey , String parentCode , ApiCallback callback) {
        String path = "/open/v1/getCity";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("parentCode" , parentCode , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse getCitySyncMode(String appKey , String parentCode) {
        String path = "/open/v1/getCity";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("parentCode" , parentCode , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void addDevice(String appKey , Long farmId , String house , String deviceBrand , Integer deviceType , String deviceName , String deviceSerial , String deviceModel , String contactPerson , String contactMobile , Long ysyId , Integer vedioResource , ApiCallback callback) {
        String path = "/open/v1/addDevice";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("farmId" , String.valueOf(farmId) , ParamPosition.QUERY , true);
        request.addParam("house" , house , ParamPosition.QUERY , false);
        request.addParam("deviceBrand" , deviceBrand , ParamPosition.QUERY , true);
        request.addParam("deviceType" , String.valueOf(deviceType) , ParamPosition.QUERY , true);
        request.addParam("deviceName" , deviceName , ParamPosition.QUERY , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , false);
        request.addParam("deviceModel" , deviceModel , ParamPosition.QUERY , false);
        request.addParam("contactPerson" , contactPerson , ParamPosition.QUERY , true);
        request.addParam("contactMobile" , contactMobile , ParamPosition.QUERY , true);
        request.addParam("ysyId" , String.valueOf(ysyId) , ParamPosition.QUERY , false);
        request.addParam("vedioResource" , String.valueOf(vedioResource) , ParamPosition.QUERY , true);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse addDeviceSyncMode(String appKey , String farmId , String house , String deviceBrand , Integer deviceType , String deviceName , String deviceSerial , String deviceModel , String contactPerson , String contactMobile , String ysyId , Integer vedioResource) {
        String path = "/open/v1/addDevice";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("farmId" , String.valueOf(farmId) , ParamPosition.QUERY , true);
        request.addParam("house" , house , ParamPosition.QUERY , false);
        request.addParam("deviceBrand" , deviceBrand , ParamPosition.QUERY , true);
        request.addParam("deviceType" , String.valueOf(deviceType) , ParamPosition.QUERY , true);
        request.addParam("deviceName" , deviceName , ParamPosition.QUERY , true);
        request.addParam("deviceSerial" , deviceSerial , ParamPosition.QUERY , false);
        request.addParam("deviceModel" , deviceModel , ParamPosition.QUERY , false);
        request.addParam("contactPerson" , contactPerson , ParamPosition.QUERY , true);
        request.addParam("contactMobile" , contactMobile , ParamPosition.QUERY , true);
        request.addParam("ysyId" , String.valueOf(ysyId) , ParamPosition.QUERY , false);
        request.addParam("vedioResource" , String.valueOf(vedioResource) , ParamPosition.QUERY , true);



        return sendSyncRequest(request);
    }
    public void addFarm(String appKey , Long userId , String farmName , String address , String telephone , String linkman , Integer scale , ApiCallback callback) {
        String path = "/open/v1/addFarm";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("userId" , String.valueOf(userId) , ParamPosition.QUERY , true);
        request.addParam("farmName" , farmName , ParamPosition.QUERY , true);
        request.addParam("address" , address , ParamPosition.QUERY , true);
        request.addParam("telephone" , telephone , ParamPosition.QUERY , true);
        request.addParam("linkman" , linkman , ParamPosition.QUERY , true);
        request.addParam("scale" , String.valueOf(scale) , ParamPosition.QUERY , false);



        sendAsyncRequest(request , callback);
    }

    public ApiResponse addFarmSyncMode(String appKey , Long userId , String farmName , String address , String telephone , String linkman , Integer scale) {
        String path = "/open/v1/addFarm";
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM , path);
        request.addParam("appKey" , appKey , ParamPosition.HEAD , true);
        request.addParam("userId" , String.valueOf(userId) , ParamPosition.QUERY , true);
        request.addParam("farmName" , farmName , ParamPosition.QUERY , true);
        request.addParam("address" , address , ParamPosition.QUERY , true);
        request.addParam("telephone" , telephone , ParamPosition.QUERY , true);
        request.addParam("linkman" , linkman , ParamPosition.QUERY , true);
        request.addParam("scale" , String.valueOf(scale) , ParamPosition.QUERY , false);



        return sendSyncRequest(request);
    }

}