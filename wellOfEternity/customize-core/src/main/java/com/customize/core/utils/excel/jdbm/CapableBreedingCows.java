package com.customize.core.utils.excel.jdbm;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CapableBreedingCows implements Serializable {
	
	/** 序号 */
	private String index;
	
	/** 母牛耳号 */
	private String cowEarNumber;
	
	/** 母牛是否在栏 */
	private String cowInTheColumn;
	
	/** 所产犊牛耳号 */
	private String calfEarNumber;
	
	/** 犊牛生产日期 */
	private String birthDate;
	
	/** 配种方式 */
	private String breedMethod;
	
	/** 是否补贴 */
	private String subsidySign;
	
	/** 犊牛是否在栏 */
	private String calfInTheColumn;
	
	public String getIndex() {
		return index;
	}
	
	public void setIndex(String index) {
		this.index = index;
	}
	
	public String getCowEarNumber() {
		return cowEarNumber;
	}
	
	public void setCowEarNumber(String cowEarNumber) {
		this.cowEarNumber = cowEarNumber;
	}
	
	public String getCowInTheColumn() {
		return cowInTheColumn;
	}
	
	public void setCowInTheColumn(String cowInTheColumn) {
		this.cowInTheColumn = cowInTheColumn;
	}
	
	public String getCalfEarNumber() {
		return calfEarNumber;
	}
	
	public void setCalfEarNumber(String calfEarNumber) {
		this.calfEarNumber = calfEarNumber;
	}
	
	public String getBirthDate() {
		return birthDate;
	}
	
	public void setBirthDate(String birthDate) {
		this.birthDate = birthDate;
	}
	
	public String getBreedMethod() {
		return breedMethod;
	}
	
	public void setBreedMethod(String breedMethod) {
		this.breedMethod = breedMethod;
	}
	
	public String getSubsidySign() {
		return subsidySign;
	}
	
	public void setSubsidySign(String subsidySign) {
		this.subsidySign = subsidySign;
	}
	
	public String getCalfInTheColumn() {
		return calfInTheColumn;
	}
	
	public void setCalfInTheColumn(String calfInTheColumn) {
		this.calfInTheColumn = calfInTheColumn;
	}
}
