package com.customize.core.enmus.common;

/**
 *"畜牧平台——>操作角色类型
 * <AUTHOR>
 */
public enum XMPTRoleInfoEnum {
	
	/**
	 * 省管理员
	 */
	PROVINCE_ADMINISTRATOR("30","PROVINCE_ADMINISTRATOR","ZHXM_ADMINISTRATOR","省管理员","ADMINISTRATOR","1", "4"),
	/**
	 * 省操作员
	 */
	PROVINCE_OPERATOR("31","PROVINCE_OPERATOR","ZHXM_OPERATOR","省操作员","OPERATOR","1", "8"),
	/**
	 * 市管理员
	 */
	CITY_ADMINISTRATOR("32","CITY_ADMINISTRATOR","ZHXM_ADMINISTRATOR","市管理员","ADMINISTRATOR","2", "4"),
	/**
	 * 市操作员
	 */
	CITY_OPERATOR("33","CITY_OPERATOR","ZHXM_OPERATOR","市操作员","OPERATOR","2", "8"),
	/**
	 * 县管理员
	 */
	COUNTY_ADMINISTRATOR("34","COUNTY_ADMINISTRATOR","ZHXM_ADMINISTRATOR","县管理员","ADMINISTRATOR","3", "4"),
	/**
	 * 县操作员
	 */
	COUNTY_OPERATOR("35","COUNTY_OPERATOR","ZHXM_OPERATOR","县操作员","OPERATOR","3", "8"),
	/**
	 * 镇管理员
	 */
	TOWN_ADMINISTRATOR("36","TOWN_ADMINISTRATOR","ZHXM_ADMINISTRATOR","镇管理员","ADMINISTRATOR","4", "4"),
	/**
	 * 镇操作员
	 */
	TOWN_OPERATOR("37","TOWN_OPERATOR","ZHXM_OPERATOR","镇操作员","OPERATOR","4", "8"),
	/**
	 * 村防疫员
	 */
	VILLAGE_IMMUNIZER("38","VILLAGE_IMMUNIZER","ZHXM_IMMUNIZER","村防疫员","IMMUNIZER","5", "9"),
	
	;
	private String roleId;
	private String roleCode;
	private String targetCode;
	private String roleName;
	private String instanceName;
	private String areaLevel;
	private String optType;
	
	public String getRoleId() {
		return roleId;
	}
	
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	
	public String getRoleCode() {
		return roleCode;
	}
	
	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}
	
	public String getTargetCode() {
		return targetCode;
	}
	
	public void setTargetCode(String targetCode) {
		this.targetCode = targetCode;
	}
	
	public String getRoleName() {
		return roleName;
	}
	
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	
	public String getInstanceName() {
		return instanceName;
	}
	
	public void setInstanceName(String instanceName) {
		this.instanceName = instanceName;
	}
	
	public String getAreaLevel() {
		return areaLevel;
	}
	
	public void setAreaLevel(String areaLevel) {
		this.areaLevel = areaLevel;
	}

	public String getOptType() {
		return optType;
	}

	public void setOptType(String optType) {
		this.optType = optType;
	}
	
	XMPTRoleInfoEnum(String roleId, String roleCode, String targetCode, String roleName, String instanceName, String areaLevel, String optType) {
		this.roleId = roleId;
		this.roleCode = roleCode;
		this.targetCode = targetCode;
		this.roleName = roleName;
		this.instanceName = instanceName;
		this.areaLevel = areaLevel;
		this.optType = optType;
	}
	
	/**
	 *
	 * @param targetCode
	 * @param areaLevel
	 * @return
	 */
	public static XMPTRoleInfoEnum getEnumInfo(String targetCode,String areaLevel) {
		for (XMPTRoleInfoEnum ele : XMPTRoleInfoEnum.values()) {
			if (ele.getTargetCode().equalsIgnoreCase(targetCode) && ele.getAreaLevel().equalsIgnoreCase(areaLevel)) {
				return ele;
			}
		}
		return null;
	}

	public static XMPTRoleInfoEnum getEnumInfoByOptTypeAreaLevel(String areaLevel, String optType){
		for (XMPTRoleInfoEnum ele : XMPTRoleInfoEnum.values()) {
			if (ele.getAreaLevel().equalsIgnoreCase(areaLevel) && ele.getOptType().equalsIgnoreCase(optType)) {
				return ele;
			}
		}
		return null;
	}
}
