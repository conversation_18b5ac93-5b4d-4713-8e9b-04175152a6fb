package com.customize.core.enmus.common;

import java.util.Arrays;

public enum CameraDeviceTypeEnum {
    YS_TYPE(1,"萤石"),
    LC_TYPE(2,"乐橙"),
    HM_TYPE(3,"和目"),
    HK_TYPE(4,"海康"),
    LC2_TYPE(5,"乐橙2"),
    HM2_TYPE(6,"和目2"),
    TP_TYPE(7,"tp-link"),
    GB_TYPE(8,"国标"),
    TY_TYPE(9,"天翼"),
    OTHER_TYPE(999,"其它设备")
    ;

    private Integer type;

    private String msg;

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    CameraDeviceTypeEnum(Integer type, String msg) {
        this.type=type;
        this.msg=msg;
    }

    public static CameraDeviceTypeEnum getEnumByType(Integer type) {
        return Arrays.stream(CameraDeviceTypeEnum.values())
                .filter(x -> x.getType().equals(type))
                .findFirst()
                .orElse(null);
    }


    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static String getType(Integer code) {
        for (CameraDeviceTypeEnum ele : CameraDeviceTypeEnum.values()) {
            if (code.equals(ele.getType())) {
                return ele.msg;
            }
        }
        return null;
    }


}
