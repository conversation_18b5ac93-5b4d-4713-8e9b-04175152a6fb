package com.customize.core.utils;


import com.core.util.WebUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023-09-11 14:14
 * @PackageName:com.xmd.xiaomuding.customize.core.utils
 * @ClassName: ProjectUtils
 * @Description: TODO
 * @Version 1.0
 */
public class ProjectUtils {
    public static Boolean isBf() {
        return StringUtils.isNotBlank(WebUtils.getRequest().getHeader("project")) && "baFu".equalsIgnoreCase(WebUtils.getRequest().getHeader("project"));
    }

}
