package com.customize.core.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @ClassName AdvancedCertificationException
 * @description 高级认证拦截器
 * @date 2021/4/6 17:01
 * @modifier zh
 * @date 2021-04-06 17:01
 * @Version V1.0
 */
public class AdvancedCertificationException extends Exception{

	@Getter
	private int errorCode;

	public AdvancedCertificationException(String msg) {
		super(msg);
		errorCode= HttpStatus.OK.value();
	}
}

