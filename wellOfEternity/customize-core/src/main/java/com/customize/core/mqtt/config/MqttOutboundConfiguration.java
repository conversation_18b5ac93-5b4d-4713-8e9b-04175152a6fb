package com.customize.core.mqtt.config;

import cn.hutool.core.util.StrUtil;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * MQTT 生产端
 * Create By Spring-2023/07/09
 *
 * <AUTHOR>
 */
@Configuration
public class MqttOutboundConfiguration {
    @Resource
    private MqttConfiguration mqttProperties;

    @Bean
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }


    /**
     * 建立MQTT连接，配置连接的参数选项
     *
     * @return
     */
    @Bean
    public MqttPahoClientFactory mqttOutClient() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        String[] mqttServerUrls = mqttProperties.getUrl().split(StrUtil.COMMA);
        MqttConnectOptions mqttConnectOptions = new MqttConnectOptions();
        mqttConnectOptions.setServerURIs(mqttServerUrls);
        mqttConnectOptions.setUserName(mqttConnectOptions.getUserName());
        mqttConnectOptions.setPassword(mqttProperties.getPassword().toCharArray());
        // 接收离线消息
        // 告诉代理客户端是否要建立持久会话   false为建立持久会话
        mqttConnectOptions.setCleanSession(false);
        factory.setConnectionOptions(mqttConnectOptions);

        return factory;
    }

    @Value("${spring.application.name}")
    private String applicationName;

    @Bean
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public MessageHandler mqttOutbound() {
        String ipAddr = "";
        try {
            ipAddr = InetAddress.getLocalHost().getHostAddress().replaceAll(StrUtil.DOT, StrUtil.EMPTY);
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
        String clientId = mqttProperties.getClientId() + StrUtil.UNDERLINE + applicationName + StrUtil.UNDERLINE + ipAddr + "_outbound";
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(clientId,
                mqttOutClient());
        // 支持主题表达式
        messageHandler.setDefaultTopic(mqttProperties.getReceiveDefaultTopics());
        messageHandler.setAsync(true);
        return messageHandler;
    }


}
