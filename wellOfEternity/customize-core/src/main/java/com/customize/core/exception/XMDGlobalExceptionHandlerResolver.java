package com.customize.core.exception;


import com.alibaba.excel.exception.ExcelAnalysisException;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.core.exception.CheckedException;
import com.core.util.Result;
import com.customize.core.aspect.ExceptionNoteAspect;
import com.idempotent.exception.IdempotentException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @ClassName: XMDGlobalExceptionHandlerResolver
 * @Description: 非业务性质抛出异常的处理器
 * @date 2023-04-19 1:21
 */
@Slf4j
@RestController
@RestControllerAdvice
public class XMDGlobalExceptionHandlerResolver {

    /**
     * @return com.xmd.xiaomuding.common.core.util.R
     * <AUTHOR>
     * @Description 全局异常处理器 自己抛出的 XMDServiceException 直接吧原错误信息放入返回对象
     * @Date 9:03 2023-04-20
     * @Param [e, request]
     */
    @ExceptionHandler(XMDServiceException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result handleGlobalXMDServiceException(XMDServiceException e) {
        if (StringUtils.isNotBlank(e.getCode())) {
            try {
                return Result.failed(null, e.getMsg(), Integer.valueOf(e.getCode()));
            } catch (Throwable val) {
            }
        }
        return Result.failed(e.getMsg());
    }

    /**
     * @return com.xmd.xiaomuding.common.core.util.R
     * <AUTHOR>
     * @Description 全局异常处理器 自己抛出的 IdempotentException 直接吧原错误信息放入返回对象
     * @Date 9:03 2023-04-20
     * @Param [e, request]
     */
    @ExceptionHandler(IdempotentException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result handleGlobalIdempotentException(IdempotentException e) {
        //TODO 幂等性返回1
        return Result.failed(null, e.getMessage(), 1);
    }

    /**
     * @return com.xmd.xiaomuding.common.core.util.R
     * <AUTHOR>
     * @Description 全局异常处理器 自己抛出的 CheckedException 直接吧原错误信息放入返回对象
     * @Date 9:03 2023-04-20
     * @Param [e, request]
     */
    @ExceptionHandler(CheckedException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result handleCheckedException(CheckedException e) {
        return Result.failed(e.getMessage());
    }


    /**
     * @return com.xmd.xiaomuding.common.core.util.R
     * <AUTHOR>
     * @Description 自定义验证异常拦截
     * @Date 9:03 2023-04-20
     * @Param [e, request]
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        return Result.failed(e.getBindingResult().getFieldError().getDefaultMessage());
    }

    @ExceptionHandler(ExcelAnalysisException.class)
    public Object handleExcelAnalysisException(ExcelAnalysisException e) {
        e.printStackTrace();
        return Result.failed("数据解析失败，请检查模板信息是否正确");
    }

    /**
     * @return com.xmd.xiaomuding.common.core.util.R
     * <AUTHOR>
     * @Description 全局异常处理器 处理非业务性质排除异常的报错问题
     * @Date 9:03 2023-04-20
     * @Param [e, request]
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Result handleGlobalException(Exception e) {
        return Result.failed((StringUtils.isNotEmpty(ExceptionNoteAspect.methodName.get()) ? ExceptionNoteAspect.methodName.get() + "失败" : "网络繁忙，请稍后再试！"));
    }

}
