package com.customize.core.enmus.warning;

import java.util.Objects;

/**
 * 摄像头告警类型
 * <AUTHOR>
 */
public enum WarningCameraTypeEnum {

	/**
	 * 离线
	 */
	OFFLINE(1,"离线"),
	/**
	 * 边界告警
	 */
	BOUNDARY_ALARM(2,"边界告警"),


	;

    private Integer value;
    private String descr;

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	WarningCameraTypeEnum(Integer value, String descr) {
		this.value = value;
		this.descr = descr;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WarningCameraTypeEnum getInstance(String value){
		for (WarningCameraTypeEnum typeEnum : WarningCameraTypeEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
				return typeEnum;
			}
		}
		return null;
	}
	
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WarningCameraTypeEnum camera = getInstance(value);
		if (Objects.nonNull(camera)){
			return camera.getDescr();
		}
		return "";
	}
}
