package com.customize.core.constant;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description Redis key值
 * @Date : 2022/2/11 17:34
 **/
@Data
public class RedisKeysConstant {
    /**
     * 用户登录token
     */
    public static String USER_TOKEN_KEY = "USER_TOKEN_KEY:";
    /**
     * 阿里消息推送设备id
     */
    public static String ALI_PUSH_DEVICE_ID_KEY = "ali_push_device_id_key:";
    /**
     * 阿里消息已推送标记
     */
    public static String ALI_PUSH_DEVICE_STATUS_KEY = "ali_push_device_status_key:";
    /**
     * 短信验证码 redis key
     */
    public static String CODE_REDIS_KEY = "SMS-CODE-TYPE:";
    /**
     * 弹窗已读标记
     */
    public static String POPUP_WINDOW_KEY = "POPUP_WINDOW_KEY:";
    /**
     *  用户阅读公告记录 Map形式存储
     */
    public static String USER_READ_NOTICE = "USER_READ_NOTICE:";
    /**
     *  用户设备所在地区json
     */
    public static String USER_DEVICE_PCA_JSON = "USER_DEVICE_PCA_JSON:";
    /**
     * 第三方token
     */
    public static String TOKEN_REDIS_KEY = "TOKEN_REDIS_KEY:";
    /**
     * 区域地址 id下所有子集id
     */
    public static String PCA_PID_REDIS_KEY = "PCA_PID_REDIS_KEY:";
    /**
     * 测温基站在线状态
     */
    public static String TEMP_MAC_ONLINE_REDIS_KEY = "TEMP:TEMP_MAC_ONLINE_REDIS_KEY:";
    /**
     * socket通道连接mac
     */
    public static String SOCKET_MAC_REDIS_KEY = "SOCKET:SOCKET_MAC_REDIS_KEY:";
    /**
     * socket通道连接mac 在线状态
     */
    public static String SOCKET_MAC_ONLINE_REDIS_KEY = "SOCKET:SOCKET_MAC_ONLINE_REDIS_KEY:";
    /**
     * socket通道连接mac MSG
     */
    public static String SOCKET_MAC_MSG_REDIS_KEY = "SOCKET:SOCKET_MAC_MSG_REDIS_KEY:";
    /**
     * socket通道称连接mac MSG
     */
    public static String SOCKET_MAC_MSG_FLAG_REDIS_KEY = "SOCKET:SOCKET_MAC_MSG_FLAG_REDIS_KEY:";
    /**
     * socket通道地址绑定mac
     */
    public static String SOCKET_CHANNEL_MAC_REDIS_KEY = "SOCKET:SOCKET_CHANNEL_MAC_REDIS_KEY:";
    /**
     * AI盘点状态
     */
    public static String SOCKET_AI_DEVICE_REDIS_KEY = "SOCKET:SOCKET_AI_DEVICE_REDIS_KEY:";
    /**
     * 车辆称重数据
     */
    public static String SOCKET_CAR_WEIGHT_DATA_REDIS_KEY = "SOCKET:SOCKET_CAR_WEIGHT_DATA_REDIS_KEY:";
    /**
     * 车辆耳标所在棚数据
     */
    public static String SOCKET_CAR_SHED_DATA_REDIS_KEY = "SOCKET:SOCKET_CAR_SHED_DATA_REDIS_KEY:";
    /**
     * 车辆GPS数据
     */
    public static String SOCKET_CAR_GPS_DATA_REDIS_KEY = "SOCKET:SOCKET_CAR_GPS_DATA_REDIS_KEY:";
    /**
     * 设置RFID射频频率
     */
    public static String SOCKET_SET_THE_RF_FREQUENCY_REDIS_KEY = "SOCKET:SOCKET_SET_THE_RF_FREQUENCY_REDIS_KEY:";
    /**
     * 传感设备数据
     */
    public static String SOCKET_SENSING_DEVICE_DATA_REDIS_KEY = "SOCKET:SOCKET_SENSING_DEVICE_DATA_REDIS_KEY:";
	/**
	 * socket通道连接mac weight mac和称重
	 */
	public static String SOCKET_MAC_ONLY_WEIGHT_REDIS_KEY = "SOCKET:SOCKET_MAC_ONLY_WEIGHT_REDIS_KEY:";
	/**
	 * 开通算法盘点设备
	 */
	public static String ALGORITHM_OPEN_CHECK_DEVICE_KEY = "ALGORITHM:OPEN_CHECK_DEVICE_KEY:";

	/**
	 * 牧场GPS redis key
	 */
	public static String FARM_GPS_INFO_KEY = "FARM_GPS_INFO_KEY:";

	public static String USER_BEE_APP_INFO = "BEE:USER_BEE_APP_INFO:";

	public static String USER_BEE_APP_SECRET_KEY= "BEE:USER_BEE_APP_SECRET_KEY:";
	public static String PROCESSING_EAR_LIST= "PROCESSING:EAR_LIST:";
}
