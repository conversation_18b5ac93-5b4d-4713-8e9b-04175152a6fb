package com.customize.core.aspect;


import com.alibaba.fastjson.JSONObject;
import com.core.exception.CheckedException;
import com.core.util.WebUtils;
import com.customize.core.annotation.DistributedLock;
import com.customize.core.constant.CustomizedCoreConstant;
import com.customize.core.utils.MD5Utils;
import com.customize.core.utils.RedisUtils;
import com.customize.core.utils.RedissonUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.hutool.core.text.StrPool.COLON;

/**
 * <AUTHOR>
 * @ClassName: DistributedLockAspect
 * @Description: 分布式索自定义注解解析器
 * @date 2023-04-19
 */
@Aspect
@Component
@Slf4j
public class DistributedLockAspect {

    private final String  IDEMPOTENT_REDIS_KEY = "AUTO_IDEMPOTENT:";
    /**
     * 切入点为所有的@DistributedLock 注解方法
     */
    @Pointcut("@annotation(com.customize.core.annotation.DistributedLock)")
    private void serviceAspect() {
    }

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description 主要切入逻辑
     * @Date 2023-04-19
     * @Param [point]
     */
    @SneakyThrows
    @Around("serviceAspect()")
    public Object around(ProceedingJoinPoint point) {
        // 从切点获取目标方法
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        // 获取目标方法上的@DistributedLock注解
        DistributedLock redisLock = method.getAnnotation(DistributedLock.class);

        // 生成Redis锁的键
        // 如果注解中指定了key，则使用该key；否则，默认使用请求URI和方法参数的MD5值拼接作为key
        // 如果注解中指定了key，则使用该key；否则，默认使用请求URI和方法参数的MD5值拼接作为key
        String key = StringUtils.isNotEmpty(redisLock.key())
                ? redisLock.key()
                : WebUtils.getRequest().getRequestURI()
                .concat(COLON)
                .concat(createKey(point))
                .concat(COLON)
                .concat(MD5Utils.MD5Str(Arrays.asList(point.getArgs()).toString()));
        // 构建完整的Redis锁键，格式为："DISTRIBUTED_LOCK:方法名:key"
        String redisLockKey = CustomizedCoreConstant.DISTRIBUTED_LOCK.concat(method.getName())
                .concat(CustomizedCoreConstant.COLON)
                .concat(key.contains(CustomizedCoreConstant.HASH)
                        ? getParamsKey(point, key.replace(CustomizedCoreConstant.HASH, CustomizedCoreConstant.BLACK).split(CustomizedCoreConstant.DOT))
                        : key);
        //TODO 新逻辑 如果不需要等待锁资源 直接抛出 就當是冪等性操作 START
        if (!redisLock.needWait()) {
            String diKey = IDEMPOTENT_REDIS_KEY.concat(redisLockKey);
            if (RedisUtils.isExist(diKey)) {
                throw new CheckedException("请勿重复操作");
            }
            RedisUtils.set(diKey, "", redisLock.waitTime(), TimeUnit.SECONDS);
        }
        // TODO 新逻辑 如果不需要等待锁资源 直接抛出 就當是冪等性操作 END
        // 尝试获取分布式锁
        boolean lock = RedissonUtils.getTryLock(redisLockKey, TimeUnit.SECONDS, redisLock.waitTime(), redisLock.leaseTime());
        log.info("根据Key:【{}】 尝试获取锁资源结果:【{}】 ", redisLockKey, lock);
        try {
            if (lock) {
                // 成功获取到锁，执行业务逻辑
                return point.proceed();
            }
            // 未能获取到锁，抛出异常
            throw new CheckedException("分布式锁获取失败");
        } finally {
            // 最后释放锁资源
            if (lock) {
                RedissonUtils.unlock(redisLockKey);
            }
        }
    }


    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 动态获取配置的key值  @eg:a.b.c.d
     * @Date 2023-04-19
     * @Param [point, regexParams]
     */
    public String getParamsKey(ProceedingJoinPoint point, String[] regexParams) throws Exception {
        if (regexParams.length == 1) {
            String classType = point.getTarget().getClass().getName();
            String methodName = point.getSignature().getName();
            // 参数值
            Object[] args = point.getArgs();
            Class<?>[] classes = new Class[args.length];
            for (int k = 0; k < args.length; k++) {
                if (!args[k].getClass().isPrimitive()) {
                    // 获取的是封装类型而不是基础类型
                    String result = args[k].getClass().getName();
                    Class s = CustomizedCoreConstant.PRIMIVATE_MAP.get(result);
                    classes[k] = s == null ? args[k].getClass() : s;
                }
            }
            ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();
            // 获取指定的方法，第二个参数可以不传，但是为了防止有重载的现象，还是需要传入参数的类型
            Method method = Class.forName(classType).getMethod(methodName, classes);
            // 参数名
            String[] parameterNames = pnd.getParameterNames(method);
            // 通过map封装参数和参数值
            for (int i = 0; i < parameterNames.length; i++) {
                if (parameterNames[i].equalsIgnoreCase(regexParams[0])) {
                    return (String) args[i];
                }
            }
        }
        //获取到方法参数名称
        //查询参数名称是否有匹配的参数 没有返回null
        Object obj = Arrays.stream(point.getArgs())
                .filter(arg -> arg.getClass().getSimpleName().equalsIgnoreCase(regexParams[0]))
                .findFirst()
                .orElseThrow(() -> new CheckedException("没有匹配的参数值,请检查RedisLock key()"));
        //获取改参数的所有属性 查找下一个符合条件的参数
        //获取参数值 返回
        return getParamsKey(obj, obj.getClass().getDeclaredFields(), regexParams, 1);
    }


    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 动态获取配置的key值
     * @Date 2023-04-19
     * @Param [obj, fields, regexParams, i]
     */
    @SneakyThrows
    public String getParamsKey(Object obj, Field[] fields, String[] regexParams, int i) {
        // 如果当前索引i超过了regexParams数组的长度，抛出异常，因为这意味着没有匹配到相应的参数
        if (i > regexParams.length) {
            throw new CheckedException("没有匹配的参数值,请检查RedisLock key()");
        }
        // 获取当前索引i对应的参数名称
        String paramName = regexParams[i];
        // 从obj对象的字段中查找名称与paramName匹配的字段
        Field field = Arrays.stream(fields)
                .filter(f -> f.getName().equals(paramName)) // 筛选字段名等于paramName的字段
                .findFirst() // 找到第一个匹配的字段
                .orElseThrow(() -> new CheckedException("没有匹配的参数值,请检查RedisLock key()")); // 如果没有找到匹配的字段，抛出异常
        // 设置私有字段可访问
        field.setAccessible(true);
        // 如果当前索引i是regexParams数组的最后一个元素，说明已经到达递归的最底层
        // 此时，使用MD5算法对字段值进行加密并返回加密后的字符串
        // 如果不是最后一个元素，递归调用getParamsKey方法，继续处理下一个字段
        // 在递归调用中，obj参数变为当前字段的值，fields参数变为该字段类型的所有声明字段
        return i == regexParams.length - 1
                ? MD5Utils.MD5Str(String.valueOf(field.get(obj)))
                : getParamsKey(field.get(obj), field.getType().getDeclaredFields(), regexParams, ++i);
    }

    /**
     * 获取唯一的key值 根据参数body体等信息 TODO 这个时候可自定义 也有可能是登录用户的token信息等等
     *
     */
    public String createKey(ProceedingJoinPoint point) {
        ExceptionNoteAspect.init(point);
        Object requestBody = ExceptionNoteAspect.requestBody.get();
        Object requestParams = ExceptionNoteAspect.requestParams.get();
        String requestBodyM5 = MD5Utils.MD5Str(Objects.isNull(requestBody) ? "" : requestBody.toString());
        String requestParamsM5 = MD5Utils.MD5Str(Objects.isNull(requestParams) ? "" : requestParams.toString());
        return requestBodyM5.concat(requestParamsM5);
    }



    public static JSONObject replaceVariables(String input, Map<String, String> variables) {
        // 定义匹配${}格式的正则表达式
        Pattern pattern = Pattern.compile("\\$\\{([^}]*)\\}");
        Matcher matcher = pattern.matcher(input);
        StringBuffer buffer = new StringBuffer();
        JSONObject jsonObject=new JSONObject();
        // 循环匹配并替换变量值
        while (matcher.find()) {
            String variableName = matcher.group(1);
            String variableValue = variables.get(variableName);
            // 如果找到对应的变量值，则替换，否则保留原始字符串
            if (variableValue != null) {
                jsonObject.put(variableName,variableValue);
                matcher.appendReplacement(buffer, variableValue);
            } else {
                // 如果找不到对应的变量值，则保留原始字符串
                matcher.appendReplacement(buffer, matcher.group(0));
            }
        }
        matcher.appendTail(buffer);
        return jsonObject;
    }

    public static void main(String[] args) {
        // 示例输入字符串
//        String input = "尊敬的用户${userName}您好，您的牧场${farmName}母牛畜只录入权限已由信息员${optName}处理。";
//        // 示例变量值
//        Map<String, String> variables = new HashMap<>();
//        variables.put("userName","张三");
//        variables.put("farmName","牧牛场");
//        variables.put("optName","李四");
//        // 调用替换方法并输出结果
//        System.out.println(replaceVariables(input, variables));
        System.out.println(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
    }

}