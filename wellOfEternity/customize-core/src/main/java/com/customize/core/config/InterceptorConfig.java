package com.customize.core.config;


import com.customize.core.interceptor.AccessLimitInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * * @date 2023/2/13
 * * Describe:
 * 拦截器配置
 */

@Configuration
@Import(AccessLimitInterceptor.class)
public class InterceptorConfig implements WebMvcConfigurer {
	@Resource
	private AccessLimitInterceptor accessLimitInterceptor;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(accessLimitInterceptor).addPathPatterns("/*/**");

	}
}

