package com.customize.core.enmus.warning;

import java.util.Objects;

/**
 * 告警登记
 * <AUTHOR>
 */
public enum WarningLevelEnum {
	
	/**
	 * 一级告警
	 */
	ONE_LEVEL_WARNING(1,"一级告警"),
	/**
	 * 二级告警
	 */
	TWO_LEVEL_WARNING(2,"二级告警"),
	/**
	 * 三级告警
	 */
	THREE_LEVEL_WARNING(3,"三级告警"),
	/**
	 * 四级告警
	 */
	FOUR_LEVEL_WARNING(4,"四级告警"),
	
	;

    private Integer value;
    private String descr;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getDescr() {
		return descr;
	}
	
	public void setDescr(String descr) {
		this.descr = descr;
	}
	
	WarningLevelEnum(Integer value, String descr) {
		this.value = value;
		this.descr = descr;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WarningLevelEnum getInstance(String value){
		for (WarningLevelEnum typeEnum : WarningLevelEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
				return typeEnum;
			}
		}
		return null;
	}
	
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WarningLevelEnum levelEnum = getInstance(value);
		if (Objects.nonNull(levelEnum)){
			return levelEnum.getDescr();
		}
		return "";
	}
	
}
