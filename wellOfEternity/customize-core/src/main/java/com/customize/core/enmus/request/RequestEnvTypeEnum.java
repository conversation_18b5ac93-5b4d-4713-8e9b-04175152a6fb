package com.customize.core.enmus.request;

/**
 * <AUTHOR> admin
 * @date : 2023/5/23
 * @description : 请求环境：dev，test，uat，prod
 */
public enum RequestEnvTypeEnum {
	/**
	 * 开发环境
	 */
	DEVELOPMENT_ENVIRONMENT("dev","开发环境"),
	/**
	 * 测试环境
	 */
	TEST_ENVIRONMENT("test","测试环境"),
	/**
	 * 生产环境
	 */
	PRODUCT_ENVIRONMENT("prod","生产环境"),
	;
	
	/**
	 *  请求环境
	 */
	private String requestEnv;
	/**
	 * 请求环境描述
	 */
	private String requestEnvDesc;
	
	public String getRequestEnv() {
		return requestEnv;
	}
	
	public void setRequestEnv(String requestEnv) {
		this.requestEnv = requestEnv;
	}
	
	public String getRequestEnvDesc() {
		return requestEnvDesc;
	}
	
	public void setRequestEnvDesc(String requestEnvDesc) {
		this.requestEnvDesc = requestEnvDesc;
	}
	
	RequestEnvTypeEnum(String codeType, String codeDesc) {
		this.requestEnv = codeType;
		this.requestEnvDesc = codeDesc;
	}
	
	/**
	 * Gets enum.
	 * @param requestEnv the code
	 * @return the enum
	 */
	public static RequestEnvTypeEnum getEnvironment(String requestEnv) {
		for (RequestEnvTypeEnum typeEnum : RequestEnvTypeEnum.values()) {
			if (requestEnv.equalsIgnoreCase(typeEnum.getRequestEnv())) {
				return typeEnum;
			}
		}
		return null;
	}
}
