package com.customize.core.aspect;


import com.customize.core.utils.RequestHeaderParamsClient;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-09-22 11:38
 * @PackageName:com.xmd.xiaomuding.project.server.interceptor.aop
 * @ClassName: AuthAspect
 * @Description: TODO
 * @Version 1.0
 */
@Aspect
@Component
@Slf4j
public class HeaderParamsAspect {


    /**
     * 定义切点
     */
    @Pointcut("@annotation(io.swagger.v3.oas.annotations.Operation)")
    public void pot() {
    }

    @Around("pot()")
    // 在目标方法执行之前执行的逻辑
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 初始化请求头参数
            // 这里假设RequestHeaderParamsClient是一个负责管理请求头参数的客户端
            // initParams方法可能是设置一些线程局部变量，用于在整个请求周期内保存请求头信息
            RequestHeaderParamsClient.initParams();
            // 继续执行目标方法，即允许业务逻辑继续进行
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            // 如果在执行目标方法过程中抛出了异常，打印异常堆栈信息
            throwable.printStackTrace();
            // 并重新抛出异常，确保异常不被吞没，可以被上层逻辑捕获处理
            throw throwable;
        } finally {
            // 在目标方法执行完毕后，无论成功还是异常结束，都执行清理操作
            // removeParams方法可能是清理线程局部变量，确保不会对后续的请求产生影响
            RequestHeaderParamsClient.removeParams();
        }
    }



}
