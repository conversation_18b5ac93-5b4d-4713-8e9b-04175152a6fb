package com.customize.core.aspect;

import cn.hutool.core.util.ObjectUtil;
import com.customize.core.annotation.OperateLog;
import com.customize.core.entity.OperateLogEntity;
import com.customize.core.enmus.StatusEnums;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName: LogAspect
 * @Description: 拦截日志切面
 * @date 2023-04-19
 */
@Aspect
@Component
@Slf4j
public class LogAspect {


    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description 切面逻辑
     * @Date 11:07 2023-04-25
     * @Param [pjp, logAnno]
     */
    @Around("@annotation(logAnno)")
    // 使用AOP拦截带有@OperateLog注解的方法，进行日志记录
    public Object doSaveMethodName(ProceedingJoinPoint pjp, OperateLog logAnno) throws Throwable {
        // 初始化日志实体，这可能包括从注解中提取信息和从切点获取方法签名等信息
        OperateLogEntity logEntity = OperateLogEntity.init(logAnno, pjp);
        try {
            // 执行被拦截的方法
            Object result = pjp.proceed();
            // 如果方法执行成功，记录方法的返回值
            logEntity.setRequestBody(ObjectUtil.isEmpty(result) ? null : result.toString());
            // 返回方法的执行结果
            return result;
        } catch (Throwable e) {
            // 如果方法执行过程中抛出异常，记录错误信息
            logEntity.setErrorMsg(e.getMessage());
            // 将日志实体的状态设置为失败
            logEntity.setStatus(StatusEnums.ERROR.toString());
            // 将异常继续向上抛出，不影响原有的异常处理逻辑
            throw e;
        } finally {
            // 无论方法执行成功还是失败，都记录日志信息
            log.debug("======logEntity====:【{}】", logEntity);
            // TODO: 实现日志实体的持久化逻辑，将日志信息保存到数据库或日志系统中
        }
    }

}