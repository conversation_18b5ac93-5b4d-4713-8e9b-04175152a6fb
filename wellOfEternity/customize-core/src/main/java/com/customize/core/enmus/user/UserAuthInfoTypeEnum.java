package com.customize.core.enmus.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @Description 认证信息类型
* @Return
**/
@AllArgsConstructor
@Getter
public enum UserAuthInfoTypeEnum {
    //1、贩运人 2、经纪人 3、推荐人 4、交易市场 5、防疫员 6、政府用户 7、农技专家 8、金融机构 9、农资商家 10、养殖场 11、屠宰场

    TRAFFIC(1,"贩运人"),
    AGENT(2,"经纪人"),
    REFEREES(3,"推荐人"),
    MARKET_PLACE(4,"交易市场"),
    FANG_YI_YUAN(5,"防疫员"),
    GOVERNMENT(6,"政府用户"),
    AGRICULTURE_EXPERT(7,"农技专家"),
    FINANCIAL_INSTITUTIONS(8,"金融机构"),
    AGRICULTURAL(9,"农资商家"),
    FARM(10,"养殖场"),
    SLAUGHTER_HOUSE(11,"屠宰场"),
	/**
	 * 见犊补母
	 */
	JDBM_AUTH(12,"见犊补母"),

    /**
     * 畜产品管理
     */
    XCPGL_AUTH(13,"畜产品管理"),
    /**
     * 智慧养蜂
     */
    ZHYF_AUTH(14,"智慧养蜂"),
    /**
     * 见犊补母
     */
    DBBF_AUTH(15,"定边八福"),
    /**
     * 子洲畜牧
     */
    XMPT_AUTH(16,"畜牧平台"),
    /**
     * 疫病监测平台
     */
    YBJCPT_AUTH(17,"疫病监测平台"),
    /**
     * 小牧丁助贷
     */
    XMDJRZD_AUTH(18,"小牧丁金融助贷"),
    /**
     * 智慧动监
     */
    ZHDJPT_AUTH(19,"智慧动监"),
    
    ;

    private Integer value;
    private String description;

    public static UserAuthInfoTypeEnum getValueOf(Integer value){
        for (UserAuthInfoTypeEnum val: UserAuthInfoTypeEnum.values()) {
            if(value.equals(val.value)){
                return val;
            }
        }
        return null;
    }
    public static String getDesc(String value){
        for (UserAuthInfoTypeEnum val: UserAuthInfoTypeEnum.values()) {
            if(value.trim().equals(val.value+"")){
                return val.description;
            }
        }
        return null;
    }
}