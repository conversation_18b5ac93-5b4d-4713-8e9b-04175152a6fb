package com.customize.core.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface CacheLock {

    /**
     * redis 锁key的前缀
     */
    String prefix() default "";

    /**
     * redis key过期时间
     */
    int expire() default 5;

    /**
     * 超时时间单位
     *
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * Key分隔符
     * 比如：Key:1
     */
    String delimiter() default ":";
}
