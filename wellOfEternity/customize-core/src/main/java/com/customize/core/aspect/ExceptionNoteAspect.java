package com.customize.core.aspect;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.core.exception.CheckedException;
import com.core.util.WebUtils;
import com.customize.core.entity.LogExceptionEntity;
import com.customize.core.exception.XMDServiceException;
import com.customize.core.fifter.RequestWrapper;
import com.customize.core.mapper.LogExceptionMapper;
import com.idempotent.exception.IdempotentException;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.MethodArgumentNotValidException;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: MethodFindAspect
 * @Description: 异常方法属性文案捕获切面
 * @date 2023-04-19 1:34
 * 测试接口 com.xmd.xiaomuding.common.core.controller.CustomizedCoreTestController#ExceptionThrowTest()
 */
@Aspect
@Service
@Slf4j
public class ExceptionNoteAspect {


    @Value("${spring.application.name}: ")
    private String applicationName;

    public static InheritableThreadLocal<String> methodName = new InheritableThreadLocal<>();
    public static InheritableThreadLocal<Object> requestParams = new InheritableThreadLocal<>();
    public static InheritableThreadLocal<Object> requestHeaders = new InheritableThreadLocal<>();
    public static InheritableThreadLocal<Object> requestBody = new InheritableThreadLocal<>();
    public static InheritableThreadLocal<String> errorMsg = new InheritableThreadLocal<>();
    public static InheritableThreadLocal<String> stackMessage = new InheritableThreadLocal<>();
    public static InheritableThreadLocal<String> aName = new InheritableThreadLocal<>();

    /**
     * @return void
     * <AUTHOR>
     * @Description 切入点 带有ApiOperation方法的控制层
     * @Date 9:00 2023-04-19
     * @Param []
     */
    @Pointcut("@annotation(io.swagger.v3.oas.annotations.Operation)")
    public void controller() {

    }

    @Pointcut("@annotation(org.springframework.web.bind.annotation.ExceptionHandler)")
    public void exceptionAspect() {
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 切入逻辑 带有ApiOperation方法的控制层
     * @Date 9:00 2023-04-19
     * @Param []
     */
    @Around("controller()")
    public Object doSaveMethodName(ProceedingJoinPoint pjp) throws Throwable {
        try {
            return pjp.proceed();
        } catch (Throwable e) {
            saveErrorParams(pjp);
            throw e;
        }
    }

    /**
     * 异常前置处理 打印调用、异常 日志
     *
     * @param joinPoint
     */
    @Before("exceptionAspect()")
    // 定义一个切面方法，用于捕获方法执行过程中抛出的异常
    public void exceptionBefore(JoinPoint joinPoint) {
        // 设置当前应用名称到线程局部变量
        aName.set(applicationName);
        // 获取方法的第一个参数，假设它是异常对象
        Object ex = joinPoint.getArgs()[0];
        // 记录错误日志，包括请求的URL、请求参数、请求头和请求体
        log.error("########接口【{}】访问出现异常 --- 请求参数【{}】--- 请求头【{}】 --- 请求体【{}】",
                WebUtils.getRequest().getRequestURL(), requestParams.get(), requestHeaders.get(), requestBody.get());

        // 根据异常类型进行分类处理
        if (ex instanceof XMDServiceException) {
            // 如果是XMDServiceException异常，记录异常信息和堆栈信息
            String message = (((XMDServiceException) ex).getMsg());
            errorMsg.set("XMDServiceException");
            stackMessage.set(message);
        } else if (ex instanceof CheckedException) {
            // 如果是CheckedException异常，记录异常信息和堆栈信息
            String message = (((CheckedException) ex).getMessage());
            errorMsg.set("CheckedException");
            stackMessage.set(message);
        } else if (ex instanceof IdempotentException) {
            // 如果是IdempotentException异常，记录异常信息和堆栈信息
            String message = ((IdempotentException) ex).getMessage();
            errorMsg.set("IdempotentException");
            stackMessage.set(message);
        } else if (ex instanceof MethodArgumentNotValidException) {
            // 如果是MethodArgumentNotValidException异常，记录异常信息和堆栈信息
            String defaultMessage = ((MethodArgumentNotValidException) ex).getBindingResult().getFieldError().getDefaultMessage();
            errorMsg.set("MethodArgumentNotValidException");
            stackMessage.set(defaultMessage);
        } else if (ex instanceof BadSqlGrammarException) {
            // 如果是BadSqlGrammarException异常，记录异常信息
            errorMsg.set("BadSqlGrammarException");
            stackMessage.set(((BadSqlGrammarException) ex).getMessage());
        } else {
            // 对于其他类型的异常，记录异常信息和堆栈信息
            errorMsg.set(((Exception) ex).getMessage());
            stackMessage.set(Arrays.asList(((Exception) ex).getStackTrace()).toString());
            ((Exception) ex).printStackTrace();
        }
        // 记录错误信息和堆栈信息到日志
        log.error("################--ERROR_MESSAGE:【{}】", errorMsg.get());
        log.error("################--STACK_MESSAGE:【{}】", stackMessage.get());
        // 执行保存错误日志的方法
        doSaveErrorLog();
    }


    /**
     * 异常后置处理 移除线程副本
     */
    @AfterReturning("exceptionAspect()")
    // 在方法执行完毕后调用，用于清理线程局部变量
    public void exceptionAfterReturning() {
        // 清除存储方法名称的线程局部变量，以避免内存泄露或数据污染
        methodName.remove();
        // 清除存储请求参数的线程局部变量
        requestParams.remove();
        // 清除存储请求头信息的线程局部变量
        requestHeaders.remove();
        // 清除存储请求体的线程局部变量
        requestBody.remove();
        // 清除存储错误信息的线程局部变量
        errorMsg.remove();
        // 清除存储堆栈信息的线程局部变量
        stackMessage.remove();
        // 清除存储应用名称的线程局部变量
        aName.remove();
    }



    // 保存错误发生时的请求参数、请求头、请求体信息，并尝试从@Operation注解获取API描述
    public void saveErrorParams(ProceedingJoinPoint pjp) {
        // 保存请求参数到线程局部变量
        saveParameterMap();
        // 保存请求头到线程局部变量
        saveHeader();
        // 保存请求体到线程局部变量
        saveBody(pjp); // 此方法需要传入ProceedingJoinPoint对象
        // 尝试从方法签名获取@Operation注解，并设置API描述或摘要到线程局部变量methodName
        Optional.of((MethodSignature) pjp.getSignature()) // 将pjp.getSignature()转换为MethodSignature
                .map(MethodSignature::getMethod) // 获取方法对象
                .filter(method -> method.isAnnotationPresent(Operation.class)) // 筛选带有@Operation注解的方法
                .map(method -> method.getAnnotation(Operation.class)) // 获取@Operation注解实例
                .ifPresent(apiOperation ->
                        methodName.set(StringUtils.isNotEmpty(apiOperation.description())
                                ? apiOperation.description() // 如果@Operation注解的description属性不为空，则使用description
                                : apiOperation.summary())); // 否则，使用summary属性
    }


    /**
     * 设置params
     */
    // 保存当前HTTP请求的参数到线程局部变量
    public static void saveParameterMap() {
        // 创建一个新的HashMap来存储请求参数
        Map<String, String> params = new HashMap<>();
        // 获取当前请求对象，并遍历其参数映射
        WebUtils.getRequest().getParameterMap().forEach((k, v) ->
                // 将每个参数的值数组转换为一个由逗号分隔的字符串，然后存储到Map中
                // 这里假设同一个参数名可能对应多个值，因此使用String.join来合并数组元素
                params.put(k, String.join(",", v))
        );
        // 将参数Map存储到线程局部变量requestParams中
        // 这样，无论请求在处理过程中的哪个点，都可以访问到这些参数
        requestParams.set(params);
    }


    /**
     * 设置header
     */
    // 保存当前HTTP请求的所有头信息到线程局部变量
    public void saveHeader() {
        // 获取当前请求对象
        HttpServletRequest request = WebUtils.getRequest();
        // 创建一个新的HashMap来存储请求头信息
        Map<String, String> headers = new HashMap<>();
        // 获取请求中所有头信息的名称
        Enumeration<String> headerNames = request.getHeaderNames();
        // 遍历头信息名称
        while (headerNames.hasMoreElements()) {
            // 获取下一个头信息的名称
            String h = headerNames.nextElement();
            // 根据头信息的名称获取其值，并将名称和值存入之前创建的Map中
            headers.put(h, request.getHeader(h));
        }
        // 将包含所有头信息的Map存储到线程局部变量requestHeaders中
        // 这样，无论请求在处理过程中的哪个点，都可以方便地访问到这些头信息
        requestHeaders.set(headers);
    }


    /**
     * 设置body
     */
    // 保存HTTP请求体或方法参数到线程局部变量
    public static void saveBody(ProceedingJoinPoint pjp) {
        // 尝试获取请求体内容
        String body = getRequestBody();
        // 如果请求体内容为空，则将方法的参数作为请求体内容保存
        // 否则，直接保存请求体内容
        // 这里使用了StringUtils工具类来检查body是否为空，如果为空，则使用pjp.getArgs()获取方法参数
        // 并将其转换为列表形式保存，如果不为空，则直接保存body字符串
        requestBody.set(StringUtils.isEmpty(body) ? Arrays.asList(pjp.getArgs()).toString() : body);
    }


    @Async
    // 保存错误日志信息到数据库
    public void doSaveErrorLog() {
        try {
            // 创建一个新的异常日志实体
            LogExceptionEntity exception = new LogExceptionEntity();
            // 设置异常日志的唯一ID，这里使用IdWorker工具类生成ID
            exception.setId(IdWorker.getId());
            // 设置请求URL，包括应用名称和请求的URI
            exception.setRequestUrl(aName.get().concat(":").concat(WebUtils.getRequest().getRequestURI()));
            // 设置请求的方法名称，可能是API的描述或摘要
            exception.setRequestMethodName(methodName.get());
            // 设置请求头信息，将Map转换为字符串
            exception.setRequestHeader(requestHeaders.get().toString());
            // 设置请求参数，将Map转换为字符串
            exception.setRequestParam(requestParams.get().toString());
            // 设置请求体信息
            exception.setRequestBody(requestBody.get().toString());
            // 设置错误消息
            exception.setErrorMsg(errorMsg.get().toString());
            // 设置错误内容，通常是异常的堆栈信息
            exception.setErrorContent(stackMessage.get().toString());
            // 设置记录的创建时间为当前时间
            exception.setCreateTime(LocalDateTime.now());
            // 设置处理状态，这里使用0表示未处理
            exception.setDisposeStatus(0);
            // 使用SpringUtil工具类获取LogExceptionMapper的bean，并执行插入操作
            SpringUtil.getBean(LogExceptionMapper.class).insert(exception);
        } catch (Exception e) {
            // 在这里捕获所有异常，避免因保存日志失败而影响正常业务，这里选择不做任何处理
        }
    }


    /**
     * 获取requestBody
     */
    public static String getRequestBody() {
        try {
            return new RequestWrapper(WebUtils.getRequest()).getBody();
        } catch (Exception e) {
            return null;
        }
    }

    public static void init(ProceedingJoinPoint point) {
        saveBody(point);
        saveParameterMap();
    }


}
