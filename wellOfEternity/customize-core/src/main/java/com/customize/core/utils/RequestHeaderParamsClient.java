package com.customize.core.utils;


import com.core.util.WebUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.Enumeration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-10-17 9:56
 * @PackageName:com.xmd.xiaomuding.common.security.dto
 * @ClassName: RequestHeaderParams
 * @Description: TODO
 * @Version 1.0
 */
@Getter
public class RequestHeaderParamsClient {

    private static final ThreadLocal<HeaderParamsModel> headers = new InheritableThreadLocal<>();


    /**
     * 对外暴露的方法
     */
    public static HeaderParamsModel getHeaders() {
        return Objects.isNull(headers.get()) ? new HeaderParamsModel() : headers.get();
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description //方法初始化
     * @Date 10:55 2023-11-02
     * @Param []
     */
    public static void initParams() {
        try {
            HeaderParamsModel model = new HeaderParamsModel();
            HttpServletRequest request = WebUtils.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                Field[] declaredFields = HeaderParamsModel.class.getDeclaredFields();
                for (Field declaredField : declaredFields) {
                    declaredField.setAccessible(Boolean.TRUE);
                    if (declaredField.getName().equalsIgnoreCase(headerName)) {
                        String header = request.getHeader(headerName);
                        if (StringUtils.isNotBlank(header)) {
                            declaredField.set(model, header);
                        }
                    }
                }
            }
            headers.set(model);
        } catch (Exception e) {

        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //副本移除
     * @Date 10:55 2023-11-02
     * @Param []
     */
    public static void removeParams() {
        if (!Objects.isNull(headers)) {
            headers.remove();
        }
    }


    @Setter
    @Getter
    public static class HeaderParamsModel {
        private String applicationId;
        private String applicationDeptId;
        private String livestockType;
        private String deptId;
        private String openRealAreaId;
        /**
         * 
         */
        private String clientType;
        /**
         * 2.发送请求的环境(requestEnv): test, prod,
         */
        private String requestEnv;
        /**
         * 3.设备类型(requestDeviceType): xiaomi,oppo,huawei
         */
        private String requestDeviceType;
        /**
         * 4.版本号(appVersion): version
         */
        private String appVersion;
    }

}
