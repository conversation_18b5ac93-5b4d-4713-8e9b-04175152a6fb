package com.customize.core.enmus.message;

/**
 * '是否已读 0已读 1未读'
 */
public enum ReadMsgTypeEnum {
    READ(0,"已读"),

    UNREAD(1,"未读");

    private Integer type;

    private String msg;

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    ReadMsgTypeEnum(Integer type, String msg) {
        this.type=type;
        this.msg=msg;

    }
}
