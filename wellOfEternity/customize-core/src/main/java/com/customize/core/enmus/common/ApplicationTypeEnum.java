package com.customize.core.enmus.common;

/**
 *"操作角色类型
 * <AUTHOR>
 */
public enum ApplicationTypeEnum{
	
	
    //1,智慧养殖 2,智慧畜牧 3,专家服务台 4,上河湖羊 5,屠宰管理 6,测温数据项目
    ZHYZ("1","智慧养殖","5","10"),
    ZHXM("2","智慧畜牧","6",""),
    ZJFWT("3","专家服务台","7","7"),
    SHHY("4","上河湖羊","8",""),
    TZGL("5","屠宰管理","10",""),
    CWSJXM("6","测温数据项目","11",""),
	JDBMXM("7","见犊补母","12","12"),
	
	XCPGL("0","畜产品管理","13",""),
	
	ZHYF("8","智慧养蜂","14",""),
	/**
	 * 定边八福
	 */
	DBBF("9","定边八福","15",""),
	/**
	 * 子洲畜牧
	 */
	XMPT("10","畜牧平台","16","16"),
	/**
	 * 疫病监测
	 */
	YBJCPT("11","疫病监测","17","17"),
	/**
	 * 小牧丁助贷
	 */
	XMDJRZD("12","小牧丁金融助贷","18","18"),
	/**
	 * 智慧动监
	 */
	ZHDJPT("13","智慧动监","19","19"),
	
	
	;

    private String value;
    private String description;
	
	private String appId;
	
	private String authInfo;
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
	
	public String getAppId() {
		return appId;
	}
	
	public void setAppId(String appId) {
		this.appId = appId;
	}
	
	public String getAuthInfo() {
		return authInfo;
	}
	
	public void setAuthInfo(String authInfo) {
		this.authInfo = authInfo;
	}
	
	ApplicationTypeEnum(String value, String description, String appId, String authInfo) {
		this.value = value;
		this.description = description;
		this.appId = appId;
		this.authInfo = authInfo;
	}
}
