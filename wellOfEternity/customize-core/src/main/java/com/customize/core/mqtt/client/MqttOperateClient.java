package com.customize.core.mqtt.client;



import com.customize.core.mqtt.config.MqttConfiguration;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 乐橙API
 *
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
public class MqttOperateClient {

    @Autowired
    private MqttConfiguration mqttConfiguration;


    /**
     * 获取客户端信息
     *
     * @param clientId 客户端id
     * @return
     */
    public String queryClientInfo(String clientId) {
        String url = "/api/v5/clients/" + clientId;
        return sendGetRequest(url,null);
    }


    /**
     * 获取客户端信息
     *
     * @param page     页码
     * @param pageSize 页数
     * @param node     节点
     * @param username   用户名
     * @param ipAddress  客户端ip
     * @param connState  连接状态  connected,idle,disconnected
     * @param likeClientId  客户端id
     * @return
     */
    public String queryAllClientInfo(Integer page, Integer pageSize, String node, String username, String ipAddress, String connState, String likeClientId) {
        String url = "/api/v5/clients/";
        Map<String,Object> paramMap = new HashMap<>(16);
        if (page != null) {
            paramMap.put("page",page);
        }
        if (pageSize != null) {
            paramMap.put("limit",pageSize);
        }
        if (StringUtils.isNotEmpty(node)) {
            paramMap.put("node",node);
        }
        if (StringUtils.isNotEmpty(username)) {
            paramMap.put("username",username);
        }
        if (StringUtils.isNotEmpty(ipAddress)) {
            paramMap.put("ip_address",ipAddress);
        }
        if (StringUtils.isNotEmpty(connState)) {
            paramMap.put("conn_state",connState);
        }
        if (StringUtils.isNotEmpty(likeClientId)) {
            paramMap.put("like_clientd",likeClientId);
        }
        return sendGetRequest(url,paramMap);
    }

    /**
     * 发送Get请求
     *
     * @param url
     * @return
     */
    public String sendGetRequest(String url, Map<String, Object> paramMap) {
        try {

            String serviceUrl = mqttConfiguration.getServiceUrl();
            String apiKey = mqttConfiguration.getApiKey();
            String secretKey = mqttConfiguration.getSecretKey();
            OkHttpClient client = new OkHttpClient();
            String requestUrl = serviceUrl + url;
            if (paramMap!=null && paramMap.size() > 0) {
                StringBuilder stringBuilder = new StringBuilder("?");
                for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                    stringBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
                requestUrl += stringBuilder.substring(0,stringBuilder.length()-1);
            }
            log.info("MQTT请求地址：{}", requestUrl);

            Request request = new Request.Builder()
                    .url(requestUrl)
                    .header("Content-Type", "application/json")
                    .header("Authorization", Credentials.basic(apiKey, secretKey))
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            log.info("MQTT返回数据：{}", result);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("MQTT获取API出错：{}，{}", e.getMessage(), e);
        }
        return null;
    }

}
