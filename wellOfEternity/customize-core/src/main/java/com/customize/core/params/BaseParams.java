package com.customize.core.params;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName: BaseParams
 * @Description: 数据库基础字段
 * @date 2023-04-23 15:48
 */
@Getter
@Setter
@ToString
public class BaseParams implements Serializable {
    protected String id;
    /**
     * 创建时间
     */
    @JsonIgnore
    protected LocalDateTime createTime;
    /**
     * 创建用户id
     */
    @JsonIgnore
    @TableField(exist=false)
    protected String createUser;
    /**
     * 创建用户id
     */
    @JsonIgnore
    @TableField(exist=false)
    protected String createBy;
    /**
     * 创建用户姓名
     */
    @JsonIgnore
    protected String createUserName;
    /**
     * 创建用户id
     */
    @JsonIgnore
    protected String createUserId;
    /**
     * 更新时间
     */
    @JsonIgnore
    protected LocalDateTime updateTime;
    /**
     * 更新用户id
     */
    @JsonIgnore
    @TableField(exist=false)
    protected String updateUser;
    /**
     * 更新用户id
     */
    @JsonIgnore
    @TableField(exist=false)
    protected String updateBy;
    /**
     * 更新用户姓名
     */
    @JsonIgnore
    protected String updateUserName;
    /**
     * 更新用户id
     */
    @JsonIgnore
    protected String updateUserId;
    /**
     * 删除状态 初始化为0-未删除 1-删除
     */
    @JsonIgnore
    protected String deleted;

}
