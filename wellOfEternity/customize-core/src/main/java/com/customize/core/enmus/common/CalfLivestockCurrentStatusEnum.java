package com.customize.core.enmus.common;

/**
 * 硬件设备名称
 * <AUTHOR>
 * @desc  //当前定义状态:1:生产上报前,2:生产上报后-公示中,3:已补贴,4:二次配种后,5:二次上报后-公示中,6:二次已补贴,7:死亡上报后
 */
public enum CalfLivestockCurrentStatusEnum {
	//当前定义状态:1:生产上报前,2:生产上报后-公示中,3:已补贴,4:二次配种后,5:二次上报后-公示中,6:二次已补贴,7:死亡上报后
	
	/**
	 * 母牛状态.
	 */
	COW_BEFORE_PRODUCT_REPORT("1","生产上报前"),
	COW_AFTER_PRODUCT_REPORT_NOTICING("2","生产上报后-公示中"),
	COW_LIVESTOCK_SUBSIDIZED("3","已补贴"),
	
	COW_AFTER_SECONDARY_MATING("4","二次配种后"),
	COW_SECOND_AFTER_PRODUCT_REPORT_NOTICING("5","二次上报后-公示中"),
	COW_SECOND_LIVESTOCK_SUBSIDIZED("6","二次已补贴"),
	COW_DEAD_PRODUCT_REPORT("7","死亡上报后"),
	
	/**
	 * 犊牛状态 
	 */
	CHILD_LIVESTOCK_OTHERS("1","生产上报后-公示中"),
	CHILD_LIVESTOCK_SUBSIDIZED("2","已补贴"),
	
	;
	
	private String currentStatus;
	private String description;
	
	public String getCurrentStatus() {
		return currentStatus;
	}
	
	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
	
	CalfLivestockCurrentStatusEnum(String currentStatus, String description) {
		this.currentStatus = currentStatus;
		this.description = description;
	}
	
}
