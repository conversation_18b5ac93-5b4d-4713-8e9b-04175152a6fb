package com.customize.core.enmus.farm;

/**
 * <AUTHOR> admin
 * @date : 2023/6/14
 * @description :
 */
public enum FarmAuditStatusEnum {
	
	
	/**
	 *管理员审核状态: 1.待审核,2.已通过; 3.已驳回
	 */
	FARM_AUDIT_WAIT(1, "待审核"),
	
	FARM_AUDIT_OK(2, "已通过"),
	
	FARM_AUDIT_REJECTED(3, "已驳回"),
	;
	
	/**
	 *  值
	 */
	private Integer value;
	/**
	 * 描述
	 */
	private String valDesc;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getValDesc() {
		return valDesc;
	}
	
	public void setValDesc(String valDesc) {
		this.valDesc = valDesc;
	}
	
	FarmAuditStatusEnum(Integer value, String valDesc) {
		this.value = value;
		this.valDesc = valDesc;
	}
	
	/**
	 * Gets enum.
	 * @param value the code
	 * @return the enum
	 */
	public static FarmAuditStatusEnum getFarmType(Integer value) {
		for (FarmAuditStatusEnum typeEnum : FarmAuditStatusEnum.values()) {
			if (value.intValue() == typeEnum.getValue().intValue()) {
				return typeEnum;
			}
		}
		return null;
	}
	
}
