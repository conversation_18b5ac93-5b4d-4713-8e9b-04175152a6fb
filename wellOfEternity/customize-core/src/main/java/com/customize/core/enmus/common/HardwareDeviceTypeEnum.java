package com.customize.core.enmus.common;

/**
 * 硬件设备名称
 * <AUTHOR>
 */
public enum HardwareDeviceTypeEnum {
	
	/**
	 * 摄像头
	 */
	CAMERA_DEVICE(1,"摄像头"),
	/**
	 * 智能风机
	 */
	INTELLIGENT_FAN_DEVICE(2,"智能风机"),
	/**
	 * 环境检测仪
	 */
	ENVIRONMENTAL_DETECTOR_DEVICE(3,"环境检测仪"),
	/**
	 * 气象站
	 */
	WEATHER_STATION_DEVICE(4,"气象站"),
	/**
	 * 喷雾/消毒
	 */
	DISINFECT_DEVICE(5,"喷雾/消毒"),
	
	;

    private Integer value;
    private String descr;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getDescr() {
		return descr;
	}
	
	public void setDescr(String descr) {
		this.descr = descr;
	}
	
	HardwareDeviceTypeEnum(Integer value, String descr) {
		this.value = value;
		this.descr = descr;
	}
	
}
