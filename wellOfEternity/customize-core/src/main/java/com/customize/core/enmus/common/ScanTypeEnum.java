package com.customize.core.enmus.common;



/**
 * 二维码常量类
 *
 * <AUTHOR>
 * @date 2023/6/2 14:58
 */
public enum ScanTypeEnum {
    WL_TYPE(101,"物料"),
    TZC_TYPE(201,"屠宰场"),
    ZNEB_TYPE(301,"智能耳标"),
    SB_TYPE(401,"设备"),
    ;
    private int code;
    private String msg;

    /**
     * Msg string.
     *
     * @return the string
     */
    public String msg() {
        return msg;
    }

    /**
     * Code int.
     *
     * @return the int
     */
    public int code() {
        return code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    ScanTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static ScanTypeEnum getEnum(int code) {
        for (ScanTypeEnum ele : ScanTypeEnum.values()) {
            if (ele.code() == code) {
                return ele;
            }
        }
        return null;
    }

    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static String getType(String code) {
        for (ScanTypeEnum ele : ScanTypeEnum.values()) {
            if (code.equals(ele.code()+"")) {
                return ele.msg;
            }
        }
        return null;
    }



}
