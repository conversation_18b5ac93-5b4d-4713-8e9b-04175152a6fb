package com.customize.core.mqtt.utils;

import cn.hutool.extra.spring.SpringUtil;

import com.customize.core.mqtt.service.MqttGateWay;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.handler.annotation.Header;

/**
 * <AUTHOR>
 * @Date 2023-07-10 18:03
 * @PackageName:com.xmd.xiaomuding.customize.call.mqtt.utils
 * @ClassName: MqttUtils
 * @Description: TODO
 * @Version 1.0
 */
public class MqttUtils {

    private static MqttGateWay mqttGateWay = SpringUtil.getBean(MqttGateWay.class);


    /**
     * 给默认话题发送
     * @param data
     */
    public static void sendToMqtt(String data) {
        mqttGateWay.sendToMqtt(data);
    }

    /**
     * 给指定话题发送消息
     * @param data
     * @param topic
     */

    public static void sendToMqtt(String data, @Header(MqttHeaders.TOPIC) String topic) {
        mqttGateWay.sendToMqtt(data, topic);
    }


    /**
     * 给指定话题发送消息（qos设定）
     * @param data
     * @param topic
     * @param qos QoS 0：消息最多传递一次，如果当时客户端不可用，则会丢失该消息。
     *            QoS 1：消息传递至少 1 次。
     *            QoS 2：消息仅传送一次。
     */
    public static void sendToMqtt(String data, @Header(MqttHeaders.TOPIC) String topic, @Header(MqttHeaders.QOS) int qos) {
        mqttGateWay.sendToMqtt(data, topic, qos);
    }
}
