package com.customize.core.aspect;

import com.customize.core.annotation.CacheLock;
import com.customize.core.common.keygenerator.CacheKeyGenerator;
import com.customize.core.common.redis.RedisLockHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.UUID;

/**
 * <AUTHOR>
 */
//@Aspect
//@Component
@AllArgsConstructor
@Slf4j
public class LockMethodInterceptor {
    private final RedisLockHelper redisLockHelper;

    private final CacheKeyGenerator cacheKeyGenerator;

    @Pointcut("@annotation(com.xmd.xiaomuding.customize.core.annotation.CacheLock)")
    public void cacheLockPointCut(){}

    @Around("cacheLockPointCut()")
    public Object interceptor(ProceedingJoinPoint pjp) {
        // 获取方法签名，并从中提取方法
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        // 获取方法上的@CacheLock注解
        CacheLock lock = method.getAnnotation(CacheLock.class);
        // 检查注解中的prefix属性是否为空，prefix用于构成锁的键
        if (StringUtils.isEmpty(lock.prefix())) {
            throw new RuntimeException("lock key don't null...");
        }
        // 通过cacheKeyGenerator生成锁的键
        final String lockKey = cacheKeyGenerator.getLockKey(pjp);
        // 生成锁的值，这里使用UUID生成一个唯一值
        String value = UUID.randomUUID().toString();
        try {
            // 尝试获取锁，lock方法需要锁的键、值、过期时间和时间单位
            final boolean success = redisLockHelper.lock(lockKey, value, lock.expire(), lock.timeUnit());
            if (!success) {
                // 如果获取锁失败，抛出异常表示重复提交
                throw new RuntimeException("重复提交");
            }
            try {
                // 获取锁成功后，继续执行目标方法
                return pjp.proceed();
            } catch (Throwable throwable) {
                // 捕获执行目标方法过程中抛出的异常，并记录日志
                String msg ="系统异常:"+throwable.getMessage();
                log.info("LockMethodInterceptor.interceptor.error:{}",msg);
                throw new RuntimeException(msg);
            }
        } finally {
            // 最后，无论目标方法执行成功还是失败，都应该释放锁
            // 注意: 如果是演示环境，可能需要注释掉解锁代码以观察效果，但实际使用时应该保留解锁逻辑以避免死锁
            redisLockHelper.unlock(lockKey, value);
        }
    }

}
