package com.customize.core.enmus.common;

/**
 *"操作角色类型
 * <AUTHOR>
 */
public enum FarmDataPermissionsEnum {
	
	/**
	 * 超级管理员 1 
	 */
    FARM_SUPER_MANAGER("FARM_SUPER_MANAGER","超级管理员"),
	/**
	 * 牧场管理员 2
	 */
    FARM_MANAGER("FARM_MANAGER","牧场管理员"),
	/**
	 * 棚管理员 n
	 */
	FARM_HOUSE_MANAGER("FARM_HOUSE_MANAGER","棚管理员"),
	/**
	 * 棚下业务员 n
	 */
	FARM_HOUSE_OTHERS("FARM_HOUSE_OTHERS","棚下业务员"),
	/**
	 * 养殖场其他人员 n
	 */
	FARM_SUPERVISION_MANAGER("FARM_SUPERVISION_MANAGER","养殖场督管人员"),
	
	;
    private String value;
    private String description;
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
	
	FarmDataPermissionsEnum(String value, String description) {
		this.value = value;
		this.description = description;
	}
}
