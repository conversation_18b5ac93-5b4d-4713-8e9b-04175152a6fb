package com.customize.core.enmus.farm;

/**
 * <AUTHOR> admin
 * @date : 2023/6/14
 * @description :
 */
public enum IdentityGenderEnum {
	
	/**
	 * 0-未填写 1-男 2-女
	 * 0- Not filled in 1- Male 2- Female
	 */
	GENDER_NOT_FILLED(0, "企业"),
	/**
	 * 0-未填写 1-男 2-女
	 * 0- Not filled in 1- Male 2- Female
	 */
	GENDER_MALE(1, "男"),
	
	/**
	 * 0-未填写 1-男 2-女
	 * 0- Not filled in 1- Male 2- Female
	 */
	GENDER_FEMALE(2, "女"),
	;
	
	/**
	 *  值
	 */
	private Integer value;
	/**
	 * 描述
	 */
	private String valDesc;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getValDesc() {
		return valDesc;
	}
	
	public void setValDesc(String valDesc) {
		this.valDesc = valDesc;
	}
	
	IdentityGenderEnum(Integer value, String valDesc) {
		this.value = value;
		this.valDesc = valDesc;
	}
	
	/**
	 * Gets enum.
	 * @param value the code
	 * @return the enum
	 */
	public static IdentityGenderEnum getGender(Integer value) {
		for (IdentityGenderEnum typeEnum : IdentityGenderEnum.values()) {
			if (value.intValue() == typeEnum.getValue().intValue()) {
				return typeEnum;
			}
		}
		return null;
	}
	
	/**
	 * Gets enum.
	 * @param valDesc the code
	 * @return the enum
	 */
	public static Integer getGender(String valDesc) {
		for (IdentityGenderEnum typeEnum : IdentityGenderEnum.values()) {
			if (valDesc.trim().equalsIgnoreCase(typeEnum.getValDesc())) {
				return typeEnum.getValue();
			}
		}
		return 0;
	}
}
