package com.customize.core.utils;

import cn.hutool.extra.spring.SpringUtil;
import org.springframework.data.geo.Circle;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description //redis距离范围计算
 * @Date 16:57 2024-11-05
 * @Param
 * @return
 **/
@Service
public class GeoService {


    private static RedisTemplate<String, String> redisTemplate = SpringUtil.getBean(RedisTemplate.class);


    private static final Map<String, Boolean> resultMap = new HashMap<>();


    public static void main(String[] args) {
        GeoService.isWithinRadius("locations",
                //目标经纬度
                Double.parseDouble("110.125644"),
                Double.parseDouble("37.086822"),
                //中心点经纬度
                Double.parseDouble("110.121732"),
                Double.parseDouble("37.090275"),
                //距离范围
                3000);
    }

    /**
     * 判断某个经纬度是否在参考点的range米范围内
     */
    public static boolean isWithinRadius(String key, double targetLongitude, double targetLatitude, double referenceLongitude, double referenceLatitude, Integer range) {
        //判断缓存中是否存在当前key 如果存在 直接返回
        String cacheKey = key + targetLongitude + targetLatitude + referenceLongitude + referenceLatitude + range;
        if (resultMap.containsKey(cacheKey)) {
            return resultMap.get(cacheKey);
        }
        // 将参考点添加到 Redis 中
        redisTemplate.opsForGeo().add(key, new RedisGeoCommands.GeoLocation<>("reference", new Point(referenceLongitude, referenceLatitude)));
        redisTemplate.opsForGeo().add(key, new RedisGeoCommands.GeoLocation<>("target", new Point(targetLongitude, targetLatitude)));
        // 查询目标经纬度是否在参考点方圆3000米内
        Set<String> geoResults = redisTemplate.opsForGeo()
                .radius(key, new Circle(new Point(referenceLongitude, referenceLatitude), new Distance(range, RedisGeoCommands.DistanceUnit.METERS)))
                .getContent()
                .stream()
                .map(geoLocation -> geoLocation.getContent().getName())
                .collect(Collectors.toSet());
        // 检查目标经纬度是否在结果集中
        boolean result = geoResults.contains("target");
        resultMap.put(cacheKey, result);
        return result;
    }

    /**
     mysql定位距离计算
     <select id="selectMs" resultType="java.lang.Integer">
     SELECT
     FLOOR(
     6371000 * ACOS(
     COS(RADIANS(${latA})) * COS(RADIANS(${latB})) *
     COS(RADIANS(${lonB}) - RADIANS(${lonA})) +
     SIN(RADIANS(${latA})) * SIN(RADIANS(${latB}))
     )
     ) AS distance_in_meters;
     </select>

     <select id="selectFarms" resultType="com.xmd.xiaomuding.base.entity.user.AppUserFarmInfoEntity">
     SELECT
     *,
     (
     6371000 * acos(
     cos(radians(${latB})) *
     cos(radians(t1.address_lat)) *
     cos(radians(t1.address_lng) - radians(${lonB})) +
     sin(radians(${latB})) *
     sin(radians(t1.address_lat))
     )
     ) AS distance
     FROM
     app_user_farm_info t1 JOIN  ca_calf_preregister t2 on t1.farm_id = t2.farm_id
     WHERE
     t1.deleted = 0 and t1.`area_id`  = 42374
     HAVING
     distance &lt;= ${ms}
     ORDER BY
     distance ASC
     </select>
     */
}
