package com.customize.core.enmus.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @Description 用户状态
* @Return
**/
@AllArgsConstructor
@Getter
public enum UserStatusEnum {

    NORMAL(1,"正常"),
    ABNORMAL(2,"异常"),
    FREEZE(3,"冻结"),
    
    USER_APP_NORMAL(1,"正常"),
    USER_APP_DISABLE(3,"禁用"),
    ;

    private Integer value;
    private String description;
    
    /**
     * Gets enum.
     * @param value the code
     * @return the enum
     */
    public static String getStatusDesc(String value) {
        for (UserStatusEnum typeEnum : UserStatusEnum.values()) {
            if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
                return typeEnum.description;
            }
        }
        return null;
    }
    
}