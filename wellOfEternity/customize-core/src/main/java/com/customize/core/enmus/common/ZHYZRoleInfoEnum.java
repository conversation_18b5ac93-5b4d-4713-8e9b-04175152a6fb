package com.customize.core.enmus.common;

/**
 *"畜牧平台——>操作角色类型
 * <AUTHOR>
 */
public enum ZHYZRoleInfoEnum {
	
	/**
	 * 牧场主人员:0:牧场主;1:操作员;2:管理员
	 */
	ZHYZ_ADMINISTRATOR("ZHYZ_ADMINISTRATOR","0","超级管理员"),
	/**
	 * 牧场管理员:0:牧场主;1:操作员;2:管理员
	 */
	ZHYZ_MANAGER("ZHYZ_MANAGER","2","牧场管理员"),
	/**
	 * 牧场操作员:0:牧场主;1:操作员;2:管理员
	 */
	ZHYZ_OPERATOR("ZHYZ_OPERATOR","1","牧场操作员"),
	
	;
	private String roleCode;
	private String targetRoleId;
	private String roleName;
	
	public String getRoleCode() {
		return roleCode;
	}
	
	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}
	
	public String getTargetRoleId() {
		return targetRoleId;
	}
	
	public void setTargetRoleId(String targetRoleId) {
		this.targetRoleId = targetRoleId;
	}
	
	public String getRoleName() {
		return roleName;
	}
	
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	
	ZHYZRoleInfoEnum(String roleCode, String targetRoleId, String roleName) {
		this.roleCode = roleCode;
		this.targetRoleId = targetRoleId;
		this.roleName = roleName;
	}
	
	/**
	 * @param targetRoleId
	 * @return
	 */
	public static ZHYZRoleInfoEnum getZHYZRoleInfo(String targetRoleId) {
		for (ZHYZRoleInfoEnum ele : ZHYZRoleInfoEnum.values()) {
			if (ele.getTargetRoleId().equalsIgnoreCase(targetRoleId)) {
				return ele;
			}
		}
		return null;
	}

}
