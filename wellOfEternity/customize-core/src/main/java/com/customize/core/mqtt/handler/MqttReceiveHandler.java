package com.customize.core.mqtt.handler;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/8 18:01
 */


import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSONObject;
import com.customize.core.utils.MD5Utils;
import com.customize.core.utils.RedisUtils;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Objects;

/**
 * mqtt客户端消息处理类
 *
 * <AUTHOR>
 */

public interface MqttReceiveHandler<T> {

    Log log = LogFactory.get(MqttReceiveHandler.class);

    String MQTT_KEY = "MQTT_KEY:";

    /**
     * 添加主题
     *
     * @return
     */
    String addTopic();

    /**
     * 处理业务
     *
     * @param t 消息
     */
    void doHandlerMessage(T t);

    default void handle(Message<?> message) {
        try {
            //设置消息幂等性
            String toc = Objects.toString(message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC));
            if (addTopic().equals(toc)) {
                //获取消息key 确保幂等性 此时幂等仅限于同一个业务处理层不允许再次消费
                String key = MD5Utils.MD5Str(MQTT_KEY.concat(this.getClass().getName()).concat(toc).concat(Objects.toString(message.getPayload())));
                if (!RedisUtils.setIfAbsent(key, toc, 2L)) {
                    log.info("该消息已被其他节点消费!");
                    return;
                }
                log.debug("消息主题：{},收到订阅消息:{}", toc, message);
                //根据当前实现类泛型获取泛型属性
                ParameterizedType parameterizedType = (ParameterizedType) this.getClass().getGenericInterfaces()[0];
                //获取所有的泛型 本接口只有一个泛型
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                doHandlerMessage((T) JSONObject.parseObject(message.getPayload().toString(), Class.forName(actualTypeArguments[0].getTypeName())));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}