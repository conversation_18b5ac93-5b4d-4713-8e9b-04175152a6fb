package com.customize.core.enmus.common;

/**
 *"操作角色类型
 * <AUTHOR>
 */
public enum XuChanPinPermissionsEnum {
	
	/**
	 * 管理员 1 
	 */
    FARM_SUPER_MANAGER("XUCHANPIN_SUPER_MANAGER","管理员"),
	/**
	 * 企业管理员 2
	 */
    FARM_MANAGER("XUCHANPIN_SUBJECT_MANAGER","企业管理员"),
	
	;
    private String value;
    private String description;
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
	
	XuChanPinPermissionsEnum(String value, String description) {
		this.value = value;
		this.description = description;
	}
}
