package com.customize.core.aspect;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.customize.core.annotation.AutoIdempotent;
import com.customize.core.config.AutoIdempotentConfig;
import com.customize.core.constant.CustomizedCoreConstant;
import com.customize.core.exception.XMDServiceException;
import com.customize.core.enmus.request.IdempotentTypeEnum;
import com.customize.core.utils.IpUtils;
import com.customize.core.utils.MD5Utils;
import com.customize.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import static com.customize.core.enmus.request.IdempotentTypeEnum.OVERALL;


/**
 * <AUTHOR>
 * @ClassName: AutoIdempotentAspect
 * @Description: 接口幂等
 * @date 2023-04-20 17:59
 */
@Aspect
@Component
@Slf4j
public class AutoIdempotentAspect {

	@Resource
	private AutoIdempotentConfig autoIdempotentConfig;

	/**
	 * 定义切点
	 */
	@Pointcut("@annotation(com.customize.core.annotation.AutoIdempotent)")
	public void autoIdempotent() {
	}


	/**
	 * @return java.lang.Object
	 * <AUTHOR>
	 * @Description 环绕通知 （可以控制目标方法前中后期执行操作，目标方法执行前后分别执行一些代码）
	 * @Date 9:06 2023-04-21
	 * @Param [joinPoint]
	 */
	@Around("autoIdempotent()")
	// 在目标方法执行前执行的逻辑，用于处理自动幂等性
	public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
		// 打印当前切面对象，主要用于调试
		System.out.println(this);

		// 检查幂等性功能是否启用，如果未启用，则直接执行原方法
		if (ObjectUtil.isNotEmpty(autoIdempotentConfig.getEnabled()) && !autoIdempotentConfig.getEnabled()) {
			return joinPoint.proceed();
		}
		// 获取目标方法上的@AutoIdempotent注解实例
		AutoIdempotent annotation = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(AutoIdempotent.class);
		// 根据注解信息查找对应的Redis键
		String redisKey = findRedisKey(annotation);
		try {
			// 检查Redis中是否存在对应的键
			if (!RedisUtils.isExist(redisKey)) {
				// 如果键不存在，说明是首次操作，将键存入Redis并设置过期时间
				RedisUtils.setExp(redisKey, 1, annotation.expireTime(), annotation.timeUnit());
				// 执行原方法
				return joinPoint.proceed();
			}
			// 如果键已存在，抛出自定义异常，提示不要重复操作
			throw new XMDServiceException("请勿重复操作");
		} catch (Throwable throwable) {
			// 捕获并重新抛出异常，确保异常不被吞掉
			throw throwable;
		}
	}



	/**
	 * @return java.lang.String
	 * <AUTHOR>
	 * @Description 根据不同场景组装不同的key
	 * @Date 9:05 2023-04-21
	 * @Param [annotation, method]
	 */
	private String findRedisKey(AutoIdempotent annotation) {
		return CustomizedCoreConstant.AUTO_IDEMPOTENT_LOCK
				.concat(idempotentTypeSelect(annotation));
	}


	/**
	 * @return java.lang.String
	 * <AUTHOR>
	 * @Description 根据不同类型获取不同幂等性形式
	 * @Date 9:19 2023-04-21
	 * @Param [annotation]
	 */
	// 定义一个私有方法，用于选择幂等性处理策略
	private String idempotentTypeSelect(AutoIdempotent annotation) {
		// 从请求上下文中获取当前请求的属性
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		// 从属性中获取HttpServletRequest对象
		HttpServletRequest request = attributes.getRequest();

		// 确定幂等性类型：首先尝试从全局配置中获取幂等性类型，如果全局配置为空，则使用注解提供的类型
		IdempotentTypeEnum typeEnum = StringUtils.isNotBlank(autoIdempotentConfig.getIdempotentType())
				? IdempotentTypeEnum.getEnumByStatus(autoIdempotentConfig.getIdempotentType())
				: annotation.idempotentType();

		// 根据确定的幂等性类型，执行相应的处理逻辑
		switch (typeEnum) {
			case IP:
				// 如果类型是IP，使用客户端IP地址和请求URI拼接成字符串作为幂等性校验的键
				return IpUtils.getIpAddr(request).concat(":").concat(request.getRequestURI());
			case USER_AGENT:
				// 如果类型是USER_AGENT，使用请求中的user-agent头的MD5值和请求URI拼接成字符串作为幂等性校验的键
				return MD5Utils.MD5Str(request.getHeader("user-agent")).concat(":").concat(request.getRequestURI());
			case OVERALL:
				// 如果类型是OVERALL，使用"OVERALL"字符串和请求URI拼接成字符串作为幂等性校验的键
				return OVERALL.toString().concat(":").concat(request.getRequestURI());
			// 默认情况下，如果没有匹配到任何枚举值，抛出自定义异常
			default:
				throw new XMDServiceException("枚举类获取失败");
		}
	}

}
