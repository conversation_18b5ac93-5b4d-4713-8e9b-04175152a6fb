package com.customize.core.enmus.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *证书、证件类别
 */
@AllArgsConstructor
@Getter
public enum UserCertTypeEnum {

    ID_CARD(1,"身份证"),
    BUSINESS_LICENSE(2,"营业执照"),
    LICENSED_VETERINARIAN(3,"执业兽医师资格证书"),
    TRADEMARK_CERTIFICATE(4,"商标证书"),
    OTHER(5,"其他证件"),
    ;

    private Integer value;
    private String description;
    
    public static UserCertTypeEnum getValueOf(Integer value){
        for (UserCertTypeEnum val: UserCertTypeEnum.values()) {
            if(value.equals(val.value)){
                return val;
            }
        }
        return null;
    }
    
    public static String getDesc(String value){
        for (UserCertTypeEnum val: UserCertTypeEnum.values()) {
            if(value.trim().equals(val.value+"")){
                return val.description;
            }
        }
        return null;
    }
    
}
