package com.customize.core.enmus.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* <AUTHOR>
 * @Description 设备开通算法状态
**/
@AllArgsConstructor
@Getter
public enum AlgoAuthDeviceEnum  {

    NORMAL(0,"服务中"),
    EXPIRED(1,"已过期"),

    CLOSE_MONITOR(0,"关闭检测"),
    OPEN_MONITOR(1,"开启检测"),

    CLOSE_PUSH_MSG(0,"关闭提醒"),
    OPEN_PUSH_MSG(1,"开启提醒"),
    ;

    private Integer value;
    private String description;

    /**
     * Msg string.
     *
     * @return the string
     */
    public String description() {
        return description;
    }

    /**
     * Code object.
     *
     * @return the object
     */
    public int value() {
        return value;
    }

    AlgoAuthDeviceEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }


}