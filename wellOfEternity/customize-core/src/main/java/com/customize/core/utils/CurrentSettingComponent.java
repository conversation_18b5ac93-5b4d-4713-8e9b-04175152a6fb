package com.customize.core.utils;

import com.customize.core.config.CurrentInfoConfig;
import com.customize.core.constant.CharsConstant;
import com.customize.core.constant.RequestHeaderConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CurrentSettingComponent {
	
	@Resource
	private CurrentInfoConfig currentInfoConfig;
	
	/**
	 * 是否可以跳过验证
	 * @desc : 
	 * 		true : 表示可以直接跳过，不用校验; 
	 * 		false : 表示要校验，根据条件做是否执行下一步的校验; 	
	 * @return
	 */
	public boolean skipCheck(Long userId){
		//1.判断是否是测试环境：test -> pass; prod -> check
		String requestEnv = Objects.toString(RequestHeaderParamsClient.getHeaders().getRequestEnv(),"");
		if (requestEnv.equalsIgnoreCase(RequestHeaderConst.REQUEST_TEST_ENV)){
			return true;
		}
		if (Objects.isNull(currentInfoConfig)){
			log.error("未获取到当前配置信息对象");
			return false;
		}
		//2.特殊用户判断.
		if (Objects.nonNull(userId)){
			String excludeUsers = Objects.toString(currentInfoConfig.getExcludeUsers(),"");
			if (StringUtils.isNotBlank(excludeUsers)){
				String[] userIds = excludeUsers.split(CharsConstant.COMMA);
				for (String infoId : userIds){
					if (infoId.equalsIgnoreCase(userId+"")){
						return true;
					}
				}
			}
		}
		return false;
	}
}