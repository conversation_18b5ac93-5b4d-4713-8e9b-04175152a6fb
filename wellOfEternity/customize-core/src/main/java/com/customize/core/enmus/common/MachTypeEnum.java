package com.customize.core.enmus.common;

import com.customize.core.enmus.IBizEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 设备类型
 **/
@AllArgsConstructor
@Getter
public enum MachTypeEnum implements IBizEnum {
    /**
     * 1：测温 2：通道,盘点3：体重 4：车辆 5: 自动称重"
     */
    TEMP_TYPE(1,"测温设备"),
    CHANNEL_TYPE(2,"通道盘点设备"),
    WEIGHT_TYPE(3,"半自动称重设备"),
    CAR_TYPE(4,"车辆设备"),
    AUTO_WEIGHT_TYPE(5,"全自动称重"),
    // 6 被其他业务使用了
    BODY_SIZE_TYPE(7,"体尺测量"),
    BUTCHER_TYPE(8,"屠宰盘点"),
    TURN_HURDLE_TYPE(9,"饮水转栏")
    ;

    private Integer value;
    private String description;
}
