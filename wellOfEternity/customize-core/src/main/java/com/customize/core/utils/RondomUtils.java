package com.customize.core.utils;

import java.util.Random;

public class RondomUtils {

    /**
     * 生成i位随机数(纯数字)
     * @param i
     * @return
     */
    public static String getRondom(int i){
        String code = "";
        Random random = new Random();
        for (int a = 0; a < i; a++) {
            int r = random.nextInt(10); //每次随机出一个数字（0-9）
            code = code + r;  //把每次随机出的数字拼在一起
        }
        return code;
    }


}
