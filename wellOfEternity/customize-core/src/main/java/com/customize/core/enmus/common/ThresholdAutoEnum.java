package com.customize.core.enmus.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @Description 用户状态
* @Return
**/
@AllArgsConstructor
@Getter
public enum ThresholdAutoEnum {

    SCHEDULED(0,"定时任务启停"),
    THRESHOLD(1,"阈值自动启停"),
    MODEL(2,"模型自动启停"),
    ;

    private Integer value;
    private String description;
    
    /**
     * Gets enum.
     * @param value the code
     * @return the enum
     */
    public static String getStatusDesc(String value) {
        for (ThresholdAutoEnum typeEnum : ThresholdAutoEnum.values()) {
            if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
                return typeEnum.description;
            }
        }
        return null;
    }
}