package com.customize.core.exception;

import com.customize.core.constant.ManaConstant;
import com.customize.core.enmus.common.ErrorEnum;
import lombok.Getter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName: XMDServiceException
 * @Description: 自定义异常
 * @date 2023-04-20 16:59
 */
@Getter
public class XMDServiceException extends RuntimeException implements Serializable {
	private String code;
	private String msg;

	public XMDServiceException() {
		this.code = ErrorEnum.IS00001.getErrorCode();
		this.msg = ErrorEnum.IS00001.getValue();
	}

	public XMDServiceException(Throwable e) {
		this.code = ErrorEnum.IS00001.getErrorCode();
		this.msg = e.getMessage();
	}

	public XMDServiceException(String code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public XMDServiceException(String msg) {
		this.code = ManaConstant.ONE_STR;
		this.msg = msg;
	}

	public XMDServiceException(ErrorEnum errorEnum) {
		this.code = errorEnum.getErrorCode();
		this.msg = errorEnum.getValue();
	}
}
