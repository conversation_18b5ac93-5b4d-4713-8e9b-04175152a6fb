package com.customize.core.utils.redis;

/**
 * <AUTHOR>
 * @Date 2024-01-08 15:35
 * @PackageName:com.xmd.xiaomuding.customize.core.utils.redis
 * @ClassName: RefreshRedis
 * @Description: 手动刷新redis失效时长工具
 * @Version 1.0
 */
public class RefreshRedisUtils {


/*
     public static void main(String[] args) {
		 // 定义主节点的名称、密码和端口号
		 String masterName = "mymaster";
		 String password = "N8cZgk8&@n!Wp$5C";
		 // 创建 JedisPoolConfig 对象，设置最大连接数等参数
		 JedisPoolConfig poolConfig = new JedisPoolConfig();
		 poolConfig.setMaxTotal(10);
		 poolConfig.setMinIdle(5);
	
		 // 创建 JedisSentinelPool 对象，指定主节点信息和哨兵地址列表
		 Set<String> sentinels = new HashSet<>();
		 sentinels.add("************:26899");
		 sentinels.add("*************:26899");
		 sentinels.add("************:26899");
		 JedisSentinelPool jedisPool = new JedisSentinelPool(masterName, sentinels, poolConfig, password);
		 try (Jedis jedis = jedisPool.getResource()) {
		 // 现在可以通过 jedis 对象与 Redis Sentinel 交互了
		 //1: app用户登录之后存储token的key
		 Set<String> keys = jedis.keys("app:1*access_token*");
		 //2: 校验token需要的参数封装的key app端
		 //Set<String> keys = jedis.keys("token:user:userdetail:app:*");
		 //3: 校验token需要的参数封装的key web端
		 //Set<String> keys = jedis.keys("token:user:userdetail:web:*");
		 for (String key : keys) {
			 System.out.println(key);
			 long seconds = 86400 * 365 * 5;   // 5年
			 jedis.expire(key, (int)seconds);
		 }
		 // ...其他操作...
		 } catch (Exception e) {
		 	e.printStackTrace();
		 } finally {
			 if (jedisPool != null) {
			 jedisPool.close();
		 }
     	}
     }
*/    

}
