package com.customize.core.utils;


import cn.hutool.core.util.StrUtil;
import com.customize.core.exception.TokenException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.apache.commons.lang3.StringUtils;
import org.jasypt.contrib.org.apache.commons.codec_1_3.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * jwt工具类
 * <AUTHOR>
 */
@Component
@ConfigurationProperties("app.jwt")
public class JwtUtils {

	@Resource
	RedisTemplate redisTemplate;
	public static final String USER_TOKEN = "USER_TOKEN_KEY:";
    private Logger logger = LoggerFactory.getLogger(getClass());

    private String secret="vNFBPH6RL64pNVfwJMoCA5UMVrGaln01FCpmc7HPMvk=";
    private long expire = 365 ; //一年
    private long refreshExpire = expire;
    private String header = "header";


	public static void main(String[] args) {

		Date nowDate = new Date();
		//过期时间
		Date expireDate = new Date(nowDate.getTime() + 600 * 1000);


//		Key key = Keys.secretKeyFor(SignatureAlgorithm.HS256);
//		String format =new String( Base64.encodeBase64(key.getEncoded()),StandardCharsets.UTF_8);
		String stringKey = "vNFBPH6RL64pNVfwJMoCA5UMVrGaln01FCpmc7HPMvk=";
		byte[] encodedKey = Base64.decodeBase64(stringKey.getBytes(StandardCharsets.UTF_8));

		SecretKey secretKey = Keys.hmacShaKeyFor(encodedKey);
		String compact = Jwts.builder()
				.setSubject("123")
				.claim("token_type", "access_token")
				.setIssuedAt(nowDate)
				.setExpiration(expireDate)
				.signWith(secretKey)
				.compact();

//		System.out.println(format);
		System.out.println(compact);

		Claims body = Jwts.parser()
				.setSigningKey(secretKey)
				.parseClaimsJws(compact)
				.getBody();
		System.out.println(body.getSubject());

	}
    /**
     * 生成jwt token
     */
    public String generateToken(long userId) throws TokenException {
        Date nowDate = new Date();
        //过期时间
        //Date expireDate = new Date(nowDate.getTime() + expire * 1000);
		String compact = Jwts.builder()
				.setSubject(userId + "")
				.claim("token_type", "access_token")
				.setIssuedAt(nowDate)
				//.setExpiration(expireDate)
				.signWith(generalKey())
				.compact();

		try {
			//如果存在拿取已有的token, 否则设置新的token
			Object oldObject = redisTemplate.opsForValue().get(USER_TOKEN + userId);
			if (StrUtil.isEmptyIfStr(oldObject)) {
				redisTemplate.opsForValue().set(USER_TOKEN + userId, compact,expire,TimeUnit.DAYS);
			}else{
				return redisTemplate.opsForValue().get(USER_TOKEN + userId).toString();
			}

			return compact;
		}catch (Exception e){
			throw new TokenException("create token failed");
		}

    }

	/**
	 *
	 * @param userId
	 * @param clientFlag  mobile  、  pc
	 * @return
	 * @throws TokenException
	 */
	public String generateToken(long userId,String clientFlag) throws TokenException {
		Date nowDate = new Date();
		//过期时间
		//Date expireDate = new Date(nowDate.getTime() + expire * 1000);
		String compact = Jwts.builder()
				.setSubject(clientFlag+StrUtil.C_COLON+userId)
				.claim("token_type", "access_token")
				.setIssuedAt(nowDate)
				//.setExpiration(expireDate)
				.signWith(generalKey())
				.compact();

		try {
			String tokenKey = USER_TOKEN + clientFlag+StrUtil.C_COLON+userId;
			 redisTemplate.opsForValue().set(tokenKey, compact,expire,TimeUnit.DAYS);
			return compact;
		}catch (Exception e){
			throw new TokenException("create token failed");
		}
	}

    /**
     * 生成jwt refresh_token
     */
    public String generateTokenRefresh(long userId) {
        Date nowDate = new Date();
        //过期时间
        Date expireDate = new Date(nowDate.getTime() + refreshExpire * 1000);

        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setSubject(userId+"")
                .claim("token_type", "refresh_token")
                .setIssuedAt(nowDate)
                //.setExpiration(expireDate)
                .signWith(generalKey())
                .compact();
    }

    public Claims getClaimByToken(String token) throws TokenException {
			Claims body = Jwts.parser()
					.setSigningKey(generalKey())
					.parseClaimsJws(token)
					.getBody();
			String subject = body.getSubject();
			String cachedToken = (String) redisTemplate.opsForValue().get(USER_TOKEN + subject);
			if (StringUtils.isEmpty(cachedToken)){
				throw new TokenException("用户凭据已失效，请重新登录");
			}else if (!StringUtils.equals(cachedToken,token)){
				throw new TokenException("你的登录信息已失效！请重新登录！");
			}else{
				return body;
			}
    }

    /**
    * @Description 删除token
    **/
	public void deleteToken(Long userId) throws TokenException {
		Boolean delete = redisTemplate.delete(USER_TOKEN + userId);
		if (!delete){
			throw new TokenException("删除用户token失败");
		}
	}

	/**
	 * @Description 删除token
	 **/
	public void deleteToken(String token) throws TokenException {
		Claims body = getClaimByToken(token);
		String subject = body.getSubject();
		Boolean delete = redisTemplate.delete(USER_TOKEN + subject);
		if (!delete){
			throw new TokenException("删除用户token失败");
		}
	}


    /**
    * @Description 增加token过期时间
    **/
    public void flushTokenLife(String token) throws TokenException {
		Claims body = getClaimByToken(token);
		String subject = body.getSubject();
		Date nowDate = new Date();
		Date expireDate = new Date(nowDate.getTime() + expire * 1000);
		redisTemplate.expire(USER_TOKEN + subject, expireDate.getTime(), TimeUnit.MILLISECONDS);
    }

    /**-
    * @Description redis token是否过期
    * @Return boolean
    **/
	public boolean isTokenExpired(Claims body) throws TokenException {
		String subject = body.getSubject();
		String cachedToken = (String) redisTemplate.opsForValue().get(USER_TOKEN + subject);
		return StringUtils.isNotEmpty(cachedToken);
	}
    /**
     * token是否过期
     * @return  true：过期
     */
    public boolean isTokenExpired(Date expiration) {
        return expiration.before(new Date());
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public long getExpire() {
        return expire;
    }

    public void setExpire(long expire) {
        this.expire = expire;
    }

    public long getRefreshExpire() {
        return refreshExpire;
    }

    public void setRefreshExpire(long refreshExpire) {
        this.refreshExpire = refreshExpire;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

	/**
	 * 由字符串生成加密key
	 *
	 * @return
	 */
	public SecretKey generalKey() {
		byte[] encodedKey = Base64.decodeBase64(secret.getBytes(StandardCharsets.UTF_8));
		return Keys.hmacShaKeyFor(encodedKey);
	}
}
