package com.customize.core.enmus.user;

/**
 * <AUTHOR> admin
 * @date : 2023/5/23
 * @description : 请求环境：dev，test，uat，prod
 */
public enum UserLoginTypeEnum {
	/**
	 * 极光登录
	 */
	JI_GUANG_ONE_LOGIN("ji<PERSON><PERSON><PERSON>ogin","极光一键登录"),
	/**
	 * 短信验证码登录
	 */
	SMS_VALID_CODE_LOGIN("smsCodeLogin","验证码登录"),
	/**
	 * 账号密码登录
	 */
	ACCOUNT_PASSWORD_LOGIN("accountPwdLogin","账号、密码登录"),
	;
	
	/**
	 *  登录类型
	 */
	private String loginType;
	/**
	 * 登录类型描述
	 */
	private String loginTypeDesc;
	
	public String getLoginType() {
		return loginType;
	}
	
	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}
	
	public String getLoginTypeDesc() {
		return loginTypeDesc;
	}
	
	public void setLoginTypeDesc(String loginTypeDesc) {
		this.loginTypeDesc = loginTypeDesc;
	}
	
	UserLoginTypeEnum(String loginType, String loginTypeDesc) {
		this.loginType = loginType;
		this.loginTypeDesc = loginTypeDesc;
	}
	
	/**
	 * Gets enum.
	 * @param loginType the code
	 * @return the enum
	 */
	public static UserLoginTypeEnum getEnvironment(String loginType) {
		for (UserLoginTypeEnum typeEnum : UserLoginTypeEnum.values()) {
			if (loginType.equalsIgnoreCase(typeEnum.getLoginType())) {
				return typeEnum;
			}
		}
		return null;
	}
}
