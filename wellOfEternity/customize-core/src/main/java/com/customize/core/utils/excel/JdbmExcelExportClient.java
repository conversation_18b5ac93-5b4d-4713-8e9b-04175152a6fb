package com.customize.core.utils.excel;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.customize.core.utils.excel.jdbm.BreedingCowsData;
import com.customize.core.utils.excel.jdbm.CapableBreedingCows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JdbmExcelExportClient {

    /**
     * 导出能繁母牛
     *
     * @param response         ：返回对象
     * @param exportPath       ：excel模板地址名称
     * @param excelName        ：excel名称
     * @param mergeColumnIndex ：合并哪些列
     * @param mergeRowIndex    合并开始的行
     * @param dataInfo         ：填充值
     * @param dataList         ：数据值
     * @param endMap           ：填充值
     * @throws Exception
     */
    public void exportCapableBreedingCows(HttpServletResponse response, String exportPath, String excelName, int[] mergeColumnIndex, int mergeRowIndex, BreedingCowsData dataInfo, List<CapableBreedingCows> dataList,
                                          Map<String, String> endMap) throws Exception {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(excelName + ".xlsx", "utf-8");
        response.setHeader("Content-disposition", "attachment; filename=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));
        URL url = new URL(exportPath);
        InputStream inputStream = url.openStream();
        //流处理
        OutputStream outputStream = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(outputStream);
        ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream)
                .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumnIndex))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        // Bean对象 转换成 Map
        Map<String, Object> dataMap = BeanUtil.beanToMap(dataInfo);
        dataMap.putAll(endMap);
        log.info("dataMap:{},\n\tdataList:{}", JSON.toJSONString(dataMap), JSON.toJSONString(dataList));
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(dataMap, fillConfig, writeSheet);
        excelWriter.fill(dataList, fillConfig, writeSheet);
        excelWriter.fill(nullData(dataList), fillConfig, writeSheet);
        excelWriter.finish();
        inputStream.close();
        bos.flush();
        bos.close();
    }


    public List<CapableBreedingCows> nullData(List<CapableBreedingCows> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            dataList = new ArrayList<>();
            for (int i = 0; i < 10; i++) {
                CapableBreedingCows x = new CapableBreedingCows();
                x.setIndex("");
                x.setCowEarNumber("");
                x.setCowInTheColumn("/");
                x.setCalfEarNumber("");
                x.setBirthDate("");
                x.setBreedMethod("〇本交  〇人工");
                x.setSubsidySign("/");
                x.setCalfInTheColumn("/");
                dataList.add(x);
            }
            return dataList;
        }
        return dataList.stream()
                .map(x -> {
                    x.setIndex("");
                    x.setCowEarNumber("");
                    x.setCowInTheColumn("/");
                    x.setCalfEarNumber("");
                    x.setBirthDate("");
                    x.setBreedMethod("〇本交  〇人工");
                    x.setSubsidySign("/");
                    x.setCalfInTheColumn("/");
                    return x;
                })
                .collect(Collectors.toList());
    }
}
