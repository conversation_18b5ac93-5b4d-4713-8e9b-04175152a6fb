package com.customize.core.mqtt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * MQTT 客户端连接的基本配置
 * Create By Spring-2023/07/09
 * <AUTHOR>
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "mqtt")
public class MqttConfiguration {
    /**
     * 服务器地址
     */
    private String url;
    /**
     * 默认客户端id
     */
    private String clientId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 连接保活心跳
     */
    private int keepalive;
    /**
     * 默认订阅话题
     */
    private String receiveDefaultTopics;
    /**
     * qos 消息服务质量 0:最多一次 1：至少一次 2：仅一次
     */
    private int qos;
    /**
     * restful Api地址
     */
    private String serviceUrl;
    /**
     * api key
     */
    private String apiKey;
    /**
     * api sk
     */
    private String secretKey;

}
