package com.customize.core.enmus.warning;

import java.util.Objects;

/**
 * 告警类型
 * <AUTHOR>
 */
public enum WarningInstanceTypeEnum {
	
	/**
	 * 摄像头
	 */
	CAMERA_DEVICE_WARNING(1,"摄像头"),
	/**
	 * 智能风机
	 */
	INTELLIGENT_FAN_DEVICE_WARNING(2,"智能风机"),
	/**
	 * 环境检测仪
	 */
	ENVIRONMENTAL_DETECTOR_DEVICE_WARNING(3,"环境检测仪"),
	/**
	 * 气象站
	 */
	WEATHER_STATION_DEVICE_WARNING(4,"气象站"),
	/**
	 * 喷雾/消毒
	 */
	DISINFECT_DEVICE_WARNING(5,"喷雾/消毒"),
	/**
	 * 控制设备
	 */
	CONTROL_DEVICE_WARNING(6,"控制设备"),
	/**
	 * 饲喂告警
	 */
	LIVESTOCK_FEEDING_INFO_WARNING(50,"饲喂告警"),
	
	/**
	 * 繁育告警
	 */
	LIVESTOCK_BREEDING_INFO_WARNING(51,"繁育告警"),
	
	;

    private Integer value;
    private String descr;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getDescr() {
		return descr;
	}
	
	public void setDescr(String descr) {
		this.descr = descr;
	}
	
	WarningInstanceTypeEnum(Integer value, String descr) {
		this.value = value;
		this.descr = descr;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WarningInstanceTypeEnum getInstance(String value){
		for (WarningInstanceTypeEnum typeEnum : WarningInstanceTypeEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
				return typeEnum;
			}
		}
		return null;
	}
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WarningInstanceTypeEnum levelEnum = getInstance(value);
		if (Objects.nonNull(levelEnum)){
			return levelEnum.getDescr();
		}
		return "";
	}
}
