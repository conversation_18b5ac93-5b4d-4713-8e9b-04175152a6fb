package com.customize.core.enmus.common;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2023-08-17 14:33
 * @PackageName:com.xmd.xiaomuding.project.server.enums
 * @ClassName: FileTypeEnmu
 * @Description: TODO
 * @Version 1.0
 */
@Getter
public enum FileTypeEnum {
    DOC("doc", "https://file.vmuyun.com/image/cover/F449B5E0198846129BB84DE9EF7FCFF0-6-2.png"),
    DOCX("docx", "https://file.vmuyun.com/image/cover/F449B5E0198846129BB84DE9EF7FCFF0-6-2.png"),
    PDF("pdf", "https://file.vmuyun.com/image/cover/25C8CAB8EAB640989694CE19D879E927-6-2.png"),
    <PERSON><PERSON>("jpg", "https://file.vmuyun.com/image/cover/986384E3C3A046B99A34BF884F1AA1BD-6-2.png"),
    <PERSON><PERSON>("xls", "https://file.vmuyun.com/image/cover/B725A2FFCCF84DEEB2D202078E146D65-6-2.png"),
    XLSX("xlsx", "https://file.vmuyun.com/image/cover/B725A2FFCCF84DEEB2D202078E146D65-6-2.png"),
    SVG("svg", "https://file.vmuyun.com/image/cover/70C1DD2AFF3E4CE588C4FB791883A831-6-2.png"),
    PNG("png", "https://file.vmuyun.com/image/cover/C639242D25A34A6AB6242A7FBF939F1F-6-2.png");

    private String type;
    private String url;


    FileTypeEnum(String status, String url) {
        this.type = status;
        this.url = url;
    }

    /**
     * 根据状态查询枚举
     */
    public static FileTypeEnum getEnumByType(String type) {
        return Arrays.stream(FileTypeEnum.values())
                .filter(x -> x.getType().equalsIgnoreCase(type))
                .findFirst()
                .orElse(null);
    }


    public static String getUrlByType(String type) {
        return Arrays.stream(FileTypeEnum.values())
                .filter(x -> x.getType().equalsIgnoreCase(type))
                .map(x -> x.getUrl())
                .findFirst()
                .orElse(null);
    }


    public static String getUrlByDocumentName(String documentName) {
        if (StringUtils.isNotBlank(documentName)) {
            String[] split = documentName.split("\\.");
            return getUrlByType(split[split.length - 1]);
        }
        return null;
    }
}