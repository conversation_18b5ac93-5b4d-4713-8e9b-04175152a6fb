package com.customize.core.enmus.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @Description 用户状态
* @Return
**/
@AllArgsConstructor
@Getter
public enum UserAuthStatusEnum {
    //0：未实名认证  1：实名认证 2:审核中 3:未通过
    /**
     * 未实名认证
     */
    UNREALIZED_AUTHENTICATION(0,"未实名认证"),
    /**
     * 实名认证
     */
    REAL_NAME_AUTHENTICATION(1,"实名认证"),
    /**
     * 审核中
     */
    INING_REVIEW(2,"审核中"),
    /**
     * 未通过
     */
    FAIL_AUTH(3,"未通过"),
    ;

    private Integer value;
    private String description;

}