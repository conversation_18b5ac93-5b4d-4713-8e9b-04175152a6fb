package com.customize.core.enmus.farm;

/**
 * <AUTHOR> admin
 * @date : 2023/6/16
 * @description :
 */
public enum FarmModelAllOneEnum {
	
	/**
	 * 智慧养殖养殖场认证
	 */
	ZHYZ_FARM_MODEL_IN("5","10","智慧养殖养殖场认证"),
	/**
	 * 见犊补母养殖场
	 */
	jdbm_farm_model_in("12","10","见犊补母养殖场"),

	;
	
	/**
	 *  值
	 */
	private String source;
	/**
	 * 描述
	 */
	private String target;
	/**
	 * 描述
	 */
	private String businDesc;
	
	public String getSource() {
		return source;
	}
	
	public void setSource(String source) {
		this.source = source;
	}
	
	public String getTarget() {
		return target;
	}
	
	public void setTarget(String target) {
		this.target = target;
	}
	
	public String getBusinDesc() {
		return businDesc;
	}
	
	public void setBusinDesc(String businDesc) {
		this.businDesc = businDesc;
	}
	
	FarmModelAllOneEnum(String source, String target, String businDesc) {
		this.source = source;
		this.target = target;
		this.businDesc = businDesc;
	}
	
	/**
	 * Gets enum.
	 * @param source the code
	 * @return the enum
	 */
	public static FarmModelAllOneEnum getFarmModel(String source) {
		for (FarmModelAllOneEnum typeEnum : FarmModelAllOneEnum.values()) {
			if (source.equalsIgnoreCase(typeEnum.getSource())) {
				return typeEnum;
			}
		}
		return null;
	}
}
