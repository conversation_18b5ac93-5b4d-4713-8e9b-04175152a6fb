package com.customize.core.enmus.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *"操作角色类型
 */
@AllArgsConstructor
@Getter
public enum OptAuthTypeEnum {
    
    IMMUNIZER(0,"防疫员","9"),
    OPERATOR(1,"操作员","8"),
    ADMINISTRATOR(2,"管理员","4"),
    ;

    private Integer value;
    private String description;
    private String roleId;
    
    public static boolean existRoleId(String roleId){
        for (OptAuthTypeEnum val: OptAuthTypeEnum.values()) {
            if(roleId.trim().equals(val.getRoleId())){
                return true;
            }
        }
        return false;
    }
    
}
