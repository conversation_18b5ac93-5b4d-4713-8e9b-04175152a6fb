package com.customize.core.enmus.common;

/**
 *"操作角色类型
 */
public enum AreaCodeLevelEnum {

    //省市区级别（1、省 2、市 3、区 4、镇 5、村）
    PROVINCE("1","省"),
    CITY("2","市"),
    DISTRICT("3","区"),
    TOWN("4","镇"),
    VILLAGE("5","村"),
    ;

    private String value;
    private String description;
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
	
	AreaCodeLevelEnum(String value, String description) {
		this.value = value;
		this.description = description;
	}
}
