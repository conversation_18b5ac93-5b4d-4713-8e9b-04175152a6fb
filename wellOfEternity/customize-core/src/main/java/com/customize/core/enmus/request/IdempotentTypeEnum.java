package com.customize.core.enmus.request;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName: DistributeLockTypeEnum
 * @Description: 接口幂等性方式
 * @date 2023-04-20 16:59
 */
@Getter
public enum IdempotentTypeEnum {
	IP,
	USER_AGENT,
	OVERALL;


	/**
	 * 根据状态查询枚举
	 */
	public static IdempotentTypeEnum getEnumByStatus(String status) {
		return Arrays.stream(IdempotentTypeEnum.values())
				.filter(x -> x.toString().equalsIgnoreCase(status))
				.findFirst()
				.orElse(null);
	}
}
