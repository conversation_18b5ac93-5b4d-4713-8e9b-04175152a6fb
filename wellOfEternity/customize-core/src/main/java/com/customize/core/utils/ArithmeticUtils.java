package com.customize.core.utils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR> admin
 * @description :
 */
public class ArithmeticUtils {
	
	/**
	 * 默认除法运算精度
	 */
	private static final int DEF_DIV_SCALE = 10;
	
	/**
	 * 提供精确的加法运算
	 *
	 * @param v1 被加数
	 * @param v2 加数
	 * @return 两个参数的和
	 */
	
	public static double add(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Objects.toString(v1));
		BigDecimal b2 = new BigDecimal(Objects.toString(v2));
		return b1.add(b2).doubleValue();
	}
	
	/**
	 * 提供精确的加法运算
	 *
	 * @param v1 被加数
	 * @param v2 加数
	 * @return 两个参数的和
	 */
	public static BigDecimal add(String v1, String v2) {
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.add(b2);
	}
	
	/**
	 * 提供精确的加法运算
	 *
	 * @param v1    被加数
	 * @param v2    加数
	 * @param scale 保留scale 位小数
	 * @return 两个参数的和
	 */
	public static String add(String v1, String v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.add(b2).setScale(scale, BigDecimal.ROUND_HALF_UP).toString();
	}
	
	/**
	 * 提供精确的减法运算
	 *
	 * @param v1 被减数
	 * @param v2 减数
	 * @return 两个参数的差
	 */
	public static double sub(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Objects.toString(v1));
		BigDecimal b2 = new BigDecimal(Objects.toString(v2));
		return b1.subtract(b2).doubleValue();
	}
	
	/**
	 * 提供精确的减法运算。
	 *
	 * @param v1 被减数
	 * @param v2 减数
	 * @return 两个参数的差
	 */
	public static BigDecimal sub(String v1, String v2) {
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.subtract(b2);
	}
	
	/**
	 * 提供精确的减法运算
	 *
	 * @param v1    被减数
	 * @param v2    减数
	 * @param scale 保留scale 位小数
	 * @return 两个参数的差
	 */
	public static String sub(String v1, String v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.subtract(b2).setScale(scale, BigDecimal.ROUND_HALF_UP).toString();
	}
	
	/**
	 * 提供精确的乘法运算
	 *
	 * @param v1 被乘数
	 * @param v2 乘数
	 * @return 两个参数的积
	 */
	public static double mul(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Objects.toString(v1));
		BigDecimal b2 = new BigDecimal(Objects.toString(v2));
		return b1.multiply(b2).doubleValue();
	}
	
	/**
	 * 提供精确的乘法运算
	 *
	 * @param v1 被乘数
	 * @param v2 乘数
	 * @return 两个参数的积
	 */
	public static BigDecimal mul(String v1, String v2) {
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.multiply(b2);
	}
	
	/**
	 * 提供精确的乘法运算
	 *
	 * @param v1    被乘数
	 * @param v2    乘数
	 * @param scale 保留scale 位小数
	 * @return 两个参数的积
	 */
	public static double mul(double v1, double v2, int scale) {
		BigDecimal b1 = new BigDecimal(Objects.toString(v1));
		BigDecimal b2 = new BigDecimal(Objects.toString(v2));
		return round(b1.multiply(b2).doubleValue(), scale);
	}
	
	/**
	 * 提供精确的乘法运算
	 *
	 * @param v1    被乘数
	 * @param v2    乘数
	 * @param scale 保留scale 位小数
	 * @return 两个参数的积
	 */
	public static String mul(String v1, String v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.multiply(b2).setScale(scale, BigDecimal.ROUND_HALF_UP).toString();
	}
	
	/**
	 * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
	 * 小数点以后10位，以后的数字四舍五入
	 *
	 * @param v1 被除数
	 * @param v2 除数
	 * @return 两个参数的商
	 */
	
	public static double div(double v1, double v2) {
		return div(v1, v2, DEF_DIV_SCALE);
	}
	
	/**
	 * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
	 * 定精度，以后的数字四舍五入
	 *
	 * @param v1    被除数
	 * @param v2    除数
	 * @param scale 表示表示需要精确到小数点以后几位。
	 * @return 两个参数的商
	 */
	public static double div(double v1, double v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(Objects.toString(v1));
		BigDecimal b2 = new BigDecimal(Objects.toString(v2));
		return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	}
	
	/**
	 * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
	 * 定精度，以后的数字四舍五入
	 *
	 * @param v1    被除数
	 * @param v2    除数
	 * @param scale 表示需要精确到小数点以后几位
	 * @return 两个参数的商
	 */
	public static String div(String v1, String v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v1);
		return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).toString();
	}
	
	/**
	 * 提供精确的小数位四舍五入处理
	 *
	 * @param v     需要四舍五入的数字
	 * @param scale 小数点后保留几位
	 * @return 四舍五入后的结果
	 */
	public static double round(double v, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive integer or zero");
		}
		BigDecimal b = new BigDecimal(Objects.toString(v));
		return b.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	}
	
	/**
	 * 提供精确的小数位四舍五入处理
	 *
	 * @param v     需要四舍五入的数字
	 * @param scale 小数点后保留几位
	 * @return 四舍五入后的结果
	 */
	public static String round(String v, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b = new BigDecimal(v);
		return b.setScale(scale, BigDecimal.ROUND_HALF_UP).toString();
	}
	
	/**
	 * 取余数
	 *
	 * @param v1    被除数
	 * @param v2    除数
	 * @param scale 小数点后保留几位
	 * @return 余数
	 */
	public static String remainder(String v1, String v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.remainder(b2).setScale(scale, BigDecimal.ROUND_HALF_UP).toString();
	}
	
	/**
	 * 取余数  BigDecimal
	 *
	 * @param v1    被除数
	 * @param v2    除数
	 * @param scale 小数点后保留几位
	 * @return 余数
	 */
	public static BigDecimal remainder(BigDecimal v1, BigDecimal v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		return v1.remainder(v2).setScale(scale, BigDecimal.ROUND_HALF_UP);
	}
	
	/**
	 * 比较大小
	 *
	 * @param v1 被比较数
	 * @param v2 比较数
	 * @return 如果v1 大于v2 则 返回true 否则false
	 */
	public static boolean compare(String v1, String v2) {
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		int bj = b1.compareTo(b2);
		boolean res;
		if (bj > 0) {
			res = true;
		}else {
			res = false;
		}
		return res;
	}
}
