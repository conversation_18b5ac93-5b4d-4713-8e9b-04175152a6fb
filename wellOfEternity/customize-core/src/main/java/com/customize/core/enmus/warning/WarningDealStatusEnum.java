package com.customize.core.enmus.warning;

import java.util.Objects;

/**
 * 处理状态
 * <AUTHOR>
 */
public enum WarningDealStatusEnum {
	
	/**
	 * 待处理
	 */
	PENDING_PROCESSING_WARNING(0,"待处理"),
	/**
	 * 已处理
	 */
	PROCESSED_WARNING(1,"已处理"),
	/**
	 * 不处理
	 */
	NOT_HANDLED_WARNING(2,"不处理"),
	
	;

    private Integer value;
    private String descr;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getDescr() {
		return descr;
	}
	
	public void setDescr(String descr) {
		this.descr = descr;
	}
	
	WarningDealStatusEnum(Integer value, String descr) {
		this.value = value;
		this.descr = descr;
	}
	
	/**
	 * 获取对象实例.
	 * @return
	 */
	public static WarningDealStatusEnum getInstance(String value){
		for (WarningDealStatusEnum typeEnum : WarningDealStatusEnum.values()) {
			if (value.equalsIgnoreCase(typeEnum.getValue()+"")) {
				return typeEnum;
			}
		}
		return null;
	}
	
	/**
	 * 获取对象介绍
	 * @return
	 */
	public static String getDesc(String value){
		WarningDealStatusEnum levelEnum = getInstance(value);
		if (Objects.nonNull(levelEnum)){
			return levelEnum.getDescr();
		}
		return "";
	}
	
}
