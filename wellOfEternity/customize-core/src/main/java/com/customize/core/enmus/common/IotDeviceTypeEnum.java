package com.customize.core.enmus.common;

import java.util.Arrays;

public enum IotDeviceTypeEnum {
    IOT_BODY_TYPE(1,"体尺测量设备"),
    TEC_ROBOT(2,"科大机器人"),
    FEED_CAR(3,"饲喂监测车"),
    FLY_CONTROL(4,"蚊蝇防控"),
    ENV_DISINFECT(5,"环境消毒杀")

    ;

    private Integer type;

    private String msg;

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    IotDeviceTypeEnum(Integer type, String msg) {
        this.type=type;
        this.msg=msg;

    }

    public static IotDeviceTypeEnum getEnumByType(Integer type) {
        return Arrays.stream(IotDeviceTypeEnum.values())
                .filter(x -> x.getType().equals(type))
                .findFirst()
                .orElse(null);
    }


}
