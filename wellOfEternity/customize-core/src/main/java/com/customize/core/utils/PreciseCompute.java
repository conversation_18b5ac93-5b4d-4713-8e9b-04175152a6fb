package com.customize.core.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * <AUTHOR>
 */
public class PreciseCompute {
//默认除法运算精度
private static final int DEF_DIV_SCALE = 10;

/**
* 提供精确的加法运算。
* @param v1 被加数
* @param v2 加数
* @return 两个参数的和
*/

public static double add(double v1, double v2) {
   BigDecimal b1 = new BigDecimal(Double.toString(v1));
   BigDecimal b2 = new BigDecimal(Double.toString(v2));
   return b1.add(b2).doubleValue();
}

/**
* 提供精确的减法运算。
* @param v1 被减数
* @param v2 减数
* @return 两个参数的差
*/

public static double sub(double v1, double v2) {
   BigDecimal b1 = new BigDecimal(Double.toString(v1));
   BigDecimal b2 = new BigDecimal(Double.toString(v2));
   return b1.subtract(b2).doubleValue();
}
/**
* 提供精确的减法运算。
* @param v1 被减数
* @param v2 减数
* @return 两个参数的差
*/
public static BigDecimal sub(BigDecimal v1, BigDecimal v2) {
	   BigDecimal b1 = v1;
	   BigDecimal b2 = v2;
	   return new BigDecimal(b1.subtract(b2).doubleValue());
	}

/**
* 提供精确的乘法运算。
* @param v1 被乘数
* @param v2 乘数
* @return 两个参数的积
*/
public static double mul(double v1, double v2) {
   BigDecimal b1 = new BigDecimal(Double.toString(v1));
   BigDecimal b2 = new BigDecimal(Double.toString(v2));
   return b1.multiply(b2).doubleValue();
}

public static String mul(String v1, String v2) {
	BigDecimal b1 = new BigDecimal(v1);
	BigDecimal b2 = new BigDecimal(v2);
	return String.valueOf(b1.multiply(b2).doubleValue());
}

/**
* 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
* 小数点以后10位，以后的数字四舍五入。
* @param v1 被除数
* @param v2 除数
* @return 两个参数的商
*/

public static double div(double v1, double v2) {
   return div(v1, v2, DEF_DIV_SCALE);
}

/**
* 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
* 定精度，以后的数字四舍五入。
* @param v1 被除数
* @param v2 除数
* @param scale 表示表示需要精确到小数点以后几位。
* @return 两个参数的商
*/
public static double div(double v1, double v2, int scale) {
   if (scale < 0) {
    throw new IllegalArgumentException(
      "The scale must be a positive integer or zero");
   }
	if(v2==0){
		throw new IllegalArgumentException("Be cannot divide by zero");
	}
   BigDecimal b1 = new BigDecimal(Double.toString(v1));
   BigDecimal b2 = new BigDecimal(Double.toString(v2));
   return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
}
public static String divForBigDecimal(double v1, double v2, int scale) {
	   if (scale < 0) {
	    throw new IllegalArgumentException(
	      "The scale must be a positive integer or zero");
	   }
		if(v2==0){
			throw new IllegalArgumentException("Be cannot divide by zero");
		}
	   BigDecimal b1 = new BigDecimal(Double.toString(v1));
	   BigDecimal b2 = new BigDecimal(Double.toString(v2));
	  // return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	   return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).toString();
	}
/**
* 提供精确的小数位四舍五入处理。
* @param v 需要四舍五入的数字
* @param scale 小数点后保留几位
* @return 四舍五入后的结果
*/
public static double round(double v, int scale) {
   if (scale < 0) {
    throw new IllegalArgumentException(
      "The scale must be a positive integer or zero");
   }
   BigDecimal b = new BigDecimal(Double.toString(v));
   BigDecimal one = new BigDecimal("1");
   return b.divide(one, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
}
/**
* 提供精确的小数位四舍五入处理。
* @param v 需要四舍五入的数字
* @param scale 小数点后保留几位
* @param isUseZeroFill 是否用零补位，例如：0.12精确到小数点4位，即就是 0.1200
* @return 四舍五入后的结果
*/
public static double round(double v, int scale,int isUseZeroFill) {
   if (scale < 0) {
    throw new IllegalArgumentException(
      "The scale must be a positive integer or zero");
   }
   BigDecimal b = new BigDecimal(Double.toString(v));
   BigDecimal one = new BigDecimal("1");
   return b.divide(one, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
}
public static BigDecimal formatComma2BigDecimal(Object obj) {
	String val = String.valueOf(obj);
	if (val == null) {
		return new BigDecimal("0.0000");
	}

	val = val.replaceAll(",", "");
	if (!isNumber(val)) {
		return new BigDecimal("0.0000");
	}

	BigDecimal decimal = new BigDecimal(val);
	decimal = decimal.setScale(4, RoundingMode.HALF_UP);

	return decimal;

}
public static String formatCommaAnd2Point(Object obj) {
	BigDecimal decimal = formatComma2BigDecimal(obj);

	//DecimalFormat df = new DecimalFormat("#,###.00");
	DecimalFormat df = new DecimalFormat("####.00");
	String decimalStr = ".00".equals(df.format(decimal))?"0.00":df.format(decimal);
	if(decimalStr.startsWith(".")){
		decimalStr = "0"+decimalStr;
	}
	return decimalStr;
}
	public static String formatCommaAnd4Point(Object obj) {
		BigDecimal decimal = formatComma2BigDecimal(obj);

		//DecimalFormat df = new DecimalFormat("#,###.00");
		DecimalFormat df = new DecimalFormat("####.0000");
		String decimalStr = ".0000".equals(df.format(decimal))?"0.0000":df.format(decimal);
		if(decimalStr.startsWith(".")){
			decimalStr = "0"+decimalStr;
		}
		return decimalStr;
	}
/**
 * 增值率
 * @param v1 被除数(一部分)(v1-v2)/v2
 * @param v2 除数
 * @return
 */
public static String getAappreciationRate(double v1, double v2){
	String appreciationRate="0.00%";
	if(v2!=0){
	appreciationRate=formatCommaAnd2Point(mul(div(sub(v1, v2),v2),100))+"%";
	}
	return appreciationRate;
}
public static boolean isDouble(String value) {
	try {
		Double.parseDouble(value);
		if (value.contains(".")) {
			return true;
		}
		return false;
	} catch (NumberFormatException e) {
		return false;
	}
}
public static boolean isInteger(String value) {
	try {
		Integer.parseInt(value);
		return true;
	} catch (NumberFormatException e) {
		return false;
	}
}
public static boolean isNumber(String value) {
	return isInteger(value) || isDouble(value);
}
/*public static void main(String[] args){
	System.out.println(add(0.01, 0.05));
	System.out.println(sub(1.0, 0.42));
	System.out.println(mul(4.015, 100000000));
	System.out.println(div(123.3, 100));
	System.out.println(div(123.35, 100));
	System.out.println(div(125, 100,1));
	System.out.println(formatCommaAnd2Point(round(123456.40,3)));
	System.out.println(formatCommaAnd2Point(123456.7895));
	System.out.println(getAappreciationRate(1146.906,1123.69));
	System.out.println(formatCommaAnd2Point(div(2000000000, 10)));
	System.out.println("18位 Double.parseDouble(2000000000000000000+'')==="+ Double.parseDouble("2000000000000000000"));
	System.out.println("new BigDecimal(2000000000).longValue()==="+new BigDecimal("2000000000000000000").longValue());
	
	System.out.println("new BigDecimal(2000000000).longValueExact()==="+new BigDecimal(2000000000).longValueExact());
	
	System.out.println("new BigDecimal(2000000000).movePointLeft(9)==="+new BigDecimal(2000000000+"").movePointLeft(9));
	System.out.println(divForBigDecimal(2000000000, 10,10));
	System.out.println(divForBigDecimal(Double.parseDouble(2000000000+""), Double.parseDouble(10+""),10));
	System.out.println("new BigDecimal(2000000000).doubleValue(), new BigDecimal(10).doubleValue(),10)==="+ divForBigDecimal(new BigDecimal(2000000000).doubleValue(), new BigDecimal(10).doubleValue(),10));
	
	
	DecimalFormat df = new DecimalFormat("0");

	Double d = new Double("1.39754587101234E10");

	System.out.println("df.format(d)==="+df.format(d));
	System.out.println("new BigDecimal(d).toString()==="+new BigDecimal(d).toString());
	System.out.println(" formatCommaAnd2Point(d);==="+ formatCommaAnd2Point(d));
	System.out.println(" formatCommaAnd4Point(d);==="+ formatCommaAnd4Point(null));
	//上面如果有小数就截掉了，这个好一点 new BigDecimal(d).toString()
	
	
	
	//System.out.println(new BigDecimal(2000000000,1));
	*//*BigDecimal b = new BigDecimal(Double.toString(1.20000));
	   BigDecimal one = new BigDecimal("1");
	   int scale=4;
	   System.out.println(b.divide(one, scale, BigDecimal.ROUND_HALF_UP).doubleValue());*//*
}*/
}

