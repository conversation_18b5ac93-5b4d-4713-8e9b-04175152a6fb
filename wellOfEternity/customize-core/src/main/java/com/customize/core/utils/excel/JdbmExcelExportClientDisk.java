package com.customize.core.utils.excel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.customize.core.utils.excel.jdbm.BreedingCowsData;
import com.customize.core.utils.excel.jdbm.CapableBreedingCows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JdbmExcelExportClientDisk {


    public static Integer count = 0;


    public static List<String> errorList = new ArrayList<>();

    /**
     * 导出能繁母牛
     *
     * @param response         ：返回对象
     * @param exportPath       ：excel模板地址名称
     * @param excelName        ：excel名称
     * @param mergeColumnIndex ：合并哪些列
     * @param mergeRowIndex    合并开始的行
     * @param dataInfo         ：填充值
     * @param dataList         ：数据值
     * @param endMap           ：填充值
     * @throws Exception
     */
    public void exportCapableBreedingCows(HttpServletResponse response, String exportPath, String excelName, int[] mergeColumnIndex,
                                          int mergeRowIndex, BreedingCowsData dataInfo, LinkedList<CapableBreedingCows> dataList,
                                          Map<String, String> endMap) throws Exception {
        try {
            URL url = new URL(exportPath);
            InputStream inputStream = url.openStream();
            //流处理
            /**
             endMap.put("townSign",townSign);
             endMap.put("villageSign",villageSign);
             */
            String townSign = dataInfo.getTownName();
            String villageSign = dataInfo.getVillageName();
            String path = "D:\\exportData3\\榆阳区" + "\\" + townSign + "\\" + villageSign;
            File file = new File(path);
            //创建文件夹
            if (!file.exists()) {
                file.mkdirs();
            }
            if (excelName.contains(":")) {
                String[] split = excelName.split(":");
                excelName = split[split.length - 1];
            }
            ExcelWriter excelWriter = EasyExcel.write(path + "\\" + excelName + ".xlsx")
                    .withTemplate(inputStream)
                    .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumnIndex))
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // Bean对象 转换成 Map
            Map<String, Object> dataMap = BeanUtil.beanToMap(dataInfo);
            dataMap.putAll(endMap);
            log.debug("dataMap:{},\n\tdataList:{}", JSON.toJSONString(dataMap), JSON.toJSONString(dataList));
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(dataMap, fillConfig, writeSheet);
            excelWriter.fill(dataList, fillConfig, writeSheet);
            excelWriter.fill(nullData(dataList, Integer.valueOf(dataInfo.getCowCattleCoount())), fillConfig, writeSheet);
            excelWriter.finish();
            inputStream.close();
            System.out.println("成功导出：" + ++count + "个");
            //bos.flush();
            //bos.close();
        } catch (Exception e) {
            errorList.add(endMap.get("farmId"));
            log.info("出错了 farmId->:【{}】", endMap.get("farmId"));
        }

    }


    public List<CapableBreedingCows> nullData(List<CapableBreedingCows> dataList, Integer length) {
        if (CollectionUtil.isNotEmpty(dataList)){
            // 使用subList截取前半部分
            // 截取前一半数据
            dataList = dataList.subList(0, dataList.size() / 2);
            return dataList.stream()
                    .map(x -> {
                        x.setIndex("");
                        x.setCowEarNumber("");
                        x.setCowInTheColumn("/");
                        x.setCalfEarNumber("");
                        x.setBirthDate("");
                        x.setBreedMethod("〇本交  〇人工");
                        x.setSubsidySign("/");
                        x.setCalfInTheColumn("/");
                        return x;
                    })
                    .collect(Collectors.toList());
        }else {
            List<CapableBreedingCows> resultList = new ArrayList<>();
            for (int i = 0; i < 10; i++) {
                CapableBreedingCows x = new CapableBreedingCows();
                x.setIndex("");
                x.setCowEarNumber("");
                x.setCowInTheColumn("/");
                x.setCalfEarNumber("");
                x.setBirthDate("");
                x.setBreedMethod("〇本交  〇人工");
                x.setSubsidySign("/");
                x.setCalfInTheColumn("/");
                resultList.add(x);
            }
            return resultList;
        }
    }
}
