package com.customize.core.annotation;



import com.customize.core.enmus.BusinessType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @ClassName: Log
 * @Description: TODO
 * @date 2023-04-25 10:21
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperateLog {

    /**
     * 主题
     * @return
     */
    String title() default "";

    /**
     * 操作类型
     * @return
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 是否保存请求的参数
     * @return
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存相应的参数
     * @return
     */
    boolean isSaveResponseData() default true;
}
