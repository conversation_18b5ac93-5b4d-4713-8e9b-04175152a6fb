package com.customize.core.utils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023-08-29 9:14
 * @PackageName:com.xmd.xiaomuding.customize.core.utils
 * @ClassName: MapUtils
 * @Description: TODO
 * @Version 1.0
 */
public class MapUtils {



    /**
     * <AUTHOR>
     * @Description //排序 根据value
     * @Date 9:15 2023-08-29
     * @return java.util.Map<java.lang.String,java.lang.Integer>
     * @Param [java.util.Map<java.lang.String,java.lang.Integer>]
    */
    public static Map<String, Integer> sortMap(Map<String, Integer> map) {
        //利用Map的entrySet方法，转化为list进行排序
        List<Map.Entry<String, Integer>> entryList = new ArrayList<>(map.entrySet());
        //利用Collections的sort方法对list排序
        Collections.sort(entryList, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                //正序排列，倒序反过来
                return o1.getValue() - o2.getValue();
            }
        });
        //遍历排序好的list，一定要放进LinkedHashMap，因为只有LinkedHashMap是根据插入顺序进行存储
        LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<String, Integer>();
        for (Map.Entry<String,Integer> e : entryList
        ) {
            linkedHashMap.put(e.getKey(),e.getValue());
        }
        return linkedHashMap;
    }
}
