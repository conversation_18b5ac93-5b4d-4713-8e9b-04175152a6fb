package com.customize.core.enmus;

import lombok.Getter;

/**
 * @ClassName 公共状态
 * @Description 公共状态
 */
@Getter
public enum CommonStatus {

    NORMAL(0,"正常"),
    
    CLOSE(1,"关闭"),
    
    SMS_OK(1,"正常"),
    
    ;
    
    /**
     * 值
     */
    private Integer value;
    
    /**
     * 介绍
     */
    private String description;
    
    CommonStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
    
    public Integer getValue() {
        return value;
    }
    
    public void setValue(Integer value) {
        this.value = value;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
}