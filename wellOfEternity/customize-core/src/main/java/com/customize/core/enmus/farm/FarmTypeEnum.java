package com.customize.core.enmus.farm;

/**
 * <AUTHOR> admin
 * @date : 2023/6/14
 * @description :
 */
public enum FarmTypeEnum {
	
	/**
	 * 1 - 规模养殖场
	 */
	COMPANY_FARM_TYPE(1, "企业"),
	COOPERATIVE_FARM_TYPE(2, "合作社"),
	
	/**
	 * 2 - 中小养殖户
	 */
	INDIVIDUAL_FARM_TYPE(3, "个体工商户"),
	
	GENERAL_FARM_TYPE(4, "一般养殖户"),
	;
	
	/**
	 *  值
	 */
	private Integer value;
	/**
	 * 描述
	 */
	private String valDesc;
	
	public Integer getValue() {
		return value;
	}
	
	public void setValue(Integer value) {
		this.value = value;
	}
	
	public String getValDesc() {
		return valDesc;
	}
	
	public void setValDesc(String valDesc) {
		this.valDesc = valDesc;
	}
	
	FarmTypeEnum(Integer value, String valDesc) {
		this.value = value;
		this.valDesc = valDesc;
	}
	
	/**
	 * Gets enum.
	 * @param value the code
	 * @return the enum
	 */
	public static FarmTypeEnum getFarmType(Integer value) {
		for (FarmTypeEnum typeEnum : FarmTypeEnum.values()) {
			if (value.intValue() == typeEnum.getValue().intValue()) {
				return typeEnum;
			}
		}
		return null;
	}
}
