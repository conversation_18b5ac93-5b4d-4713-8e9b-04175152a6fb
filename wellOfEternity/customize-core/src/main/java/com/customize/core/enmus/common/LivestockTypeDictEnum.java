package com.customize.core.enmus.common;
/**
 * 牲畜类别 0:其他 1：羊 2：猪 3：牛 4：驴 5：鸡
 */
public enum LivestockTypeDictEnum {
	
	/**
	 * 其他 字典对应的code 
	 */
	OTHER_LIVESTOCK_DESC("0","other_type"),
	/**
	 * 羊 字典对应的code 
	 */
	SHEEP_LIVESTOCK_DESC("1","sheep_type"),
	/**
	 * 猪 字典对应的code 
	 */
	PIG_LIVESTOCK_DESC("2","variety_type"),
	/**
	 * 牛 字典对应的code 
	 */
	CATTLE_LIVESTOCK_DESC("3","cattle_type"),
	/**
	 * 驴 字典对应的code 
	 */
	DONKEY_LIVESTOCK_DESC("4","donkey_type"),
	/**
	 * 鸡 字典对应的code 
	 */
	CHICKEN_LIVESTOCK_DESC("5","chicken_type"),
	
    ;
	
	/**
	 * 畜只类型的值 d_sys_dict 表中 livestock_type 的值
	 */
    private String livestockTypeVal;
	/**
	 * 字典对应的种类
	 */
    private String livestockTypeDict;
	
	public String getLivestockTypeVal() {
		return livestockTypeVal;
	}
	
	public void setLivestockTypeVal(String livestockTypeVal) {
		this.livestockTypeVal = livestockTypeVal;
	}
	
	public String getLivestockTypeDict() {
		return livestockTypeDict;
	}
	
	public void setLivestockTypeDict(String livestockTypeDict) {
		this.livestockTypeDict = livestockTypeDict;
	}
	
	LivestockTypeDictEnum(String livestockTypeVal, String livestockTypeDict) {
		this.livestockTypeVal = livestockTypeVal;
		this.livestockTypeDict = livestockTypeDict;
	}
	
	/**
	 * @param livestockType 
	 * @return 
	 */
	public static String getLivestockType(String livestockType) {
		for (LivestockTypeDictEnum typeDict : LivestockTypeDictEnum.values()) {
			if (livestockType.equalsIgnoreCase(typeDict.getLivestockTypeVal())) {
				return typeDict.livestockTypeDict;
			}
		}
		return null;
	}
}
