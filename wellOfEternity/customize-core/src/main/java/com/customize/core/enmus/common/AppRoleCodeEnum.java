package com.customize.core.enmus.common;

/**
 *"操作角色类型
 */
public enum AppRoleCodeEnum {
	/*********************************************************************************************************** 畜牧平台 *************************************************************************************/
	/**
	 * 防疫员
	 */
    IMMUNIZER("ZHXM_IMMUNIZER","防疫员"),
	/**
	 * 操作员
	 */
    OPERATOR("ZHXM_OPERATOR","操作员"),
	/**
	 * 管理员
	 */
    ADMINISTRATOR("ZHXM_ADMINISTRATOR","管理员"),
	
	/*********************************************************************************************************** 见犊补母应用 *************************************************************************************/
	
	/**
	 * 见犊补母操作员
	 */
	JDBM_OPERATOR("JDBM_OPERATER","操作员|信息员"),
	/**
	 * 见犊补母管理员
	 */
	JDBM_MANAGER("JDBM_MANAGER","管理员"),
	/**
	 * 农场主
	 */
	JDBM_FARMER("JDBM_FARMER","牧场主"),
	/**
	 * 查看员
	 */
	JDBM_REVIEWER("JDBM_REVIEWER","查看员"),
	
	/*********************************************************************************************************** 小牧丁金融应用 *************************************************************************************/
	/**
	 * 贷款用户
	 */
	AMOUNT_LOAN_USER("AMOUNT_LOAN_USER","贷款用户"),
	/**
	 * 机构超级管理员
	 */
	INSTITUTION_SUPER_ADMINISTRATOR("INSTITUTION_SUPER_ADMINISTRATOR","机构超级管理员"),
	/**
	 * 机构管理员
	 */
	INSTITUTION_ADMINISTRATOR("INSTITUTION_ADMINISTRATOR","机构管理员"),
	/**
	 * 机构业务员
	 */
	INSTITUTION_OPERATOR("INSTITUTION_OPERATOR","机构业务员"),
	/**
	 * 后台小牧丁管理员
	 */
	SYSTEM_XMD_ADMINISTRATOR("SYSTEM_XMD_ADMINISTRATOR","后台小牧丁管理员"),
	/**
	 * 后台监督管理员
	 */
	SYSTEM_WATCH_ADMINISTRATOR("SYSTEM_WATCH_ADMINISTRATOR","后台监督管理员"),
	
    ;

    private String value;
    private String description;
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
	
	AppRoleCodeEnum(String value, String description) {
		this.value = value;
		this.description = description;
	}
}
