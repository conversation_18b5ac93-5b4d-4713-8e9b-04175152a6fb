package com.customize.core.interceptor;

import cn.hutool.core.util.ObjectUtil;
import com.customize.core.config.PreventAttackingConfig;
import com.customize.core.exception.XMDServiceException;
import com.customize.core.utils.IpUtils;
import com.customize.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.customize.core.constant.CustomizedCoreConstant.AUTO_ATTACKING_LOCK;
import static com.customize.core.constant.CustomizedCoreConstant.MD5Str;

/**
 * <AUTHOR>
 * @ClassName: AutoIdempotentConfig
 * @Description: 访问限制拦截器
 * @date 2023-04-21 11:23
 */
@Slf4j
@Component
@Import(PreventAttackingConfig.class)
public class AccessLimitInterceptor implements HandlerInterceptor {
	SimpleDateFormat sf = new SimpleDateFormat("YYYYMMdd");
	@Resource
	private PreventAttackingConfig action;

	@Override
	// 在请求处理之前执行的方法，用于控制请求频率
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
		// 记录调试信息，显示当前的控制动作
		log.debug("action【{}】", action);
		// 判断是否启用了请求控制。如果控制未启用或状态为false，则直接放行请求
		if (ObjectUtil.isEmpty(action.getStatus()) || !action.getStatus()) {
			log.debug("请求控流全局开关未打开 直接放行");
			return Boolean.TRUE;
		}
		// 检查当前请求的IP地址是否已达到最大请求限制
		if (isMax(IpUtils.getIpAddr(request))) {
			log.debug("ip【{}】 超出最大限制【{}】", IpUtils.getIpAddr(request), action.getMax());
			// 如果超出最大限制，则抛出自定义服务异常
			throw new XMDServiceException(action.getMessage());
		}
		// 构建当前请求的唯一标识key，包括IP地址、请求URI、查询字符串和POST数据的MD5值
		String key = AUTO_ATTACKING_LOCK
				.concat(":")
				.concat(IpUtils.getIpAddr(request))
				.concat("_")
				.concat(request.getRequestURI())
				.concat("_" + MD5Str.concat(request.getQueryString()))
				.concat("_" + MD5Str.concat(getPostData(request)));
		// 从Redis中获取当前key对应的访问次数，如果不存在则为0
		Integer localUrlCount = RedisUtils.isExist(key) ? Integer.valueOf(RedisUtils.get(key).toString()) : 0;
		log.info("key【{}】 localIpMax【{}】 max【{}】", key, localUrlCount, action.getTime());
		// 如果是第一次访问或者访问次数未超过限制，则更新Redis中的计数器，并设置过期时间
		if (localUrlCount == 0 || localUrlCount <= action.getLimit().shortValue()) {
			RedisUtils.setExp(key, ++localUrlCount, Long.valueOf(action.getTime()), TimeUnit.SECONDS);
			return true;
		}
		// 如果访问次数超出了限制，则抛出自定义服务异常
		throw new XMDServiceException(action.getMessage());
	}


	/**
	 * 判断当前ip是否达到请求上线
	 *
	 * @return
	 */
	// 检查特定IP地址的请求次数是否达到了设定的最大限制
	public Boolean isMax(String ip) {
		// 构建Redis键，包括锁前缀、当前日期和IP地址
		// 这样可以确保每天对同一个IP的计数是独立的
		String key = AUTO_ATTACKING_LOCK
				.concat(sf.format(new Date()))
				.concat(":")
				.concat(ip);
		log.info("开始判断当前ip是否达到请求上限 key:【{}】", key);

		// 如果Redis中不存在该键，意味着这是该IP今天的第一次请求
		if (!RedisUtils.isExist(key)) {
			log.info("第一次请求 key:【{}】", key);
			// 将该键值对设置到Redis中，并初始化计数为1，设置过期时间为1天
			RedisUtils.set(key, 1, 1L, TimeUnit.DAYS);
			// 返回FALSE，表示该IP的请求次数尚未达到最大限制
			return Boolean.FALSE;
		}

		// 如果键已存在，从Redis中获取当前IP的请求次数
		Integer localIpMax = Integer.valueOf(RedisUtils.get(key).toString());
		log.info("localIpMax【{}】 max【{}】", localIpMax, action.getMax());

		// 检查当前请求次数是否小于设定的最大值
		if (localIpMax < action.getMax()) {
			// 如果未达到最大限制，请求次数加1
			RedisUtils.incr(key);
			// 返回FALSE，表示请求尚未达到最大限制
			return Boolean.FALSE;
		}

		// 如果达到或超过最大限制，返回TRUE
		return Boolean.TRUE;
	}


	// 从HttpServletRequest中获取POST请求的请求体数据
	public static String getPostData(HttpServletRequest request) {
		// 使用StringBuilder来累积读取到的数据
		StringBuilder data = new StringBuilder();
		String line;
		BufferedReader reader;
		try {
			// 获取请求的读取器对象
			reader = request.getReader();
			// 循环读取请求体中的每一行数据，直到读取完毕
			while (null != (line = reader.readLine())) {
				// 将读取到的数据追加到StringBuilder对象中
				data.append(line);
			}
		} catch (IOException e) {
			// 如果读取过程中发生IO异常，则返回null
			return null;
		}
		// 将累积的数据转换成String返回
		return data.toString();
	}

}

