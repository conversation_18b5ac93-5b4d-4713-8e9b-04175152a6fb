package com.customize.core.enmus.common;
/**
 * '牧场经营模式:1 育肥, 2.育种'
 */
public enum BusinessModelEnum {

    FATTEN_TYPE(1,"育肥"),
    BREED_TYPE(2,"育种")
    ;
    private int code;
    private String msg;

    /**
     * Msg string.
     *
     * @return the string
     */
    public String msg() {
        return msg;
    }

    /**
     * Code int.
     *
     * @return the int
     */
    public int code() {
        return code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    BusinessModelEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Gets enum.
     *
     * @param code the code
     *
     * @return the enum
     */
    public static BusinessModelEnum getEnum(int code) {
        for (BusinessModelEnum ele : BusinessModelEnum.values()) {
            if (ele.code() == code) {
                return ele;
            }
        }
        return null;
    }
    
    /**
     * Gets enum.
     *
     * @param codes the code
     *
     * @return the enum
     */
    public static String getType(String codes) {
        String[] codeStrs = codes.split(",");
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < codeStrs.length; i++) {
            for (BusinessModelEnum ele : BusinessModelEnum.values()) {
                if (codeStrs[i].equals(ele.code()+"")) {
                    stringBuffer.append(ele.msg);
                }
            }
            if (i+1  <   codeStrs.length){
                stringBuffer.append(",");
            }
        }
        return stringBuffer.toString();
    }


    public static void main(String[] args) {
        String codes ="1,2";
        String type = getType(codes);
        System.out.println(type);
    }
}
