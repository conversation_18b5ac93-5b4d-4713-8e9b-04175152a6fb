package com.customize.core.annotation;



import com.customize.core.enmus.DistributeLockTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @ClassName: DistributedLock 使用demo见 com.xmd.xiaomuding.livestockbiz.aspect.DistributedLockTestController
 * @Description: 分布式索自定义注解
 * @date 2023-04-19 11:21
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLock {
	/**
	 * 分布式锁key  如需要动态从参数配置  #参数.属性: #Param.filed1.filed2
	 */
	String key() default "";

	/**
	 * 等待锁时间 单位 默认为秒
	 */
	long waitTime() default 5;

	/**
	 * 租界/使用锁的时间 单位 默认为秒
	 */
	long leaseTime() default 10;
	/**
	 * 是否需要等待业务 默认不等待直接排除异常 默認当做幂等性处理
	 */
	boolean needWait() default false;
	/**
	 * 锁的容器 TODO 目前只用到了redis分布式锁 其余均未实现
	 * @return
	 */
	DistributeLockTypeEnum lockType() default DistributeLockTypeEnum.REDIS;
}
