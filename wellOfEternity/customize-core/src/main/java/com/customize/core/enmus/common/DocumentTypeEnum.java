package com.customize.core.enmus.common;

/**
 * 文档类型
 * <AUTHOR>
 */
public enum DocumentTypeEnum {

	/**
	 * 规程管理
	 */
	REGULATIONS_MANAGEMENT (1,"规程管理"),
	/**
	 * 计划管理
	 */
	PLANNED_MANAGEMENT(2,"计划管理"),
	/**
	 * 档案管理
	 */
	FILE_MANAGEMENT (3,"档案管理"),

	SLAUGHTER_PROCESS_MANAGEMENT (4,"屠宰流程管理"),

	;

    private Integer value;
    private String descr;

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	DocumentTypeEnum(Integer value, String descr) {
		this.value = value;
		this.descr = descr;
	}
	
}
