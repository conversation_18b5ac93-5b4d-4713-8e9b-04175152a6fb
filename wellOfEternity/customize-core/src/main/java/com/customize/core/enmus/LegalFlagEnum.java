package com.customize.core.enmus;

import lombok.Getter;

/**
 *我是法人(0.否 ,  1.是)
 */
@Getter
public enum LegalFlagEnum {

    NO(0,"否"),
    
    YES(1,"是"),
    
    ;

    private Integer value;
    private String description;
    
    public Integer getValue() {
        return value;
    }
    
    public void setValue(Integer value) {
        this.value = value;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    LegalFlagEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
