package com.idempotent.aspect;

import com.idempotent.annotation.Idempotent;
import com.idempotent.expression.KeyResolver;
import com.idempotent.exception.IdempotentException;
import com.idempotent.utils.IpUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.Redisson;
import org.redisson.api.RMapCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static jodd.util.StringPool.COLON;

/**
 * <AUTHOR>
 */
@Aspect
public class IdempotentAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(IdempotentAspect.class);

    private static final ThreadLocal<Map<String, Object>> THREAD_CACHE = ThreadLocal.withInitial(HashMap::new);
    private static final ThreadLocal<String> REDIS_KEY = new InheritableThreadLocal<>();

    private static final String RMAPCACHE_KEY = "idempotent";

    private static final String KEY = "key";

    private static final String DELKEY = "delKey";

    @Autowired
    private Redisson redisson;

    @Autowired
    private KeyResolver keyResolver;

    @Pointcut("@annotation(com.idempotent.annotation.Idempotent)")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void beforePointCut(JoinPoint joinPoint) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        if (!method.isAnnotationPresent(Idempotent.class)) {
            return;
        }
        Idempotent idempotent = signature.getMethod().getAnnotation(Idempotent.class);
        String key;  //TODO key 添加ip属性 ip隔离
        // 若没有配置 幂等 标识编号，则使用 url + 参数列表作为区分
        if (!StringUtils.hasLength(idempotent.key())) {
            key = request.getRequestURL()
                    .toString()
                    .concat(COLON)
                    .concat(Base64Utils.encodeToString(Arrays.asList(joinPoint.getArgs()).toString().getBytes()))
                    .concat(COLON)
                    .concat(IpUtils.getIpAddr(request));
        } else {
            // 使用jstl 规则区分
            key = keyResolver.resolver(idempotent, joinPoint);
        }
        // do not need check null
        RMapCache<String, Object> rMapCache = redisson.getMapCache(RMAPCACHE_KEY);
        String value = LocalDateTime.now().toString().replace("T", " ");
        if (null != rMapCache.get(key)) {
            // had stored
            throw new IdempotentException(idempotent.info());
        }
        synchronized (this) {
            Object v1 = rMapCache.putIfAbsent(key, value, idempotent.expireTime(), idempotent.timeUnit());
            if (null != v1) {
                throw new IdempotentException(idempotent.info());
            }
            LOGGER.debug("[idempotent]:has stored key={},value={},expireTime={}{},now={}", key, value, idempotent.expireTime(),
                    idempotent.timeUnit(), LocalDateTime.now());
        }
        Map<String, Object> map = THREAD_CACHE.get();
        map.put(KEY, key);
        map.put(DELKEY, idempotent.delKey());
    }

    @After("pointCut()")
    public void afterPointCut(JoinPoint joinPoint) {
        Map<String, Object> map = THREAD_CACHE.get();
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        RMapCache<Object, Object> mapCache = redisson.getMapCache(RMAPCACHE_KEY);
        if (mapCache.size() == 0) {
            return;
        }
        if(map.containsKey(KEY)){
            String key = map.get(KEY).toString();
            if ((boolean) map.get(DELKEY)) {
                mapCache.fastRemove(key);
                LOGGER.debug("[idempotent]:has removed key={}", key);
            }
            THREAD_CACHE.remove();
        }
    }

    @AfterThrowing("pointCut()")
    public void AfterThrowingPointCut(JoinPoint joinPoint) {
        String key = findKey(joinPoint);
        RMapCache<String, Object> rMapCache = redisson.getMapCache(RMAPCACHE_KEY);
        if (rMapCache.containsKey(key)) {
            rMapCache.remove(key);
        }
        Map<String, Object> map = THREAD_CACHE.get();
        map.remove(KEY, key);
    }


    public String findKey(JoinPoint joinPoint) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Idempotent idempotent = signature.getMethod().getAnnotation(Idempotent.class);
        String key;  //TODO key 添加ip属性 ip隔离
        // 若没有配置 幂等 标识编号，则使用 url + 参数列表作为区分
        if (!StringUtils.hasLength(idempotent.key())) {
            key = request.getRequestURL()
                    .toString()
                    .concat(COLON)
                    .concat(Base64Utils.encodeToString(Arrays.asList(joinPoint.getArgs()).toString().getBytes()))
                    .concat(COLON)
                    .concat(IpUtils.getIpAddr(request));
        } else {
            // 使用jstl 规则区分
            key = keyResolver.resolver(idempotent, joinPoint);
        }
        REDIS_KEY.set(key);
        return key;
    }


}
