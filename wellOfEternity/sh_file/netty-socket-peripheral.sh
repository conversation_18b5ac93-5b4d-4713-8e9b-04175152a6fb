#!/bin/bash
ID=`ps -ef | grep xiaomuding-netty-socket-peripheral.jar | grep -v grep | awk '{print $2}'`
echo $ID
for id in $ID
do
kill -9 $id
echo "kill $id"
done

echo "start"
nohup java -Dfile.encoding=utf-8 -Xms512m -Xmx1024m -jar /mnt/appserver/services/xiaomuding-netty-socket-peripheral.jar >/dev/null 2>&1 & 
echo "success"


echo "tail -200f /mnt/applogs/xiaomuding-netty-socket-peripheral/debug.log"

