package com.swagger.annotation;

import com.core.factory.YamlPropertySourceFactory;
import com.swagger.config.OpenAPIDefinitionImportSelector;
import com.swagger.support.SwaggerProperties;


import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;

import java.lang.annotation.*;

/**
 * 开启 pig spring doc
 *
 * <AUTHOR>
 * @date 2024/7/25 14:00
 */
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableConfigurationProperties(SwaggerProperties.class)
@Import({ OpenAPIDefinitionImportSelector.class })
@PropertySource(value = "classpath:openapi-config.yaml", factory = YamlPropertySourceFactory.class)
public @interface EnableOpenApi {

	/**
	 * 网关路由前缀
	 * @return String
	 */
	String value() default "";

	/**
	 * 是否是微服务架构
	 * @return true
	 */
	boolean isMicro() default true;

}
