<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rb</groupId>
        <artifactId>wellOfEternity</artifactId>
        <version>1.0.0.RELEASE</version>
    </parent>

    <groupId>com.rb</groupId>
    <artifactId>core</artifactId>
    <packaging>jar</packaging>

    <description>公共工具类核心包</description>


    <properties>
        <simon.version>1.0.0.RELEASE</simon.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <pagehelper.version>1.4.6</pagehelper.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <druid.version>1.2.9</druid.version>
        <hutool.version>5.8.16</hutool.version>
        <mysql.connector.version>8.0.32</mysql.connector.version>
        <oracle.version>********</oracle.version>
        <sqlserver.version>8.4.1.jre8</sqlserver.version>
        <dm.version>********</dm.version>
        <knife4j.version>3.0.3</knife4j.version>
        <swagger.core.version>2.2.0</swagger.core.version>
        <springdoc.version>1.6.9</springdoc.version>
        <mp.weixin.version>4.4.0</mp.weixin.version>
        <ijpay.version>2.8.0</ijpay.version>
        <groovy.version>3.0.3</groovy.version>
        <jsoup.version>1.13.1</jsoup.version>
        <security.oauth.version>2.3.6.RELEASE</security.oauth.version>
        <fastjson.version>1.2.83</fastjson.version>
        <xxl.job.version>2.3.0</xxl.job.version>
        <aliyun.version>3.0.52.ALL</aliyun.version>
        <aws.version>1.11.543</aws.version>
        <javers.version>6.10.0</javers.version>
        <seata.version>1.6.1</seata.version>
        <asm.version>7.1</asm.version>
        <log4j2.version>2.17.1</log4j2.version>
        <javaformat.plugin.version>0.0.23</javaformat.plugin.version>
        <docker.plugin.version>0.33.0</docker.plugin.version>
        <cloud.plugin.version>1.0.0</cloud.plugin.version>
        <sentinel.version>1.8.4</sentinel.version>
        <jasypt.version>3.0.5</jasypt.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <jaxb.version>2.3.5</jaxb.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.10.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.10.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.10.5</version>
        </dependency>
        <!--mvc 相关配置-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--hutool-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <!--server-api-->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>
        <!--hibernate-validator-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!--json模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <!--TTL-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${ttl.version}</version>
        </dependency>
        <!--swagger 依赖-->
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>5.1.1</version>
        </dependency>
        <!--jasypt配置文件加解密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <!-- JAVA-->
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>${jaxb.version}</version>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
    </dependencies>
</project>
