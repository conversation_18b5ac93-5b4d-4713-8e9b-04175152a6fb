

package com.core.util;

import cn.hutool.json.JSONUtil;
import com.core.constant.CommonConstants;
//import com.core.util.aes.AESUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 响应信息主体
 * <AUTHOR>
 * @date 2024-04-10
 */
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "响应信息主体")
public class AesR<T> implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	@Getter
	@Setter
	@Schema(description = "返回标记：成功标记=0，失败标记=1")
	private int code;
	
	@Getter
	@Setter
	@Schema(description = "返回信息")
	private String msg;
	
	@Getter
	@Setter
	@Schema(description = "是否aes加密")
	private boolean aesSign;
	
	@Getter
	@Setter
	@Schema(description = "数据")
	private Object data;
	
	public static <T> AesR<T> ok() {
		return aesRestResult(null, CommonConstants.SUCCESS, null);
	}
	
	public static <T> AesR<T> ok(T data) {
		return aesRestResult(data, CommonConstants.SUCCESS, null);
	}
	
	public static <T> AesR<T> ok(T data, String msg) {
		return aesRestResult(data, CommonConstants.SUCCESS, msg);
	}
	
	public static <T> AesR<T> ok(T data, Integer successCode, String msg) {
		return aesRestResult(data, successCode, msg);
	}
	
	static <T> AesR<T> aesRestResult(T data, int code, String msg) {
		AesR<T> apiResult = new AesR<>();
		apiResult.setCode(code);
		String json = JSONUtil.toJsonStr(data);
		try {
			//apiResult.setData(AESUtils.encrypt(json));
			apiResult.setAesSign(true);
		}catch (Exception e){
			apiResult.setData(data);
			apiResult.setAesSign(false);
		}
		apiResult.setMsg(msg);
		return apiResult;
	}
	
}
