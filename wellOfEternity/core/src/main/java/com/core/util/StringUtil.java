package com.core.util;


import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class StringUtil {
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static String toString(Object s) {
        if (s == null) {
            return "";
        } else {
            return s+"";
        }
    }

	public static String BigDecimalToString(BigDecimal b){
		if (b == null) {
			return "";
		}
		String[] split = b.toString().split("\\.");
		if (split.length > 1 && (StringUtils.equals(split[1], "0") || StringUtils.equals(split[1], "00"))) {
			return split[0];
		}
		return b.toString();
	}

    public static Date queryUTCStringToDate(String dateString){
        Date date = new Date();
        if (StringUtils.isNotBlank(dateString)) {
            try {
                date = DateUtil.parseUTC(dateString).toJdkDate();
            }catch (Exception e) {
                log.info("时间转换异常,dateString:{}, e:{}", dateString, e);
            }
        }
        return date;
    }
    /**
     * 通过正则判断
     * @return
     */

    public static boolean checkNumberWithRegex(String numStr){
        String pattern = "^1[\\d]{10}";
        boolean isMatch = Pattern.matches(pattern, numStr);
        return isMatch;
    }

	private static final Pattern pattern =
			Pattern.compile("\\$\\{(.*?)\\}");
//			Pattern.compile("\\{(.*?)\\}");
	private static Matcher matcher;
	/**
	 * 格式化字符串 字符串中使用{key}表示占位符
	 *
	 * @param sourStr
	 *            需要匹配的字符串
	 * @param param
	 *            参数集
	 * @return
	 */
	public static String format(String sourStr, Map<String, Object> param) {
		String tagerStr = sourStr;
		if (param == null) {
			return tagerStr;
		}
		try {
			matcher = pattern.matcher(tagerStr);
			while (matcher.find()) {
				String key = matcher.group();
				String keyclone = key.substring(1, key.length() - 1).trim();
				Object value = param.get(keyclone);
				if (value != null) {
					tagerStr = tagerStr.replace(key, value.toString());
				}
			}
		}catch (Exception e){
			return null;
		}
		return tagerStr;
	}

	/**
	 * 字符串长度截取
	 * @param s 需截取的字符串
	 * @param length 需截取的长度
	 * @return
	 */
	public static String StringCutOut(String s, Integer length){
		return s.length()>length? s.substring(0, length) : s;
	}

	/**
	 * 校验字符串是否全为数字
	 * @param str
	 * @return
	 */
	public static boolean isNumeric(String str){
		Pattern pattern = Pattern.compile("[0-9]*");
		return pattern.matcher(str).matches();
	}

	/**
	 * 获取一定长度的随机字符串
	 * @param length 指定字符串长度
	 * @return 一定长度的字符串
	 */
	public static String getRandomStringByLength(int length) {
		String base = "abcdefghijklmnopqrstuvwxyz0123456789";
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	/**
	 * 去除按逗号分割的某字符串中的其中一个元素,例如(字符串'a,b,c,d'中，去除'b',最后得到'a,c,d')
	 * @return
	 */
	public static String removeStr(String str, String r) {
		return Arrays.stream(str.split(",")).filter(s -> !StringUtils.equals(s, r)).collect(Collectors.joining(","));
	}
}
