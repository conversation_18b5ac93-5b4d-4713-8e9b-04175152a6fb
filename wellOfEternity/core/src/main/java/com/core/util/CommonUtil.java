package com.core.util;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2024-04-10
 * @description :
 */
public class CommonUtil {
	/**
	 * 获取返回信息
	 * @param startValue
	 * @param endValue
	 * @return
	 */
	public static String getMessage(Object startValue, Object endValue){
		LocalDate startDate = null,endDate = null;
		if (startValue instanceof LocalDate || startValue instanceof LocalDateTime ){
			startDate = (LocalDate) startValue;
		}
		if (endValue instanceof LocalDate || endValue instanceof LocalDateTime ){
			endDate = (LocalDate) endValue;
		}
		Period period = Period.between(startDate,endDate);
		int months = period.getYears()*12 + period.getMonths();
		int days = period.getDays();
		if (months < 1 ){
			return"已出生" + days + "天";
		}else {
			return "已足" + months + "月龄";
		}
	}
	
	/**
	 * 获取返回信息
	 * @param startValue
	 * @param endValue
	 * @return
	 */
	public static String getMonthMsg(Object startValue, Object endValue){
		LocalDate startDate = null,endDate = null;
		if (startValue instanceof LocalDate || startValue instanceof LocalDateTime ){
			startDate = (LocalDate) startValue;
		}
		if (endValue instanceof LocalDate || endValue instanceof LocalDateTime ){
			endDate = (LocalDate) endValue;
		}
		Period period = Period.between(startDate,endDate);
		int months = period.getYears()*12 + period.getMonths();
		if (months < 1 ){
			return "未足月";
		}else {
			return months + "月龄";
		}
	}
	
	/**
	 * 判断连个时间的相差的月数
	 * @param startValue
	 * @param endValue
	 * @return
	 */
	public static int getMonth(LocalDate startValue,LocalDate endValue){
		Period period = Period.between(startValue, endValue);
		return period.getYears()*12 + period.getMonths();
	}
	
	/**
	 * 判断连个时间的相差的月数
	 * @param startValue
	 * @param endValue
	 * @return
	 */
	public static int getMonth(LocalDateTime startValue,LocalDateTime endValue){
		Period period = Period.between(startValue.toLocalDate(),endValue.toLocalDate());
		return period.getYears()*12 + period.getMonths();
	}
	
	/**
	 * 判断连个时间的相差的月数
	 * @param startValue
	 * @param endValue
	 * @return
	 */
	public static int getDays(LocalDate startValue,LocalDate endValue){
		Long days =  ChronoUnit.DAYS.between(startValue, endValue);
		return days.intValue();
	}
	
	/**
	 * 判断连个时间的相差的月数
	 * @param startValue
	 * @param endValue
	 * @return
	 */
	public static int getDays(LocalDateTime startValue,LocalDateTime endValue){
		Long days =  ChronoUnit.DAYS.between(startValue, endValue);
		return days.intValue();
	}
	
	/**
	 * 获取当前时间
	 * @return
	 */
	public static LocalDate getLocalDate(String dateStr){
		LocalDate productTime;
		if (StringUtils.isEmpty(dateStr)){
			return LocalDate.now();
		}else {
			int length = dateStr.trim().length();
			//2023-10-19
			if (length > 10){
				productTime = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			}else{
				productTime = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			}
			return productTime;
		}
	}
	
}
