//package com.core.util.aes;
//
////import sun.misc.BASE64Decoder;
////import sun.misc.BASE64Encoder;
//
//import javax.crypto.Cipher;
//import javax.crypto.spec.IvParameterSpec;
//import javax.crypto.spec.SecretKeySpec;
//
///**
// * <AUTHOR>
// * @date 2024-04-10
// * @description :
// */
//
//public class AESUtils {
//
//	private static final String AES = "AES";
//
//	private static final String CRYPT_KEY = "Simon@!.go";
//
//	private static final String IV_STRING = "<EMAIL>";
//
//	public static void main(String[] args) {
//		String data = "YXBwOmFwcA==";
//		String value = encrypt(data);
//		System.out.println("加密后结果是:"+value);
//		System.out.println("解密后结果是："+decrypt(value));
//	}
//
//	/**
//	 * 加密
//	 * @param content 加密内容
//	 * @return 密文
//	 * @throws Exception e
//	 */
//	public static String encrypt(String content) {
//		byte[] encryptedBytes = new byte[0];
//		try {
//			byte[] byteContent = content.getBytes("UTF-8");
//			// 注意，为了能与 iOS 统一
//			// 这里的 key 不可以使用 KeyGenerator、SecureRandom、SecretKey 生成
//			byte[] enCodeFormat = CRYPT_KEY.getBytes();
//			SecretKeySpec secretKeySpec = new SecretKeySpec(enCodeFormat, AES);
//			byte[] initParam = IV_STRING.getBytes();
//			IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
//			// 指定加密的算法、工作模式和填充方式
//			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
//			cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
//			encryptedBytes = cipher.doFinal(byteContent);
//			// 同样对加密后数据进行 base64 编码
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return new BASE64Encoder().encode(encryptedBytes);
//	}
//
//	/**
//	 * 解密
//	 * @param content 密文
//	 * @return 明文
//	 * @throws Exception e
//	 */
//	public static String decrypt(String content) {
//		// base64 解码
//		try {
//			byte[] encryptedBytes = new BASE64Decoder().decodeBuffer(content);
//			byte[] enCodeFormat = CRYPT_KEY.getBytes();
//			SecretKeySpec secretKey = new SecretKeySpec(enCodeFormat, AES);
//			byte[] initParam = IV_STRING.getBytes();
//			IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
//			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
//			cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
//			byte[] result = cipher.doFinal(encryptedBytes);
//			return new String(result, "UTF-8");
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return null;
//	}
//
//}
