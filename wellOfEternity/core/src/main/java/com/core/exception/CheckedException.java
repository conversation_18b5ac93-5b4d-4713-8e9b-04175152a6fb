package com.core.exception;

import cn.hutool.core.collection.CollectionUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-04-10
 */
@NoArgsConstructor
public class CheckedException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	public CheckedException(String message) {
		super(message);
	}

	public CheckedException(Throwable cause) {
		super(cause);
	}

	public CheckedException(String message, Throwable cause) {
		super(message, cause);
	}

	public CheckedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}
	public static void doThrow(String message) {
		throw new CheckedException(message);
	}


	public static void checkNull(Object obj, String message) {
		if (Objects.isNull(obj)) {
			throw new CheckedException(message);
		}
	}

	public static void checkNotNull(Object obj, String message) {
		if (!Objects.isNull(obj)) {
			throw new CheckedException(message);
		}
	}

	public static void checkEmpty(String obj, String message) {
		if (StringUtils.isEmpty(obj)) {
			throw new CheckedException(message);
		}
	}

	public static void checkEmpty(List obj, String message) {
		if (CollectionUtil.isEmpty(obj)) {
			throw new CheckedException(message);
		}
	}
}
