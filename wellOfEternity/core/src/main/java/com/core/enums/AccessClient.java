package com.core.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-10
 * @description : 请求的客户端
 */
public enum AccessClient {
	/**
	 * app端访问
	 */
	APP_ACCESS("app","app端访问"),
	/**
	 * web端访问
	 */
	WEB_ACCESS("web","web端访问"),
	/**
	 * h5端访问
	 */
	H5_ACCESS("h5","h5端访问"),
	/**
	 * 三方认证端访问
	 */
	SOCIAL_ACCESS("social","第三方认证端访问"),

	BAFU("bafu","八福"),


	;
	
	/**
	 * 值
	 */
	private String value;
	/**
	 * 描述
	 */
	private String desc;
	
	AccessClient(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}
	
	public String getValue() {
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDesc() {
		return desc;
	}
	
	public void setDesc(String desc) {
		this.desc = desc;
	}
	
	/**
	 * 获取登录客户端
	 * @return
	 */
	public static List<String> getClients(){
		List<String> clients = new ArrayList<>();
		AccessClient[] values = AccessClient.values();
		for (AccessClient client : values){
			clients.add(client.value);
		}
		return clients;
	}
}
