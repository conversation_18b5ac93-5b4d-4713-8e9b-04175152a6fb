

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.core.constant.enums.UserTypeEnum;
import com.data.datascope.DataScope;
import com.data.datascope.DataScopeHandle;
import com.data.datascope.DataScopeTypeEnum;
import lombok.RequiredArgsConstructor;


import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-06
 * 默认data scope 判断处理器
 */
@RequiredArgsConstructor
public class SimonDatascopeHandle implements DataScopeHandle {

	//private final RemoteDataScopeService dataScopeService;

	/**
	 * 计算用户数据权限
	 * @param dataScope 数据权限范围
	 * @return
	 */
	@Override
	public Boolean calcScope(DataScope dataScope) {
//		XiaomudingUser user = SecurityUtils.getUser();
//		// toc 客户端不进行数据权限
//		if (UserTypeEnum.TOC.getStatus().equals(user.getUserType())) {
//			return true;
//		}
//
//		List<String> roleIdList = user.getAuthorities().stream().map(GrantedAuthority::getAuthority)
//				.filter(authority -> authority.startsWith(SecurityConstants.ROLE))
//				.map(authority -> authority.split(StrUtil.UNDERLINE)[1]).collect(Collectors.toList());
//
//		List<Long> deptList = dataScope.getDeptList();
//
//		// 当前用户的角色为空 , 返回false
//		if (CollectionUtil.isEmpty(roleIdList)) {
//			return false;
//		}
//		// @formatter:off
//		SysRole role = RetOps.of(dataScopeService.getRoleList(roleIdList))
//				.getData()
//				.orElseGet(Collections::emptyList)
//				.stream()
//				.min(Comparator.comparingInt(SysRole::getDsType)).orElse(null);
//		// @formatter:on
//		// 角色有可能已经删除了
//		if (role == null) {
//			return false;
//		}
//		Integer dsType = role.getDsType();
//		// 查询全部
//		if (DataScopeTypeEnum.ALL.getType() == dsType) {
//			return true;
//		}
//		// 自定义
//		if (DataScopeTypeEnum.CUSTOM.getType() == dsType && StrUtil.isNotBlank(role.getDsScope())) {
//			String dsScope = role.getDsScope();
//			deptList.addAll(
//					Arrays.stream(dsScope.split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList()));
//		}
//		// 查询本级及其下级
//		if (DataScopeTypeEnum.OWN_CHILD_LEVEL.getType() == dsType) {
//			// @formatter:off
//			List<Long> deptIdList = RetOps.of(dataScopeService.getDescendantList(user.getDeptId()))
//					.getData()
//					.orElseGet(Collections::emptyList)
//					.stream()
//					.map(SysDept::getDeptId).collect(Collectors.toList());
//			// @formatter:on
//			deptList.addAll(deptIdList);
//		}
//		// 只查询本级
//		if (DataScopeTypeEnum.OWN_LEVEL.getType() == dsType) {
//			deptList.add(user.getDeptId());
//		}
//
//		// 只查询本人
//		if (DataScopeTypeEnum.SELF_LEVEL.getType() == dsType) {
//			dataScope.setUsername(user.getUsername());
//		}
		return false;
	}

}
