package com.datasource.config;

import com.core.factory.YamlPropertySourceFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR>
 * @date 2024-04-06
 *
 * 注入SQL 格式化的插件
 */
@ConditionalOnClass(name = "com.data.mybatis.DruidSqlLogFilter")
@PropertySource(value = "classpath:dynamic-ds-log.yaml", factory = YamlPropertySourceFactory.class)
public class DynamicLogConfiguration {

}
