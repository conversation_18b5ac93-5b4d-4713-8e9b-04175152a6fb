package com.datasource.config;

import com.baomidou.dynamic.datasource.provider.AbstractJdbcDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.druid.DruidConfig;
import com.datasource.support.DataSourceConstants;
import com.datasource.util.DsConfTypeEnum;
import com.datasource.util.DsJdbcUrlEnum;
import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.StringEncryptor;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-06
 * <p>
 * 从数据源中获取 配置信息
 */
@Slf4j
public class JdbcDynamicDataSourceProvider extends AbstractJdbcDataSourceProvider {

    private final DruidDataSourceProperties properties;

    private final StringEncryptor stringEncryptor;

    public JdbcDynamicDataSourceProvider(StringEncryptor stringEncryptor, DruidDataSourceProperties properties) {
        super(properties.getDriverClassName(), properties.getUrl(), properties.getUsername(), properties.getPassword());
        this.stringEncryptor = stringEncryptor;
        this.properties = properties;
    }

    /**
     * 执行语句获得数据源参数
     *
     * @param statement 语句
     * @return 数据源参数
     * @throws SQLException sql异常
     */
    @Override
    protected Map<String, DataSourceProperty> executeStmt(Statement statement) throws SQLException {
        ResultSet rs = statement.executeQuery(properties.getQueryDsSql());
        log.info("=========================properties:【{}】",properties);
        log.info("=========================rs:【{}】",rs);

        Map<String, DataSourceProperty> map = new HashMap<>(8);
        while (rs.next()) {
            String name = rs.getString(DataSourceConstants.NAME);
            String username = rs.getString(DataSourceConstants.DS_USER_NAME);
            String password = rs.getString(DataSourceConstants.DS_USER_PWD);
            Integer confType = rs.getInt(DataSourceConstants.DS_CONFIG_TYPE);
            String dsType = rs.getString(DataSourceConstants.DS_TYPE);

            DataSourceProperty property = new DataSourceProperty();
            property.setUsername(username);
            try {
                property.setPassword(stringEncryptor.decrypt(password));
            } catch (Exception e) {
                property.setPassword(properties.getPassword());
            }
            String url;
            // JDBC 配置形式
            DsJdbcUrlEnum urlEnum = DsJdbcUrlEnum.get(dsType);
            if (DsConfTypeEnum.JDBC.getType().equals(confType)) {
                url = rs.getString(DataSourceConstants.DS_JDBC_URL);
            } else {
                String host = rs.getString(DataSourceConstants.DS_HOST);
                String port = rs.getString(DataSourceConstants.DS_PORT);
                String dsName = rs.getString(DataSourceConstants.DS_NAME);
                url = String.format(urlEnum.getUrl(), host, port, dsName);
            }

            // Druid Config
            DruidConfig druidConfig = new DruidConfig();
            druidConfig.setValidationQuery(urlEnum.getValidationQuery());
            property.setDruid(druidConfig);
            property.setUrl(url);

            map.put(name, property);
        }

        // 添加默认主数据源
        DataSourceProperty property = new DataSourceProperty();
        property.setUsername(properties.getUsername());
        property.setPassword(properties.getPassword());
        property.setUrl(properties.getUrl());
        map.put(DataSourceConstants.DS_MASTER, property);
        return map;
    }


}
