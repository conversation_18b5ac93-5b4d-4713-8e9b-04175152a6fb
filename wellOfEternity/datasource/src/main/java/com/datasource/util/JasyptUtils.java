package com.datasource.util;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

public class JasyptUtils {


    public static final String soft = "3RfusleU@Zp8qVCn";
    /**
     * 加密
     *
     * @param password 配置文件中设定的加密盐值
     * @param value    加密值
     * @return 加密后的字符串
     */
    public static String encrypt(String password, String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        encryptor.setConfig(encrypt(password));
        return encryptor.encrypt(value);
    }

    /**
     * 解密
     *
     * @param password 配置文件中设定的加密盐值
     * @param value    解密密文
     * @return 解密后的字符串
     */
    public static String decrypt(String password, String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        encryptor.setConfig(encrypt(password));
        return encryptor.decrypt(value);
    }

    /**
     * 配置,对应yml中的配置
     *
     * @param password 盐值
     * @return SimpleStringPBEConfig
     */
    public static SimpleStringPBEConfig encrypt(String password) {
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        //设置盐值
        config.setPassword(password);
        //设置算法
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        return config;
    }


    public static void main(String[] args) {
        // 加密 salt 盐 建议采用MD5加密
        String userNameEncryptStr = encrypt(soft, "root");
        String passwordEncryptStr = encrypt(soft, "Wmy@testMysql20230512");
        System.out.println("加密后:" + userNameEncryptStr);
        System.out.println("加密后:" + passwordEncryptStr);
        // 解密 salt 盐
        String userNameDecryptStr = decrypt(soft, userNameEncryptStr);
        String passwordDecryptStr = decrypt(soft, passwordEncryptStr);
        System.out.println("解密后:" + userNameDecryptStr);
        System.out.println("解密后:" + passwordDecryptStr);
    }
}