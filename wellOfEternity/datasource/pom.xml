<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>com.rb</groupId>
		<artifactId>wellOfEternity</artifactId>
		<version>1.0.0.RELEASE</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.rb</groupId>
	<artifactId>datasource</artifactId>

	<packaging>jar</packaging>

	<description>动态切换数据源</description>

	<dependencies>
		<dependency>
			<groupId>com.rb</groupId>
			<artifactId>core</artifactId>
		</dependency>
		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
		</dependency>
		<!-- druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<scope>provided</scope>
		</dependency>
		<!--拦截器依赖-->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
	</dependencies>
</project>
