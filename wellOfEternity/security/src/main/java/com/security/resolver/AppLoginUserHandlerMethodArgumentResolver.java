package com.security.resolver;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;

import com.core.exception.CheckedException;
import com.core.util.WebUtils;
import com.security.annotation.LoginUser;
import com.security.dto.LoginUserInfo;
import com.security.util.SecurityUtils;
import com.security.annotation.Inner ;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 有@LoginUser注解的方法参数，注入当前登录用户
 *
 * <AUTHOR> <PERSON>@gmail.com
 */

@Component
@Slf4j
public class AppLoginUserHandlerMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Resource
    @Lazy
    //private RemoteAppUserService remoteAppUserService;

    /**
     * header 头中的信息
     */
    public static final String clientType = "clientType";
    public static final String requestEnv = "requestEnv";
    public static final String requestDeviceType = "requestDeviceType";
    public static final String appVersion = "appVersion";

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(LoginUser.class);
    }

    /**
     * @return com.xmd.xiaomuding.common.security.dto.LoginUserInfo
     * <AUTHOR>
     * @Description //封装LoginUser 对象
     * @Date 14:39 2023-05-25
     * @Param [com.xmd.xiaomuding.common.security.dto.LoginUserInfo, org.springframework.web.context.request.NativeWebRequest, com.xmd.xiaomuding.common.security.service.XiaomudingUser]
     */
    @Override
    public Object resolveArgument (MethodParameter parameter, ModelAndViewContainer container, NativeWebRequest request, WebDataBinderFactory factory) {
        if (parameter.hasParameterAnnotation(Inner.class) || parameter.getMethod().isAnnotationPresent(Inner.class)) {
            log.error("==============URI:{} ========@LoginUser 不允许使用@Inner注解,抛出登录信息失效异常 检查此处去掉注解@Inner========================", WebUtils.getRequest().getRequestURI());
            return null;
        }
        //token转化User
        return Optional.ofNullable(SecurityUtils.getUser())
                //过滤空对象
                .filter(ObjectUtil::isNotEmpty)
                //初始化分装对象
                .map(x -> LoginUserInfo.init())
                //判断是够要查auth
                //.map(x -> parameter.getParameterAnnotation(LoginUser.class).needAuth() ? saveAuth(x, request, SecurityUtils.getUser()) : x)
                .map(x -> (saveAuth(x, request, SecurityUtils.getUser(), parameter.getParameterAnnotation(LoginUser.class).needAuth())))
                //保存用户设备信息
                .map(x -> pushDeviceInfo(x, request, SecurityUtils.getUser()))
                //未查到信息抛出异常
                .orElseThrow(() -> new CheckedException("你的登录信息已失效！请重新登录!"));
    }


    /**
     * @return com.xmd.xiaomuding.common.security.dto.LoginUserInfo
     * <AUTHOR>
     * @Description //查询认证信息
     * @Date 14:39 2023-05-25
     * @Param [com.xmd.xiaomuding.common.security.dto.LoginUserInfo, org.springframework.web.context.request.NativeWebRequest, com.xmd.xiaomuding.common.security.service.XiaomudingUser]
     */
    // 在登录流程中保存或更新用户认证信息
    public LoginUserInfo saveAuth(LoginUserInfo loginUserInfo, NativeWebRequest request, Object xmdUser, Boolean needAuth) {
       return null;
    }



    /**
     * @return com.xmd.xiaomuding.common.security.dto.LoginUserInfo
     * <AUTHOR>
     * @Description //保存用户设备信息
     * @Date 15:25 2023-07-18
     * @Param [com.xmd.xiaomuding.common.security.dto.LoginUserInfo, org.springframework.web.context.request.NativeWebRequest, com.xmd.xiaomuding.common.security.service.XiaomudingUser]
     */
    // 更新并推送用户的设备信息
    public LoginUserInfo pushDeviceInfo(LoginUserInfo loginUserInfo, NativeWebRequest request, Object xmdUser) {
        // 尝试保存用户的设备信息
        try {
            // 准备包含设备信息的映射
            Map<String, Object> userMap = new HashMap<>();
            userMap.put("userId", loginUserInfo.getId());
            userMap.put("loginPhone", loginUserInfo.getPhone());
            userMap.put("loginName", loginUserInfo.getUserName());
            // 从请求头中获取客户端类型、设备类型和应用版本信息
            userMap.put("loginClient", request.getHeader(clientType));
            userMap.put("deviceType", request.getHeader(requestDeviceType));
            userMap.put("appVersion", request.getHeader(appVersion));
        } catch (Exception e) {
            // 如果在推送过程中发生异常，记录错误日志
            log.error("保存用户设备信息失败，失败原因是：{}", e.getMessage());
        }
        // 记录调试信息，显示传递的用户ID和变化后的值
        log.debug("传递的用户ID是：{},变化后的值是:{}", JSON.toJSONString(xmdUser), JSON.toJSONString(loginUserInfo));
        // 返回更新后的LoginUserInfo对象
        return loginUserInfo;
    }

}
