package com.security.service.userdetails;


import cn.hutool.system.UserInfo;
import com.core.util.Result;
import com.security.service.XiaomudingUserDetailsService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * 用户详细信息 default
 *
 * <AUTHOR>
 */
@Slf4j
@Primary
@RequiredArgsConstructor
public class XiaomudingDefaultUserDetailsServiceImpl implements XiaomudingUserDetailsService {

	//private final RemoteUserService remoteUserService;

	private final CacheManager cacheManager;

	/**
	 * 用户密码登录
	 * @param username 用户名
	 * @return
	 * @throws UsernameNotFoundException
	 */
	@Override
	@SneakyThrows
	public UserDetails loadUserByUsername(String username) {
		//Result<UserInfo> result = remoteUserService.info(username, SecurityConstants.FROM_IN);
		return  null;
	}

	@Override
	public int getOrder() {
		return Integer.MIN_VALUE;
	}

}
