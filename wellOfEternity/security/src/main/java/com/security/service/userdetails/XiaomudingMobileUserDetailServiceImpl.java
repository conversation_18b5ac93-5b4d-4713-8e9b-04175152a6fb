package com.security.service.userdetails;

import cn.hutool.system.UserInfo;
import com.core.constant.SecurityConstants;
import com.core.util.Result;
import com.security.service.XiaomudingUser;
import com.security.service.XiaomudingUserDetailsService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class XiaomudingMobileUserDetailServiceImpl implements XiaomudingUserDetailsService {

    private final UserDetailsService xiaomudingDefaultUserDetailsServiceImpl;

   // private final RemoteUserService remoteUserService;

    @Override
    @SneakyThrows
    public UserDetails loadUserByUsername(String phone) {
        //Result<UserInfo> result = remoteUserService.social(phone, SecurityConstants.FROM_IN);
        return null;
    }

    @Override
    public UserDetails loadUserByUser(XiaomudingUser xiaomudingUser) {
        return xiaomudingDefaultUserDetailsServiceImpl.loadUserByUsername(xiaomudingUser.getUsername());
    }

    /**
     * 支持所有的 mobile 类型
     *
     * @param clientId  目标客户端
     * @param grantType 授权类型
     * @return true/false
     */
    @Override
    public boolean support(String clientId, String grantType) {
        return StringUtils.isEmpty(clientId) && SecurityConstants.GRANT_MOBILE.equals(grantType);
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE - 1;
    }
}
