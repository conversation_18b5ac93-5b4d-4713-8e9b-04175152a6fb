package com.security.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;

import com.core.enums.AccessClient;
import com.core.util.KeyStrResolver;
import com.core.util.WebUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationCode;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.core.constant.SecurityConstants.*;


/**
 * <AUTHOR>
 * @date 2022/5/27
 */
@RequiredArgsConstructor
@Slf4j
public class XiaomudingRedisOAuth2AuthorizationService implements OAuth2AuthorizationService {

    private final static Long TIMEOUT = 10L;

    private static final String AUTHORIZATION = "token";

    private final RedisTemplate<String, Object> redisTemplate;

    private final KeyStrResolver tenantKeyStrResolver;

    public static ThreadLocal<OAuth2Authorization> authorization_local = new InheritableThreadLocal();
    public static ThreadLocal<Boolean> is_authorization = new InheritableThreadLocal();

    @Override
    public void save(OAuth2Authorization authorization) {
        authorization_local.set(authorization);
        is_authorization.set(Boolean.TRUE);
        Assert.notNull(authorization, "authorization cannot be null");
        //state 认证
        if (isState(authorization)) {
            String token = authorization.getAttribute("state");
            redisTemplate.setValueSerializer(RedisSerializer.java());
            redisTemplate.opsForValue().set(buildKey(OAuth2ParameterNames.STATE, token), authorization, TIMEOUT,
                    TimeUnit.MINUTES);
        }
        //code 认证
        if (isCode(authorization)) {
            OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCode = authorization
                    .getToken(OAuth2AuthorizationCode.class);
            OAuth2AuthorizationCode authorizationCodeToken = authorizationCode.getToken();
            long between = ChronoUnit.MINUTES.between(authorizationCodeToken.getIssuedAt(),
                    authorizationCodeToken.getExpiresAt());
            redisTemplate.setValueSerializer(RedisSerializer.java());
            redisTemplate.opsForValue().set(buildKey(OAuth2ParameterNames.CODE, authorizationCodeToken.getTokenValue()),
                    authorization, between, TimeUnit.MINUTES);
        }
        //refreshToken 认证
        if (isRefreshToken(authorization)) {
            OAuth2RefreshToken refreshToken = authorization.getRefreshToken().getToken();
            long between = ChronoUnit.SECONDS.between(refreshToken.getIssuedAt(), refreshToken.getExpiresAt());
            redisTemplate.setValueSerializer(RedisSerializer.java());
            redisTemplate.opsForValue().set(buildKey(OAuth2ParameterNames.REFRESH_TOKEN, refreshToken.getTokenValue()),
                    authorization, between, TimeUnit.SECONDS);
        }
        //accessToken 认证
        if (isAccessToken(authorization)) {
            OAuth2AccessToken accessToken = authorization.getAccessToken().getToken();
            long between = ChronoUnit.SECONDS.between(accessToken.getIssuedAt(), accessToken.getExpiresAt());
            redisTemplate.setValueSerializer(RedisSerializer.java());
            redisTemplate.opsForValue().set(buildKey(OAuth2ParameterNames.ACCESS_TOKEN, accessToken.getTokenValue()),
                    authorization, between, TimeUnit.SECONDS);
        }
    }

    @Override
    public void remove(OAuth2Authorization authorization) {
        Assert.notNull(authorization, "authorization cannot be null");

        List<String> keys = new ArrayList<>();
        if (isState(authorization)) {
            String token = authorization.getAttribute("state");
            keys.add(buildKey(OAuth2ParameterNames.STATE, token));
        }

        if (isCode(authorization)) {
            OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCode = authorization
                    .getToken(OAuth2AuthorizationCode.class);
            OAuth2AuthorizationCode authorizationCodeToken = authorizationCode.getToken();
            keys.add(buildKey(OAuth2ParameterNames.CODE, authorizationCodeToken.getTokenValue()));
        }

        if (isRefreshToken(authorization)) {
            OAuth2RefreshToken refreshToken = authorization.getRefreshToken().getToken();
            keys.add(buildKey(OAuth2ParameterNames.REFRESH_TOKEN, refreshToken.getTokenValue()));
        }

        if (isAccessToken(authorization)) {
            OAuth2AccessToken accessToken = authorization.getAccessToken().getToken();
            keys.add(buildKey(OAuth2ParameterNames.ACCESS_TOKEN, accessToken.getTokenValue()));
        }
        //TODO 删除token
        redisTemplate.delete(keys);
    }

    @Override
    @Nullable
    public OAuth2Authorization findById(String id) {
        throw new UnsupportedOperationException();
    }

    @Override
    @Nullable
    public OAuth2Authorization findByToken(String token, @Nullable OAuth2TokenType tokenType) {
        Assert.hasText(token, "token cannot be empty");
        Assert.notNull(tokenType, "tokenType cannot be empty");
        redisTemplate.setValueSerializer(RedisSerializer.java());
        OAuth2Authorization authorization = (OAuth2Authorization) redisTemplate.opsForValue().get(buildKey(tokenType.getValue(), token));
        //TODO 修改源码 续约token
        //renewalToken(authorization);
        //TODO 修改源码 续约token
        return authorization;
    }


    /**
     * 续约token
     */
    public void renewalToken(OAuth2Authorization authorization) {
        if (Objects.isNull(authorization)) {
            log.info("token为空");
            return;
        }
        try {
            OAuth2AccessToken token = authorization.getAccessToken().getToken();
            redisTemplate.keys("app:1:*".concat(token.getTokenValue()).concat("*"))
                    .forEach(key -> {
                        //直接续约2年
                        redisTemplate.expire(key, 365*5, TimeUnit.DAYS);
                    });
            redisTemplate.keys("token:user:userdetail:app:*".concat(token.getTokenValue()))
                    .forEach(key -> {
                        //直接续约2年
                        redisTemplate.expire(key, 365*5, TimeUnit.DAYS);
                    });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("续约token失败。。。。{}", e.getMessage());
        }
    }


    private Boolean removeKey() {
        if (ObjectUtil.isNotEmpty(is_authorization.get()) && is_authorization.get()) {
            is_authorization.remove();
            //认证前提 需要删除token
            redisTemplate.keys(buildKey(ASTERISK, ASTERISK))
                    .stream()
                    .forEach(x -> redisTemplate.delete(x));
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 删除自定义的token对应关系
     * @Date 11:08 2023-05-11
     * @Param [clientId, userName]
     */
    private void removeCusTokenKey(String clientId, String userName, Boolean isRemove) {
        try {
            if (isRemove) {
                //获取key
                String keys = TOKEN_USER_KEY
                        .concat(clientId)
                        .concat(COLON)
                        .concat(userName)
                        .concat(ASTERISK);
                //删除所有key
                redisTemplate.keys(keys).forEach(redisTemplate::delete);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("删除key出错:{}", e.getMessage());
        }

    }

    /**
     * 生成key逻辑
     *
     * @param type
     * @param token
     * @return
     */
    private String buildKey(String type, String token) {
        //TODO 源码修改 客户端隔离 改造token生成逻辑 start----
        Boolean isRemove = removeKey();
        JSONObject params = findParams(type, token);
        if (ObjectUtil.isEmpty(params)) {
            return String.format("%s::%s::%s::%s", tenantKeyStrResolver.key(), null, AUTHORIZATION, type, token);
        }
        String clientId = params.containsKey(CLIENT_ID) ? params.get(CLIENT_ID).toString() : null;
        String userName = params.containsKey(DETAILS_USERNAME) ? params.get(DETAILS_USERNAME).toString() : null;
        removeCusTokenKey(clientId, userName, isRemove);
        // app的调用端访问.
        if (AccessClient.getClients().contains(clientId)) {
            return String.format("%s:%s:%s:%s:%s", clientId, tenantKeyStrResolver.key(), userName, type, token);
        }
        // pigx 原始的访问.·
        return String.format("%s::%s::%s::%s", tenantKeyStrResolver.key(), userName, AUTHORIZATION, type, token);
        //TODO 源码修改 客户端隔离 改造token生成逻辑 end----
    }


    /**
     * 从请求数据中获取参数
     *
     * @return
     */
    private JSONObject findParams(String type, String token) {
        final String[] finalClientId = {null};
        final String[] finalUserName = {null};
        HttpServletRequest request = WebUtils.getRequest();
        return Optional.of(request)
                .map(x -> new JSONObject())
                .map(x -> {
                    String clientId = WebUtils.extractClientId(request.getHeader(HttpHeaders.AUTHORIZATION))
                            .orElse(request.getHeader(CLIENT_ID));
                    String userName = ObjectUtil.isNotEmpty(authorization_local.get()) && ObjectUtil.isNotEmpty(authorization_local.get().getPrincipalName()) ?
                            authorization_local.get().getPrincipalName() : request.getHeader(DETAILS_USERNAME);
                    if (!StringUtils.isEmpty(clientId)) {
                        x.put(CLIENT_ID, clientId);
                    }
                    if (!StringUtils.isEmpty(userName)) {
                        x.put(DETAILS_USERNAME, userName);
                    }
                    finalClientId[0] = clientId;
                    finalUserName[0] = userName;
                    return x;
                })
                .filter(x -> x.containsKey(CLIENT_ID) || x.containsKey(DETAILS_USERNAME))
                .orElse(findParamsByToken(type, token, finalClientId[0], finalUserName[0]));
    }


    private static boolean isState(OAuth2Authorization authorization) {
        return Objects.nonNull(authorization.getAttribute("state"));
    }

    private static boolean isCode(OAuth2Authorization authorization) {
        OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCode = authorization
                .getToken(OAuth2AuthorizationCode.class);
        return Objects.nonNull(authorizationCode);
    }

    private static boolean isRefreshToken(OAuth2Authorization authorization) {
        return Objects.nonNull(authorization.getRefreshToken());
    }

    private static boolean isAccessToken(OAuth2Authorization authorization) {
        return Objects.nonNull(authorization.getAccessToken());
    }

    /**
     * 扩展方法根据 username 查询是否存在存储的
     *
     * @param authentication
     * @param clientId
     * @return
     */
    public void removeByUsername(Authentication authentication, String clientId) {
        //TODO 源码修改 客户端隔离 改造token生成逻辑 start----
        if (AccessClient.getClients().contains(clientId)) {
            // 根据 username查询对应access-token
            String authenticationName = authentication.getName();
            // 扩展记录 access-token 、username 的关系 1::token::username::admin::xxx
            String tokenUsernameKey = String.format("%s::%s::%s::%s::%s::*", clientId, tenantKeyStrResolver.key(), AUTHORIZATION,
                    DETAILS_USERNAME, authenticationName);
            Set<String> keys = redisTemplate.keys(tokenUsernameKey);
            if (CollUtil.isEmpty(keys)) {
                return;
            }
            List<Object> tokenList = redisTemplate.opsForValue().multiGet(keys);
            for (Object token : tokenList) {
                // 根据token 查询存储的 OAuth2Authorization
                OAuth2Authorization authorization = this.findByToken((String) token, OAuth2TokenType.ACCESS_TOKEN);
                // 根据 OAuth2Authorization 删除相关令牌
                this.remove(authorization);
            }
        } else {
            // 根据 username查询对应access-token
            String authenticationName = authentication.getName();
            // 扩展记录 access-token 、username 的关系 1::token::username::admin::xxx
            String tokenUsernameKey = String.format("%s::%s::%s::%s::*", tenantKeyStrResolver.key(), AUTHORIZATION,
                    DETAILS_USERNAME, authenticationName);
            Set<String> keys = redisTemplate.keys(tokenUsernameKey);
            if (CollUtil.isEmpty(keys)) {
                return;
            }
            List<Object> tokenList = redisTemplate.opsForValue().multiGet(keys);
            for (Object token : tokenList) {
                // 根据token 查询存储的 OAuth2Authorization
                OAuth2Authorization authorization = this.findByToken((String) token, OAuth2TokenType.ACCESS_TOKEN);
                // 根据 OAuth2Authorization 删除相关令牌
                this.remove(authorization);
            }
        }
        //TODO 源码修改 客户端隔离 改造token生成逻辑 start----
    }


    /**
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @Description 通过token获取参数
     * @Date 15:36 2023-05-09
     * @Param [token]
     */
    public JSONObject findParamsByToken(String type, String token, String clickId, String userName) {
        if (ObjectUtil.isEmpty(token) || token.contains(ASTERISK) || OAuth2ParameterNames.REFRESH_TOKEN.equals(type)) {
            return null;
        }
        //请求资源token认证过程中  clickId userName 是无法拿到的 所以的模糊拿到
        if (StringUtils.isEmpty(clickId) || StringUtils.isEmpty(userName)) {
            String key = redisTemplate.keys(TOKEN_USER_KEY.concat(ASTERISK).concat(token)).stream().findFirst().orElse(null);
            return null == key ? null : (JSONObject) redisTemplate.opsForValue().get(key);
        }
        return (JSONObject) redisTemplate.opsForValue().get(TOKEN_USER_KEY
                .concat(clickId)
                .concat(COLON)
                .concat(userName)
                .concat(COLON)
                .concat(token));
    }


}
