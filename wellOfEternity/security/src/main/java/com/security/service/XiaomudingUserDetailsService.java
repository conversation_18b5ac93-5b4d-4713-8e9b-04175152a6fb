package com.security.service;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.system.UserInfo;
import com.core.constant.SecurityConstants;
import org.springframework.core.Ordered;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/8/15
 */
public interface XiaomudingUserDetailsService extends UserDetailsService, Ordered {

    /**
     * 是否支持此客户端校验
     *
     * @param clientId  请求客户端
     * @param grantType 授权类型
     * @return true/false
     */
    default boolean support(String clientId, String grantType) {
        return true;
    }

    /**
     * 排序值 默认取最大的
     *
     * @return 排序值
     */@Override
    default int getOrder() {
        return 0;
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 业务校验 如果有子类需要校验则实现改方法
     * @Date 9:49 2023-05-11
     * @Param []
     */
    default void checkedBusiness() {
        //do nothing
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 前置处理器
     * @Date 17:39 2023-05-23
     * @Param []
     */
    default Boolean doBeforeHandler() {
        //前置参数校验
        checkedBusiness();
        //是否需要校验密码
        return needCheckedPassWord();
    }

    /**
     * 构建userdetails
     *
     * @param result 用户信息
     * @return UserDetails
     * @throws UsernameNotFoundException
     */
//    default UserDetails getUserDetails(R<UserInfo> result) {
//        // @formatter:off
//        return RetOps.of(result)
//                .getData()
//                .map(this::convertUserDetails)
//                .orElseThrow(() -> new UsernameNotFoundException("用户不存在"));
//        // @formatter:on
//    }

    /**
     * UserInfo 转 UserDetails
     *
     * @param info
     * @return 返回UserDetails对象
     */
    default UserDetails convertUserDetails(UserInfo info) {
        Set<String> dbAuthsSet = new HashSet<>();
      //  if (ArrayUtil.isNotEmpty(info.getRoles())) {
//            // 获取角色
//            Arrays.stream(info.getRoles()).forEach(roleId -> dbAuthsSet.add(SecurityConstants.ROLE + roleId));
//            // 获取资源
//            dbAuthsSet.addAll(Arrays.asList(info.getPermissions()));

      //  }
        Collection<? extends GrantedAuthority> authorities = AuthorityUtils
                .createAuthorityList(dbAuthsSet.toArray(new String[0]));
       // SysUser user = info.getSysUser();
        // 构造security用户
//
//        return new XiaomudingUser(user.getUserId(), user.getUsername(), user.getDeptId(), user.getPhone(), user.getAvatar(),
//                user.getNickname(), user.getName(), user.getEmail(), user.getTenantId(),
//                SecurityConstants.BCRYPT + user.getPassword(), true, true, UserTypeEnum.TOB.getStatus(), true,
//                !CommonConstants.STATUS_LOCK.equals(user.getLockFlag()), authorities);
        return null;
    }

    /**
     * 通过用户实体查询
     *
     * @param xiaomudingUser user
     * @return
     */
    default UserDetails loadUserByUser(XiaomudingUser xiaomudingUser) {
        return this.loadUserByUsername(xiaomudingUser.getUsername());
    }


    /**
     * @return boolean
     * <AUTHOR>
     * @Description 默认方法 需要校验密码  默认为true 如果不需要校验 子类修改
     * @Date 10:37 2023-05-11
     * @Param []
     */
    default Boolean needCheckedPassWord() {
        //默认需要校验
        return Boolean.TRUE;
    }
}
