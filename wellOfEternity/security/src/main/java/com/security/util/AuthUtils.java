package com.security.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

import com.core.util.WebUtils;
import com.security.mapper.OutsideApiMapper;
import com.security.service.XiaomudingUserDetailsService;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.web.authentication.www.BasicAuthenticationConverter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;

import static com.core.constant.SecurityConstants.*;


/**
 * <AUTHOR>
 * @date 2018/5/13 认证授权相关工具类
 */
@Slf4j
@UtilityClass
public class AuthUtils {

    public static ThreadLocal<String> CLIENT_ID = new InheritableThreadLocal<>();
    public static ThreadLocal<String> GRANT_TYPE = new InheritableThreadLocal<>();
    public static ThreadLocal<XiaomudingUserDetailsService> USER_DETAILS_SERVICE = new InheritableThreadLocal<>();

    private final String BASIC_ = "Basic ";

    /**
     * 从header 请求中的clientId/clientsecect
     *
     * @param header header中的参数
     * @throws RuntimeException if the Basic header is not present or is not valid Base64
     */
    @SneakyThrows
    public String[] extractAndDecodeHeader(String header) {

        byte[] base64Token = header.substring(6).getBytes("UTF-8");
        byte[] decoded;
        try {
            decoded = Base64.decode(base64Token);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Failed to decode basic authentication token");
        }

        String token = new String(decoded, StandardCharsets.UTF_8);

        int delim = token.indexOf(":");

        if (delim == -1) {
            throw new RuntimeException("Invalid basic authentication token");
        }
        return new String[]{token.substring(0, delim), token.substring(delim + 1)};
    }

    /**
     * *从header 请求中的clientId/clientsecect
     *
     * @param request
     * @return
     */
    @SneakyThrows
    public String[] extractAndDecodeHeader(HttpServletRequest request) {
        String header = request.getHeader(HttpHeaders.AUTHORIZATION);

        if (header == null || !header.startsWith(BASIC_)) {
            throw new RuntimeException("请求头中client信息为空");
        }

        return extractAndDecodeHeader(header);
    }


    public void saveParams() {
        String clientId = WebUtils.getRequest().getParameter(OAuth2ParameterNames.CLIENT_ID);
        if (StrUtil.isBlank(clientId)) {
            clientId = new BasicAuthenticationConverter().convert(WebUtils.getRequest()).getName();
        }
        CLIENT_ID.set(clientId);
        GRANT_TYPE.set(WebUtils.getRequest().getParameter(OAuth2ParameterNames.GRANT_TYPE));
        XiaomudingUserDetailsService xiaomudingUserDetailsService = SpringUtil.getBeansOfType(XiaomudingUserDetailsService.class)
                .values()
                .stream()
                .sorted((o1, o2) -> o1.getOrder() - o2.getOrder())
                .filter(service -> service.support(CLIENT_ID.get(), GRANT_TYPE.get()))
                .max(Comparator.comparingInt(Ordered::getOrder))
                .orElseThrow(() -> new InternalAuthenticationServiceException("UserDetailsService error , not register"));
        USER_DETAILS_SERVICE.set(xiaomudingUserDetailsService);

    }

    private RedisTemplate<String, String> strRedisTemplate = SpringUtil.getBean(RedisTemplate.class);

    public static void removeToken(String userPhone, Long userId) {
        if (StringUtils.isEmpty(userPhone)) {
            userPhone = SpringUtil.getBean(OutsideApiMapper.class).findUserNameById(userId);
        }
        //删除key
        strRedisTemplate.keys(ASTERISK
                        .concat(COLON)
                        .concat(ONE_STR)
                        .concat(COLON)
                        .concat(userPhone)
                        .concat(ASTERISK))
                .forEach(key -> {
                    System.out.println("==============" + key);
                    strRedisTemplate.delete(key);
                });
    }

}
