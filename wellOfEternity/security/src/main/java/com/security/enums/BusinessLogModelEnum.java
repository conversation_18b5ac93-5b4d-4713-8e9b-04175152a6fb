package com.security.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024-01-11 14:33
 * @PackageName:com.xmd.xiaomuding.customize.core.enmus.request
 * @ClassName: BusinessLogModelEnum
 * @Description: TODO
 * @Version 1.0
 */
@Getter
public enum BusinessLogModelEnum {
    MATERIAL("物料管理"),
    DISTINGUISH("区管理"),
    SHED("棚管理"),
    COLUMN("栏管理"),
    USER("人员管理"),
    ROLE("角色管理"),
    DEVICE("设备管理"),
    NODE("操作日志记录管理"),
    SYSTEM("系统模块"),
    AUTHORITY("权限模块"),
    LIVESTOCK("畜只管理"),
    ;
    private String description;

    BusinessLogModelEnum(String description) {
        this.description = description;
    }
}
