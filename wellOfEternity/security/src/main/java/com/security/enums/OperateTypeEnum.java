package com.security.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024-01-11 14:33
 * @PackageName:com.xmd.xiaomuding.customize.core.enmus.request
 * @ClassName: BusinessLogModelEnum
 * @Description: TODO
 * @Version 1.0
 */
@Getter
public enum OperateTypeEnum {
    /**
     * 未实名认证
     */
    INSERT("增加"),
    UPDATE("更新"),
    INSERT_OR_UPDATE("增加/修改"),
    DELETE("删除"),
    SELECT("查询"),
    OTHER("其他");
    private String description;

    OperateTypeEnum(String description) {
        this.description = description;
    }
}
