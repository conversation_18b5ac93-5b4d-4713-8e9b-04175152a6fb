package com.security.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.security.aspect.OutsideApiEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @ClassName: OutsideApiMapper
 * @Description: TODO
 * @date 2023-04-23 15:37
 */
@Mapper
public interface OutsideApiMapper extends BaseMapper<OutsideApiEntity> {


    @Select("SELECT user_account from app_user_auth_info where id = #{uId} and deleted = 0")
    String findUserNameById(@Param("uId") Long uId);

    @Select("SELECT count(1) FROM m_user_application where application_id = #{applicationId} and user_id = #{userId})")
    int findUserApplication(@Param("applicationId") Long applicationId, @Param("userId") Long userId);

}
