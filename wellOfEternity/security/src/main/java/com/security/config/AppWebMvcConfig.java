package com.security.config;



import com.security.resolver.AppLoginUserHandlerMethodArgumentResolver;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.List;

/**
 * MVC配置
 *
 * @<NAME_EMAIL>
 */
@Configuration
public class AppWebMvcConfig implements WebMvcConfigurer {

	@Resource
	private AppLoginUserHandlerMethodArgumentResolver loginUserHandlerMethodArgumentResolver;


	@Override
	public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
		argumentResolvers.add(loginUserHandlerMethodArgumentResolver);
	}
	/**
	 * 替换fastjson，解决精度丢失问题
	 */
	@Override
	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {

	}
}
