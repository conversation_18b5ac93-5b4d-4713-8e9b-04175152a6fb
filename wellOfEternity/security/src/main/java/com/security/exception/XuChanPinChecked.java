package com.security.exception;

import com.core.exception.CheckedException;
import com.security.exception.ProjectCheckedHandler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-09-21 14:18
 * @PackageName:com.xmd.xiaomuding.auth.support.exception
 * @ClassName: XuChanpinCheked
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
public class Xu<PERSON>hanPinChecked implements ProjectCheckedHandler {
    @Override
    public void checked(Object entity) {
        if (findUserApplication(13L, 1L) == 0) {
            throw new CheckedException("您无权限登录");
        }
    }
}
