package com.security.exception;

import cn.hutool.extra.spring.SpringUtil;
import com.security.mapper.OutsideApiMapper;


/**
 * <AUTHOR>
 * @Date 2023-09-21 14:17
 * @PackageName:com.xmd.xiaomuding.auth.support.exception
 * @ClassName: ProjectChekedHandler
 * @Description: TODO
 * @Version 1.0
 */
public interface ProjectCheckedHandler {

    OutsideApiMapper outsideApiMapper = SpringUtil.getBean(OutsideApiMapper.class);

    void checked(Object entity);


    default int findUserApplication(Long applicationId, Long userId) {
        return outsideApiMapper.findUserApplication(applicationId, userId);
    }


}
