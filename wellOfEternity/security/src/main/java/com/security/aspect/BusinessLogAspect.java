package com.security.aspect;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;

import com.core.util.WebUtils;
import com.security.annotation.BusinessLog;
import com.security.dto.BusinessLogEntity;
import com.security.service.XiaomudingUser;
import com.security.util.IpUtils;
import com.security.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: MethodFindAspect
 * @Description: 异常方法属性文案捕获切面
 * @date 2023-04-19 1:34
 * 测试接口 com.xmd.xiaomuding.common.core.controller.CustomizedCoreTestController#ExceptionThrowTest()
 */
@Aspect
@Service
@Slf4j
public class BusinessLogAspect {

    private final String farmId = "farmId";
    private final String applicationId = "applicationId";


    @Resource
    private Object businessLogMapper;


    @Value("${spring.application.name}: ")
    private String applicationName;

    /**
     * @return void
     * <AUTHOR>
     * @Description 切入点 带有BusinessLog方法的控制层
     * @Date 9:00 2023-04-19
     * @Param []
     */
    @Pointcut("@annotation(businessLog)")
    public void controller(BusinessLog businessLog) {

    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 切入逻辑 带有BusinessLog方法的控制层
     * @Date 9:00 2023-04-19
     * @Param []
     */
    @SneakyThrows
    @Around("@annotation(businessLog)")
    // 环绕通知，用于记录业务日志
    public Object around(ProceedingJoinPoint point, BusinessLog businessLog) {
        // 记录方法开始执行的时间
        Long startTime = System.currentTimeMillis();
        // 根据切点和注解信息，初始化业务日志实体
        BusinessLogEntity businessLogEntity = initBusinessLog(point, businessLog);
        Object obj;
        try {
            // 执行目标方法
            obj = point.proceed();
            // 如果方法执行成功，设置状态为“成功”
            businessLogEntity.setStatus("成功");
        } catch (Exception e) {
            // 如果执行过程中抛出异常，设置状态为“失败”并记录异常信息
            businessLogEntity.setStatus("失败");
            businessLogEntity.setExceptionMessage(e.getMessage());
            // 继续抛出异常，不影响原有的异常处理流程
            throw e;
        } finally {
            try {
                // 检查是否设置了应用的白名单，或者当前应用ID是否在白名单中
                if (businessLog.applicationList().length < 1 ||
                        Arrays.stream(businessLog.applicationList()).anyMatch(x -> x == businessLogEntity.getApplicationId())) {
                    // 如果未设置应用白名单，或当前应用ID符合白名单条件，则记录日志
                    // 计算方法执行时间并设置
                    businessLogEntity.setTime(System.currentTimeMillis() - startTime);
                    // 将日志实体保存到数据库或其他存储介质中
                    insertLog(businessLogEntity);
                }
            } catch (Exception e) {
                // 如果日志记录过程中出现异常，打印异常栈信息（这里应该避免影响主业务流程）
                e.printStackTrace();
            }
        }
        // 返回目标方法的执行结果
        return obj;
    }



    /**
     * 构建日志实体
     */
    public BusinessLogEntity initBusinessLog(ProceedingJoinPoint point, BusinessLog businessLog) {
        BusinessLogEntity entity = new BusinessLogEntity();
        try {
            //服务名称
            entity.setApplicationName(applicationName);
            //所属模块
           // entity.setModel(businessLog.model().getDescription());
            //操作类型
           // entity.setOperateType(businessLog.operateType().getDescription());
            HttpServletRequest request = WebUtils.getRequest();
            //请求体
            JSONObject requestBody = getRequestBody();
            entity.setRequestBody(JSONObject.toJSONString(requestBody));
            //养殖场id
            entity.setFarmId(StringUtils.isBlank(requestBody.getString(farmId)) ? null : Long.valueOf(requestBody.getString(farmId)));
            //应用id
            entity.setApplicationId(StringUtils.isBlank(requestBody.getString(applicationId)) ? null : Integer.valueOf(requestBody.getString(applicationId)));
            //请求方式
            entity.setRequestType(request.getMethod());
            //请求路径
            entity.setRequestUrl(request.getRequestURL().toString());

            //请求参数
            entity.setRequestParam(HttpUtil.toParams(request.getParameterMap()));
            //请求头
            entity.setRequestHeader(JSONObject.toJSONString(header()));
            //请求ip
            entity.setRequestIp(IpUtils.getIpAddr(request));
            //所属模块
            XiaomudingUser user = SecurityUtils.getUser();
            //用户账号
            entity.setUserAccount(user.getPhone());
            //创建人名称
            entity.setCreateUserName(user.getUsername());
            //创建人id
            entity.setCreateUserId(String.valueOf(user.getId()));
            //接口备注
            entity.setNotes(businessLog.notes());
            //所属模块
            Method method = ((MethodSignature) point.getSignature()).getMethod();
            if (method.isAnnotationPresent(Operation.class)) {
                //接口描述
                entity.setDescribe(method.getAnnotation(Operation.class).description());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return entity;
    }


    /**
     * 获取请求体
     */
    // 从HttpServletRequest中读取请求体，并将其解析为JSONObject
    public JSONObject getRequestBody() {
        // 使用StringBuilder收集请求体内容
        StringBuilder requestBody = new StringBuilder();
        try (BufferedReader reader = WebUtils.getRequest().getReader()) {
            // 获取请求的BufferedReader用于读取请求体内容
            String line;
            // 循环读取每一行内容，直到读取完毕
            while ((line = reader.readLine()) != null) {
                // 将读取到的内容追加到StringBuilder中，每行之间插入换行符
                requestBody.append(line).append('\n');
            }
        } catch (IOException e) {
            // 如果读取过程中发生IOException，可以在这里进行异常处理
            // 例如，记录日志、抛出自定义异常等
        }
        // 将收集到的请求体内容转换为字符串，然后解析为JSONObject对象并返回
        // 注意：这里假设请求体内容是有效的JSON格式，如果不是，此处可能抛出解析异常
        return JSONObject.parseObject(requestBody.toString());
    }


    /**
     * 设置header
     */
    // Retrieves all headers from the current HTTP request and returns them as a map.
    // 从当前HTTP请求中检索所有请求头，并将它们作为映射返回。
    public Map<String, String> header() {
        // 获取当前HTTP请求对象。
        HttpServletRequest request = WebUtils.getRequest();
        // 初始化一个映射，用于保存请求头名称及其值。
        Map<String, String> headers = new HashMap<>();
        // 获取请求中所有请求头名称的枚举。
        Enumeration<String> headerNames = request.getHeaderNames();
        // 遍历所有请求头名称。
        while (headerNames.hasMoreElements()) {
            // 获取下一个请求头名称。
            String h = headerNames.nextElement();
            // 获取当前请求头名称的值，并将其放入映射中。
            headers.put(h, request.getHeader(h));
        }
        // 返回包含所有请求头名称及其值的映射。
        return headers;
    }




    /**
     * 开启一段新的事物保存日志 防止业务报错回滚
     */
    @Async
    public void insertLog(BusinessLogEntity businessLogEntity) {
        log.info("开始保存系统日志:{}", businessLogEntity);
      //  businessLogMapper.insert(businessLogEntity);
    }

}
