package com.security.aspect;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.core.exception.CheckedException;
import com.core.util.WebUtils;
import com.security.mapper.OutsideApiMapper;
import com.security.util.IpUtils;
import com.security.util.SecurityUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @ClassName: OutsideApiAspect
 * @Description: TODO
 * @date 2023-04-23 14:46
 */
@Aspect
@Component
@Slf4j
public class OutsideApiAspect {


    private static ThreadLocal<OutsideApiEntity> localOutSide = new InheritableThreadLocal<>();


    public static OutsideApiEntity get() {
        return localOutSide.get();
    }


    @Resource
    private OutsideApiMapper outsideApiMapper;

    /**
     * 定义切点
     */
    @Pointcut("@annotation(com.security.annotation.OutsideApi)")
    public void outsideApi() {
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 三方校验
     * @Date 14:48 2023-04-23
     * @Param [joinPoint]
     */
    @SneakyThrows
    @Around("outsideApi()")
    // 环绕通知，用于拦截方法调用并进行第三方接口访问控制和权限验证
    public Object around(ProceedingJoinPoint point) {
        try {
            // 从数据库中查询当前用户关联的外部API认证信息
            OutsideApiEntity outsideApiEntity = outsideApiMapper.selectById(SecurityUtils.getUser().getId());
            log.info("查询到的认证实体:【{}】", outsideApiEntity);
            // 如果没有找到认证信息，尝试通过手机号查找
            if (ObjectUtil.isEmpty(outsideApiEntity)) {
                outsideApiEntity = outsideApiMapper.selectOne(new LambdaQueryWrapper<OutsideApiEntity>()
                        //.eq(OutsideApiEntity::getDeleted, 0) // 筛选未删除的记录
                        .eq(OutsideApiEntity::getPhone, SecurityUtils.getUser().getPhone()).last(" LIMIT 1")); // 根据手机号筛选
            }

            // 如果仍然没有找到认证信息，抛出异常
            if (ObjectUtil.isEmpty(outsideApiEntity)) {
                throw new CheckedException("Your token has expired");
            }

            // 验证有效访问次数，如果次数小于1，则抛出异常
            if (outsideApiEntity.getVisitsCount() < 1) {
                throw new CheckedException("Your visits have reached the maximum limit");
            }

            // 验证接口请求权限，如果没有配置接口列表或请求的接口不在列表中，则抛出异常
            if (StringUtils.isEmpty(outsideApiEntity.getInterfaceList())) {
                throw new CheckedException("You do not have permission to access this interface");
            }

            Arrays.stream(outsideApiEntity.getInterfaceList().split(","))
                    .map(String::trim)
                    .filter(x -> x.startsWith("#") ? Pattern.compile(x.replace("#", "")).matcher(WebUtils.getRequest().getRequestURI().trim()).find() :
                            x.equals(WebUtils.getRequest().getRequestURI().trim()))
                    .findFirst()
                    .orElseThrow(() -> new CheckedException("You do not have permission to access this interface"));

            // 验证授权访问终止时间，如果当前时间晚于终止时间，则抛出异常
            if (LocalDateTime.now().isAfter(outsideApiEntity.getDeadLineTime())) {
                throw new CheckedException("Your verification time has expired. Please contact the administrator");
            }

            // 如果配置了IP列表，则验证请求来源IP是否在白名单中
            if (ObjectUtil.isNotEmpty(outsideApiEntity.getIpList()) &&
                    !JSONArray.parseArray(outsideApiEntity.getIpList(), String.class).contains(IpUtils.getIpAddr(WebUtils.getRequest()))) {
                throw new CheckedException("You do not have permission to access this interface");
            }

            // 在ThreadLocal中存储认证信息，以便业务逻辑中使用
            localOutSide.set(outsideApiEntity);

            // 继续执行被拦截的方法
            Object result = point.proceed();

            // 更新认证信息的数据，例如减少访问次数
            doUpdateDate(outsideApiEntity);

            return result;
        } catch (Exception e) {
            log.error("=======三方校验错误========{}", e.getMessage(), e);
            // 抛出异常，终止请求处理
            throw e;
        } finally {
            // 释放ThreadLocal资源，避免内存泄露
            localOutSide.remove();
        }
    }



    /**
     * 更新数据访问次数
     */
    // 更新外部API实体的访问次数
    public void doUpdateDate(OutsideApiEntity outsideApiEntity) {
        try {
            // 将外部API实体的访问次数减一
            // 这是在成功处理一个外部请求后进行的操作，以确保访问次数的正确计数
            outsideApiEntity.setVisitsCount(outsideApiEntity.getVisitsCount() - 1);
            // 使用外部API映射器（mapper）更新数据库中的实体
            // 这将持久化访问次数的变化
            outsideApiMapper.updateById(outsideApiEntity);
        } catch (Exception e) {
            // 如果更新过程中发生异常，记录错误日志
            // 这有助于调试和追踪可能的数据更新问题
            log.error("更新数据访问次数出错:{}", e.getMessage());
        }
    }

}
