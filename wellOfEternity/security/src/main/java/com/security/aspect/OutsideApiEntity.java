package com.security.aspect;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName: OutsideApiEntity
 * @Description: TODO
 * @date 2023-04-23 15:41
 */
@Data
@TableName("t_outside_api_authentication")
public class OutsideApiEntity  {

    /**
     * 主体、公司名称
     */
    private String companyName;
    /**
     * apiKey
     */
    private String apiKey;
    /**
     * secretKey
     */
    private String secretKey;
    /**
     * app_id
     */
    private String appId;
    /**
     * 授权接口列表
     */
    private String interfaceList;
    /**
     * 单词访问有效期时间-秒
     */
    private Long expireTime;
    /**
     * 有效访问次数
     */
    private Integer visitsCount;
    /**
     * 授权访问终止时间
     */
    private LocalDateTime deadLineTime;
    /**
     * 可访问的ip列表
     */
    private String ipList;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
}
