package com.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> admin
 * @date : 2023/6/9
 * @description :
 */
@Data
public class PushDeviceInfoDto implements Serializable {
	/**
	 * 主键id
	 */
	@Schema(description="主键id")
	private Long loginId;
	
	/**
	 * 用户的user_id
	 */
	@Schema(description="用户的user_id")
	private Long userId;
	
	/**
	 * 登录手机号
	 */
	@Schema(description="登录手机号")
	private String loginPhone;
	
	/**
	 * 登录用户名
	 */
	@Schema(description="登录用户名")
	private String loginName;
	
	/**
	 * 登录客户端:android,ios,pc,h5
	 */
	@Schema(description="登录客户端:android,ios,pc,h5")
	private String loginClient;
	
	/**
	 * 设备类型:xiaomi,oppo,huawei
	 */
	@Schema(description="设备类型:xiaomi,oppo,huawei")
	private String deviceType;
	
	/**
	 * APP当前版本
	 */
	@Schema(description="APP当前版本")
	private String appVersion;
}
