package com.security.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统业务日志表
 *
 * <AUTHOR>
 * @date 2024-01-11 17:07:10
 */
@Data
@TableName("sys_business_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "系统业务日志表")
public class BusinessLogEntity extends Model<BusinessLogEntity> {

    private static final long serialVersionUID = 1L;


	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 所属模块
	*/
    @Schema(description="所属模块")
    private String model;

	/**
	* 操作类型
	*/
    @Schema(description="操作类型")
    private String operateType;

	/**
	* 请求方式
	*/
    @Schema(description="请求方式")
    private String requestType;

	/**
	* 请求路径
	*/
    @Schema(description="请求路径")
    private String requestUrl;

	/**
	* 请求参数
	*/
    @Schema(description="请求参数")
    private String requestParam;

	/**
	* 请求体
	*/
    @Schema(description="请求体")
    private String requestBody;

	/**
	* 请求头
	*/
    @Schema(description="请求头")
    private String requestHeader;

	/**
	* 请求ip
	*/
    @Schema(description="请求ip")
    private String requestIp;

	/**
	* 用户账号
	*/
    @Schema(description="用户账号")
    private String userAccount;

	/**
	* 请求状态
	*/
	@TableField(value = "`status`")
    @Schema(description="请求状态")
    private String status;

	/**
	* 接口描述
	*/
	@TableField(value = "`describe`")
    @Schema(description="接口描述")
    private String describe;

	/**
	* 服务名称
	*/
    @Schema(description="服务名称")
    private String applicationName;

	/**
	* 接口备注
	*/
    @Schema(description="接口备注")
    private String notes;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remake;

	/**
	* 创建人名称
	*/
    @Schema(description="创建人名称")
    private String createUserName;

	/**
	* 创建人id
	*/
    @Schema(description="创建人id")
    private String createUserId;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 更新人
	*/
    @Schema(description="更新人")
    private String updateUserName;

	/**
	* 更新人id
	*/
    @Schema(description="更新人id")
    private String updateUserId;

	/**
	* 删除状态
	*/
    @TableLogic
    @Schema(description="删除状态")
    private Integer delFlag;

	/**
	* 异常信息
	*/
    @Schema(description="异常信息")
    private String exceptionMessage;

	/**
	* 请求时长
	*/
    @Schema(description="请求时长")
    private Long time;

	/**
	* 用户认证养殖场id
	*/
    @Schema(description="用户认证养殖场id")
    private Long farmId;

	/**
	* 应用Id
	*/
    @Schema(description="应用Id")
    private Integer applicationId;

}