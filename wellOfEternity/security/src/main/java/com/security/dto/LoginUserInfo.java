package com.security.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.SpringSecurityCoreVersion;
import org.springframework.web.context.request.NativeWebRequest;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: LoginUser
 * @Description: 客户端登录用户对象 @LoginUser 封装后的对象
 * @date 2023-06-06 10:58
 */

@Getter
@Setter
@ToString
public class LoginUserInfo implements Serializable {

    private static final long serialVersionUID = SpringSecurityCoreVersion.SERIAL_VERSION_UID;

    //TODO =======================================security User============================
    /**
     * @param auth 属性
     */
    private Collection<GrantedAuthority> authorities;

    /**
     * @param credentialsNonExpired set to <code>true</code> if the credentials have not
     */
    private boolean accountNonExpired;
    /**
     * @param accountNonLocked set to <code>true</code> if the account is not locked
     */
    private boolean accountNonLocked;
    /**
     * @param credentialsNonExpired set to <code>true</code> if the credentials have not
     */
    private boolean credentialsNonExpired;
    /**
     * @param enabled set to <code>true</code> if the user is enabled
     */
    private boolean enabled;
    //TODO =======================================app User============================
    /**
     * 扩展属性，方便存放oauth 上下文相关信息
     */
    @Schema(description = "登录账号:手机号")
    private Map<String, Object> attributes;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long id;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    /**
     * 拓展字段:姓名
     */
    @Schema(description = "拓展字段:姓名")
    private String name;

    /**
     * 拓展字段:邮箱
     */
    @Schema(description = "拓展字段:邮箱")
    private String email;
    /**
     * 用户类型
     */
    @Schema(description = "用户类型")
    private String userType;

    /**
     * 登录账号:手机号
     */
    @Schema(description = "登录账号:手机号")
    private String userAccount;
    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    private String userName;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;
    //TODO =======================================authInfo============================

    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    private String userPwd;

    /**
     * 居住地址,app能获取并且上送就存储这个
     */
    @Schema(description = "居住地址,app能获取并且上送就存储这个")
    private String address;


    /**
     * 头像url
     */
    @Schema(description = "头像url")
    private String avatarUrl;

    /**
     * 用户状态(1 正常  2 异常)
     */
    @Schema(description = "用户状态(1 正常  2 异常)")
    private Integer userStatus;

    /**
     * 用户注册客户端类型(android,ios,pc,web)
     */
    @Schema(description = "用户注册客户端类型(android,ios,pc,web)")
    private String registerClient;

    /**
     * 萤石的子账号
     */
    @Schema(description = "萤石的子账号")
    private String ysUserId;

    /**
     * 手机运营商
     */
    @Schema(description = "手机运营商")
    private String phoneOperator;

    /**
     * 区域id
     */
    @Schema(description = "区域id")
    private long pcareasId;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    private String identityCard;

    /**
     * 身份证正面照片
     */
    @Schema(description = "身份证正面照片")
    private String identityPositiveUrl;

    /**
     * 身份证反面照片
     */
    @Schema(description = "身份证反面照片")
    private String identityAntiUrl;

    /**
     * 身份证名称
     */
    @Schema(description = "身份证名称")
    private String identityName;

    /**
     * 民族
     */
    @Schema(description = "民族")
    private String userEthnicity;

    /**
     * 性别(0-未填写,1-男,2-女)
     */
    @Schema(description = "性别(0-未填写,1-男,2-女)")
    private Integer userGender;

    /**
     * 出生年月
     */
    @Schema(description = "出生年月")
    private String birthDate;

    /**
     * 户籍地址
     */
    @Schema(description = "户籍地址")
    private String residenceAddress;

    /**
     * 有效期
     */
    @Schema(description = "有效期")
    private String certificateOfDate;

    /**
     * 实名认证状态(0：未实名认证  1：实名认证 2:审核中 3:未通过)
     */
    @Schema(description = "实名认证状态(0：未实名认证  1：实名认证 2:审核中 3:未通过)")
    private Integer authStatus;

    /**
     * 认证类别(1、贩运人 2、经纪人 3、推荐人 4、交易市场 5、防疫员 6、政府用户 7、农技专家 8、金融机构 9、农资商家 10、养殖场 11、屠宰场、12、见犊补母)
     */
    @Schema(description = "认证类别(1、贩运人 2、经纪人 3、推荐人 4、交易市场 5、防疫员 6、政府用户 7、农技专家 8、金融机构 9、农资商家 10、养殖场 11、屠宰场、12、见犊补母)")
    private String authInfo;
    /**
     * 认证行政区域级别
     */
    @Schema(description = "认证行政区域级别(存值为:‘全国区域字典表d_provinces_city_areas主键id")
    private String authProvincesCityAreas;

    /**
     * 认证行政区域级别的上一级
     */
    @Schema(description = "认证行政区域级别的上一级")
    String authProvincesCityAreasUpperLevel;
    /**
     * 操作角色类型(9：防疫员 8:操作员 4:管理员)
     */
    @Schema(description = "操作角色类型(9：防疫员 8:操作员 4:管理员)")
    String optAuthType;

    /**
     * 逻辑删除标记(0:未删除，1:已删除)
     */
    @Schema(description = "逻辑删除标记(0:未删除，1:已删除)")
    private Integer deleted;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改者
     */
    @Schema(description = "修改者")
    private String updateUser;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 客户端类型(clientType): android,ios,pc
     */
    @Schema(description = "客户端类型(clientType): android,ios,pc")
    private String clientType;

    /**
     * 发送请求的环境 : test, prod
     */
    @Schema(description = "发送请求的环境 : test, prod")
    private String requestEnv;

    /**
     * 设备类型 : xiaomi,oppo,huawei
     */
    @Schema(description = "设备类型 : xiaomi,oppo,huawei")
    private String requestDeviceType;

    /**
     * 版本号: version
     */
    @Schema(description = "版本号: version")
    private String appVersion;


    /**
     * 初始化数据
     */

    public static LoginUserInfo init() {
        return new LoginUserInfo();
    }

}
