package com.security.annotation;


import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Date 2024-01-11 11:43
 * <p>
 * 资源服务注解
 */
@Documented
@Inherited
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@EnableGlobalMethodSecurity(prePostEnabled = true)
//@Import({ XiaomudingResourceServerAutoConfiguration.class, XiaomudingResourceServerConfiguration.class,
//		XiaomudingFeignClientConfiguration.class })
public @interface EnableWOEResourceServer {

}
