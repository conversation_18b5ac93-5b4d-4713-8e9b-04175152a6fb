package com.security.annotation;



import com.security.enums.BusinessLogModelEnum;
import com.security.enums.OperateTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Date 2024-01-11 11:43
 * @PackageName:com.xmd.xiaomuding.customize.core.annotation
 * @ClassName: BusinessLog
 * @Description: 业务日志
 * @Version 1.0
 */
@Target({ElementType.PARAMETER, ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface BusinessLog {

    /**
     * 应用集合  如果由此参数 则为白名单 非配置不添加日志 若此参数为空 则所有的日志都添加
     */
    int[] applicationList() default {};

    /**
     * 所属业务模块
     */
    BusinessLogModelEnum model();

    /**
     * 操作类型
     */
    OperateTypeEnum operateType() default OperateTypeEnum.OTHER;

    /**
     * 备注
     */
    String notes() default "";
}
